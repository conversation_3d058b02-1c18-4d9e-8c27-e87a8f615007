{% extends 'z115_app/cap_vat_tu_khu_a.html' %}

{% block sub_content %}
    <div class="container mt-4 text-center">
        <h2 style="font-weight: bold; color: #008000;">DUYỆT PHIẾU SỐ {{ phieu.so_phieu }}</h2>
        <p>Đơn vị nhận: {{ phieu.don_vi_nhan }} | Kho: {{ phieu.kho }} | Ngày tạo: {{ phieu.ngay_tao|date:'d/m/Y H:i' }}</p>

        <form method="post">
            {% csrf_token %}
            <table class="table mt-4">
                <thead style="background-color: #0000FF; color: white; font-weight: bold;">
                    <tr>
                        <th>STT</th>
                        <th>M<PERSON>c đích</th>
                        <th>Tên vật tư</th>
                        <th>ĐVT</th>
                        <th>Số lượng yêu cầu</th>
                        <th><PERSON><PERSON> lượng duyệt</th>
                        <th><PERSON><PERSON> lượng thự<PERSON> cấp</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vat_tu in vat_tus %}
                        <tr>
                            <td>{{ vat_tu.stt }}</td>
                            <td>{{ vat_tu.muc_dich_su_dung }}</td>
                            <td>{{ vat_tu.ten_vat_tu }}</td>
                            <td>{{ vat_tu.don_vi_tinh }}</td>
                            <td>{{ vat_tu.so_luong_yeu_cau }}</td>
                            <td>{{ vat_tu.so_luong_duyet|default:'Chưa duyệt' }}</td>
                            <td>{{ vat_tu.so_luong_thuc_cap|default:'' }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            <input type="hidden" name="phieu_id" value="{{ phieu.id }}">
            <button type="submit" name="duyet_phieu" class="btn btn-success">ĐÃ DUYỆT</button>
        </form>
    </div>
{% endblock %}