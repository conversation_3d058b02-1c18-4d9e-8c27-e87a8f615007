from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('bao-kiem-vat-tu/', views.bao_kiem_vat_tu, name='bao_kiem_vat_tu'),
    path('bao-kiem-vat-tu-kiem-tra/', views.bao_kiem_vat_tu_kiem_tra, name='bao_kiem_vat_tu_kiem_tra'),
    path('bao-kiem-vat-tu-da-kiem/', views.bao_kiem_vat_tu_da_kiem, name='bao_kiem_vat_tu_da_kiem'),
    path('bao-cao-ton-kho-b3/', views.bao_cao_ton_kho_b3, name='bao_cao_ton_kho_b3'),
    path('XNT-kho-xi-nghiep-1/', views.XNT_kho_xi_nghiep_1, name='XNT_kho_xi_nghiep_1'),
    path('XNT-kho-xi-nghiep-1/vat-tu-pvsx/', views.vat_tu_pvsx, name='vat_tu_pvsx'),
    path('XNT-kho-xi-nghiep-1/thanh-pham/', views.thanh_pham, name='thanh_pham'),
    path('cap-vat-tu-khu-a/', views.cap_vat_tu_khu_a, name='cap_vat_tu_khu_a'),
    path('nhap-kho-thanh-pham-khu-a/', views.nhap_kho_thanh_pham_khu_a, name='nhap_kho_thanh_pham_khu_a'),
    path('quan-ly-nguoi-dung/', views.quan_ly_nguoi_dung, name='quan_ly_nguoi_dung'),
    path('tao-phieu/', views.tao_phieu, name='tao_phieu'),
    path('danh-sach-cho-dmv-duyet/', views.danh_sach_cho_dmv_duyet, name='danh_sach_cho_dmv_duyet'),
    path('duyet-phieu-dmv/<int:phieu_id>/', views.danh_sach_cho_dmv_duyet, name='duyet_phieu_dmv'),
    path('danh-sach-cho-ch-duyet/', views.danh_sach_cho_ch_duyet, name='danh_sach_cho_ch_duyet'),
    path('danh-sach-phieu-dmv-duyet/', views.danh_sach_phieu_dmv_duyet, name='danh_sach_phieu_dmv_duyet'),
    path('danh-sach-phieu-ch-duyet/', views.danh_sach_phieu_ch_duyet, name='danh_sach_phieu_ch_duyet'),
    path('quan-ly-nhom-user/', views.quan_ly_nhom_user, name='quan_ly_nhom_user'),
    # Thêm các route mới
    path('get_phieu_details_dmv/<int:phieu_id>/', views.get_phieu_details_dmv, name='get_phieu_details_dmv'),
    path('save_duyet_and_transfer/', views.save_duyet_and_transfer, name='save_duyet_and_transfer'),
    path('save_duyet_ch_ajax/', views.save_duyet_ch_ajax, name='save_duyet_ch_ajax'),
    path('huy_phieu_ch/<int:phieu_id>/', views.huy_phieu_ch, name='huy_phieu_ch'),
    path('get_phieu_details_ch/<int:phieu_id>/', views.get_phieu_details_ch, name='get_phieu_details_ch'),
    path('get_phieu_details_dmv_duyet/<int:phieu_id>/', views.get_phieu_details_dmv_duyet, name='get_phieu_details_dmv_duyet'),
    path('api/phieu_dmv_da_duyet/<int:phieu_id>/', views.chi_tiet_phieu_dmv_da_duyet, name='chi_tiet_phieu_dmv_da_duyet'),
    path('api/phieu_ch_da_duyet/<int:phieu_id>/', views.chi_tiet_phieu_ch_da_duyet, name='chi_tiet_phieu_ch_da_duyet'),
    path('in_phieu/<int:phieu_id>/', views.in_phieu_view, name='in_phieu'),
    # Dashboard API endpoints
    path('api/update-phieu-status/', views.update_phieu_status, name='update_phieu_status'),
    path('api/phieu-details/<int:phieu_id>/', views.phieu_details_api, name='phieu_details_api'),
]