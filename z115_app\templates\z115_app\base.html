{% load static %}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}NHÀ MÁY Z115{% endblock %}</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'adminlte/plugins/fontawesome-free/css/all.min.css' %}">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="{% static 'adminlte/dist/css/adminlte.min.css' %}">
    <!-- overlayScrollbars -->
    <link rel="stylesheet" href="{% static 'adminlte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="{% static 'images/logo.png' %}" alt="Z115Logo" height="60" width="60">
  </div>

  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="{% url 'home' %}" class="nav-link">Trang chủ</a>
      </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      {% if user.is_authenticated %}
      <!-- User Account Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-user"></i>
          {{ user.username }}
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <span class="dropdown-item dropdown-header">{{ user.get_full_name|default:user.username }}</span>
          <div class="dropdown-divider"></div>
          <a href="{% url 'logout' %}" class="dropdown-item">
            <i class="fas fa-sign-out-alt mr-2"></i> Đăng xuất
          </a>
        </div>
      </li>
      {% endif %}
    </ul>
  </nav>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="{% url 'home' %}" class="brand-link">
      <img src="{% static 'images/logo.png' %}" alt="Z115 Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
      <span class="brand-text font-weight-light">NHÀ MÁY Z115</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Sidebar user panel (optional) -->
      {% if user.is_authenticated %}
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <img src="{% static 'adminlte/dist/img/user2-160x160.jpg' %}" class="img-circle elevation-2" alt="User Image">
        </div>
        <div class="info">
          <a href="#" class="d-block">{{ user.get_full_name|default:user.username }}</a>
        </div>
      </div>
      {% endif %}

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Dashboard -->
          <li class="nav-item">
            <a href="{% url 'home' %}" class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}">
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>Dashboard</p>
            </a>
          </li>

          <!-- Báo kiểm vật tư -->
          {% if perms.z115_app.view_baokiem %}
          <li class="nav-item {% if 'bao_kiem' in request.resolver_match.url_name %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'bao_kiem' in request.resolver_match.url_name %}active{% endif %}">
              <i class="nav-icon fas fa-clipboard-check"></i>
              <p>
                Báo kiểm vật tư
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'bao_kiem_vat_tu' %}" class="nav-link {% if request.resolver_match.url_name == 'bao_kiem_vat_tu' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Báo kiểm vật tư PVSX</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'bao_kiem_vat_tu_kiem_tra' %}" class="nav-link {% if request.resolver_match.url_name == 'bao_kiem_vat_tu_kiem_tra' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Báo kiểm kiểm tra</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'bao_kiem_vat_tu_da_kiem' %}" class="nav-link {% if request.resolver_match.url_name == 'bao_kiem_vat_tu_da_kiem' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Báo kiểm đã kiểm</p>
                </a>
              </li>
            </ul>
          </li>
          {% endif %}

          <!-- Báo cáo kho -->
          {% if perms.z115_app.view_kehoach %}
          <li class="nav-item">
            <a href="{% url 'bao_cao_ton_kho_b3' %}" class="nav-link {% if request.resolver_match.url_name == 'bao_cao_ton_kho_b3' %}active{% endif %}">
              <i class="nav-icon fas fa-chart-bar"></i>
              <p>Báo cáo tồn kho B3</p>
            </a>
          </li>
          {% endif %}

          <!-- NXT Kho xí nghiệp -->
          {% if perms.z115_app.view_thuocno %}
          <li class="nav-item {% if 'vat_tu_pvsx' in request.resolver_match.url_name or 'thanh_pham' in request.resolver_match.url_name or 'XNT_kho_xi_nghiep_1' in request.resolver_match.url_name %}menu-open{% endif %}">
            <a href="{% url 'XNT_kho_xi_nghiep_1' %}" class="nav-link {% if 'XNT_kho_xi_nghiep_1' in request.resolver_match.url_name or 'vat_tu_pvsx' in request.resolver_match.url_name or 'thanh_pham' in request.resolver_match.url_name %}active{% endif %}">
              <i class="nav-icon fas fa-bomb"></i>
              <p>
                NXT Kho xí nghiệp I
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'vat_tu_pvsx' %}" class="nav-link {% if request.resolver_match.url_name == 'vat_tu_pvsx' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Vật tư PVSX</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'thanh_pham' %}" class="nav-link {% if request.resolver_match.url_name == 'thanh_pham' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Thành phẩm</p>
                </a>
              </li>
            </ul>
          </li>
          {% endif %}

          <!-- Cấp vật tư -->
          {% if perms.z115_app.view_cap_vat_tu_khu_a %}
          <li class="nav-item">
            <a href="{% url 'cap_vat_tu_khu_a' %}" class="nav-link {% if request.resolver_match.url_name == 'cap_vat_tu_khu_a' %}active{% endif %}">
              <i class="nav-icon fas fa-truck-loading"></i>
              <p>Cấp vật tư PVSX khu A</p>
            </a>
          </li>
          {% endif %}

          <!-- Nhập kho -->
          {% if perms.z115_app.view_nhap_kho_thanh_pham_khu_a %}
          <li class="nav-item">
            <a href="{% url 'nhap_kho_thanh_pham_khu_a' %}" class="nav-link {% if request.resolver_match.url_name == 'nhap_kho_thanh_pham_khu_a' %}active{% endif %}">
              <i class="nav-icon fas fa-warehouse"></i>
              <p>Nhập kho thành phẩm khu A</p>
            </a>
          </li>
          {% endif %}

          <!-- Quản lý phiếu -->
          <li class="nav-header">QUẢN LÝ PHIẾU</li>
          <li class="nav-item">
            <a href="{% url 'tao_phieu' %}" class="nav-link {% if request.resolver_match.url_name == 'tao_phieu' %}active{% endif %}">
              <i class="nav-icon fas fa-plus-circle"></i>
              <p>Tạo phiếu</p>
            </a>
          </li>

          <!-- Duyệt phiếu -->
          <li class="nav-item {% if 'duyet' in request.resolver_match.url_name or 'danh_sach_cho' in request.resolver_match.url_name %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'duyet' in request.resolver_match.url_name or 'danh_sach_cho' in request.resolver_match.url_name %}active{% endif %}">
              <i class="nav-icon fas fa-check-circle"></i>
              <p>
                Duyệt phiếu
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danh_sach_cho_dmv_duyet' %}" class="nav-link {% if request.resolver_match.url_name == 'danh_sach_cho_dmv_duyet' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh sách chờ DMV duyệt</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'danh_sach_cho_ch_duyet' %}" class="nav-link {% if request.resolver_match.url_name == 'danh_sach_cho_ch_duyet' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh sách chờ CH duyệt</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'danh_sach_phieu_dmv_duyet' %}" class="nav-link {% if request.resolver_match.url_name == 'danh_sach_phieu_dmv_duyet' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh sách phiếu DMV duyệt</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="{% url 'danh_sach_phieu_ch_duyet' %}" class="nav-link {% if request.resolver_match.url_name == 'danh_sach_phieu_ch_duyet' %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh sách phiếu CH duyệt</p>
                </a>
              </li>
            </ul>
          </li>

          <!-- Quản trị hệ thống -->
          {% if user.username == 'hungpc892' and user.is_superuser %}
          <li class="nav-header">QUẢN TRỊ HỆ THỐNG</li>
          <li class="nav-item">
            <a href="{% url 'quan_ly_nguoi_dung' %}" class="nav-link {% if request.resolver_match.url_name == 'quan_ly_nguoi_dung' %}active{% endif %}">
              <i class="nav-icon fas fa-users"></i>
              <p>Quản lý người dùng</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="{% url 'quan_ly_nhom_user' %}" class="nav-link {% if request.resolver_match.url_name == 'quan_ly_nhom_user' %}active{% endif %}">
              <i class="nav-icon fas fa-users-cog"></i>
              <p>Quản lý nhóm user</p>
            </a>
          </li>
          {% endif %}
        </ul>
      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">{% block page_title %}Dashboard{% endblock %}</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              {% block breadcrumb %}
              <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
              <li class="breadcrumb-item active">Dashboard</li>
              {% endblock %}
            </ol>
          </div>
        </div>
      </div>
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Messages -->
        {% if messages %}
          {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
              {{ message }}
              <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
          {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <footer class="main-footer">
    <strong>Copyright &copy; 2025 <a href="#">Trần Đình Hưng - Phòng vật tư - Nhà máy Z115</a>.</strong>
    Tổng cục CNQP.
    <div class="float-right d-none d-sm-inline-block">
      <b>Version</b> 1.0.0
    </div>
  </footer>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="{% static 'adminlte/plugins/jquery/jquery.min.js' %}"></script>
<!-- jQuery UI 1.11.4 -->
<script src="{% static 'adminlte/plugins/jquery-ui/jquery-ui.min.js' %}"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button)
</script>
<!-- Bootstrap 4 -->
<script src="{% static 'adminlte/plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
<!-- overlayScrollbars -->
<script src="{% static 'adminlte/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js' %}"></script>
<!-- AdminLTE App -->
<script src="{% static 'adminlte/dist/js/adminlte.js' %}"></script>
{% block extra_js %}{% endblock %}
</body>
</html>