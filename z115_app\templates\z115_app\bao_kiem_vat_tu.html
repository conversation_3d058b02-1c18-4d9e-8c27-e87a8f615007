{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}BÁO KIỂM VẬT TƯ{% endblock %}

{% block content %}
    <div class="container mt-4">
        <h2>BÁO KIỂM VẬT TƯ</h2>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <form method="get" action="">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="from_date">TỪ NGÀY:</label>
                    <input type="date" name="from_date" id="from_date" value="{% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="to_date">ĐẾN NGÀY:</label>
                    <input type="date" name="to_date" id="to_date" value="{% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}" class="form-control">
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" name="clear_date" value="1" class="btn btn-secondary w-100">BỎ LỌC THEO NGÀY</button>
                </div>
                <div class="col-md-4">
                    <label for="search_name">TÌM KIẾM TÊN VẬT TƯ:</label>
                    <input type="text" name="search_name" id="search_name" value="{{ search_name|default:'' }}" class="form-control">
                </div>
                <div class="col-md-4">
                    <label for="search_kho">TÌM KIẾM MÃ KHO:</label>
                    <input type="text" name="search_kho" id="search_kho" value="{{ search_kho|default:'' }}" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="filter_type">LỌC:</label>
                    <select name="filter_type" id="filter_type" class="form-control">
                        <option value="all" {% if filter_type == 'all' %}selected{% endif %}>Tất cả</option>
                        <option value="chua_kiem" {% if filter_type == 'chua_kiem' %}selected{% endif %}>Chờ kiểm</option>
                        <option value="da_kiem" {% if filter_type == 'da_kiem' %}selected{% endif %}>Đã kiểm</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" class="btn btn-primary w-100">TÌM KIẾM</button>
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" name="export_excel" value="1" class="btn btn-success w-100">XUẤT EXCEL</button>
                </div>
            </div>
        </form>

        <h4 class="mt-4">Danh sách báo kiểm</h4>
        <table class="table table-striped table-hover">
            <thead style="background-color: #0066cc; color: white;">
                <tr>
                    <th style="padding: 10px;">Ngày</th>
                    <th style="padding: 10px;">Mã VLSPP</th>
                    <th style="padding: 10px;">Mã kho</th>
                    <th style="padding: 10px;">Tên quy cách</th>
                    <th style="padding: 10px;">Dùng vào việc</th>
                    <th style="padding: 10px;">Đvt</th>
                    <th style="padding: 10px;">Số lượng</th>
                    <th style="padding: 10px;">Người báo kiểm</th>
                    <th style="padding: 10px;">Nơi để vật tư</th>
                    <th style="padding: 10px;">Số hợp cách</th>
                    <th style="padding: 10px;">Kết quả kiểm tra</th>
                    <th style="padding: 10px;">Ngày trả kết quả</th>
                    <th style="padding: 10px;">Phòng B12 ký</th>
                    <th style="padding: 10px;">Phòng B3 ký</th>
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                    <tr>
                        <td style="padding: 10px;">{{ item.ngay|date:'d/m/Y' }}</td>
                        <td style="padding: 10px;">{{ item.ma_vlspp|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.ma_kho|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.ten_quy_cach }}</td>
                        <td style="padding: 10px;">{{ item.dung_vao_viec }}</td>
                        <td style="padding: 10px;">{{ item.don_vi_tinh }}</td>
                        <td style="padding: 10px;">{{ item.so_luong|floatformat:0 }}</td>
                        <td style="padding: 10px;">{{ item.nguoi_bao_kiem }}</td>
                        <td style="padding: 10px;">{{ item.noi_de_vat_tu }}</td>
                        <td style="padding: 10px;">{{ item.so_hop_cach|default:0 }}</td>
                        <td style="padding: 10px;">{{ item.ket_qua_kiem_tra|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.ngay_tra_ket_qua|date:'d/m/Y'|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.phong_b12_ky|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.phong_b3_ky|default:'' }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">Trước</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Trước</span></li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                            <a class="page-link" href="?page={{ num }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">{{ num }}</a>
                        </li>
                    {% endfor %}
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">Tiếp</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}

        {% if request.user.username == 'PHUONGB3' %}
            <h4 class="mt-4">Quản lý dữ liệu</h4>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="excel_file">TẢI LÊN:</label>
                        <input type="file" name="excel_file" id="excel_file" accept=".xlsx, .xls" class="form-control" placeholder="CHỌN FILE">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="upload" class="btn btn-primary w-100">TẢI LÊN</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="reset_data" value="1" class="btn btn-danger w-100">RESET</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="backup_data" value="1" class="btn btn-warning w-100">SAO LƯU</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="restore_data" value="1" class="btn btn-info w-100">KHÔI PHỤC</button>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-3">
                        <label for="phuongb3_password">Mật khẩu:</label>
                        <input type="password" name="phuongb3_password" id="phuongb3_password" class="form-control">
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-3">
                        <label for="backup_date">KHÔI PHỤC:</label>
                        <select name="backup_date" id="backup_date" class="form-control">
                            {% if backup_dates %}
                                {% for date in backup_dates %}
                                    <option value="{{ date|date:'Y-m-d H:i:s' }}">{{ date|date:'d/m/Y H:i' }}</option>
                                {% endfor %}
                            {% else %}
                                <option value="">Chưa có bản sao lưu nào.</option>
                            {% endif %}
                        </select>
                    </div>
                </div>
            </form>
        {% endif %}

        {% if user.username == 'PHONGB12' %}
            <div class="text-center mt-4">
                <a href="{% url 'home' %}" class="btn btn-secondary">QUAY LẠI</a>
            </div>
        {% endif %}
    </div>
{% endblock %}