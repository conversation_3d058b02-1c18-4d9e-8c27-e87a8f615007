# views.py
import logging
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from .views.auth_views import *
from .views.bao_kiem_views import *
from .views.bao_cao_views import *
from .views.nxt_views import *
from .views.cap_vat_tu_views import *
from .views.nhap_kho_views import *
from .views.quan_ly_views import *

logger = logging.getLogger(__name__)

@login_required
def home(request):
    logger.debug(f"User accessing home: {request.user.username}")
    if request.user.username == 'PHONGB12':
        return render(request, 'z115_app/phongb12_home.html', {'username': request.user.username})
    return render(request, 'z115_app/home.html', {'username': request.user.username})