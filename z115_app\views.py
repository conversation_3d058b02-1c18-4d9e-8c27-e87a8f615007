# views.py
import logging
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.db.models import Count
from .models import <PERSON>eu, PhieuVatTu, BaoKiemVatTu, TonKhoB3
from .views.auth_views import *
from .views.bao_kiem_views import *
from .views.bao_cao_views import *
from .views.nxt_views import *
from .views.cap_vat_tu_views import *
from .views.nhap_kho_views import *
from .views.quan_ly_views import *

logger = logging.getLogger(__name__)

@login_required
def home(request):
    logger.debug(f"User accessing home: {request.user.username}")
    if request.user.username == 'PHONGB12':
        return render(request, 'z115_app/phongb12_home.html', {'username': request.user.username})

    # Thống kê phiếu theo trạng thái
    phieu_stats = Phieu.objects.values('trang_thai').annotate(count=Count('id')).order_by('trang_thai')

    # Tổng số phiếu
    total_phieu = Phieu.objects.count()

    # <PERSON>ếu cần xử lý (chờ duyệt)
    phieu_cho_duyet = Phieu.objects.filter(
        trang_thai__in=['CHO_DMV_DUYET', 'CHO_CH_DUYET']
    ).count()

    # Phiếu đã hoàn thành
    phieu_hoan_thanh = Phieu.objects.filter(trang_thai='DA_DUYET_CH').count()

    # Phiếu bị hủy
    phieu_huy = Phieu.objects.filter(trang_thai='CH_HUY_PHIEU').count()

    # Danh sách phiếu gần đây (10 phiếu mới nhất)
    phieu_gan_day = Phieu.objects.select_related().order_by('-ngay_tao')[:10]

    # Thống kê báo kiểm vật tư
    total_bao_kiem = BaoKiemVatTu.objects.count()

    # Thống kê tồn kho
    total_ton_kho = TonKhoB3.objects.count()

    context = {
        'username': request.user.username,
        'phieu_stats': phieu_stats,
        'total_phieu': total_phieu,
        'phieu_cho_duyet': phieu_cho_duyet,
        'phieu_hoan_thanh': phieu_hoan_thanh,
        'phieu_huy': phieu_huy,
        'phieu_gan_day': phieu_gan_day,
        'total_bao_kiem': total_bao_kiem,
        'total_ton_kho': total_ton_kho,
    }

    return render(request, 'z115_app/home.html', context)