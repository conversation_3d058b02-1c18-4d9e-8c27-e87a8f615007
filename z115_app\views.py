# views.py
import logging
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.db.models import Count
from .models import <PERSON>eu, PhieuVatTu, BaoKiemVatTu, TonKhoB3
from .views.auth_views import *
from .views.bao_kiem_views import *
from .views.bao_cao_views import *
from .views.nxt_views import *
from .views.cap_vat_tu_views import *
from .views.nhap_kho_views import *
from .views.quan_ly_views import *

logger = logging.getLogger(__name__)

@login_required
def home(request):
    logger.debug(f"User accessing home: {request.user.username}")
    if request.user.username == 'PHONGB12':
        return render(request, 'z115_app/phongb12_home.html', {'username': request.user.username})

    # Thống kê phiếu theo trạng thái
    phieu_stats = Phieu.objects.values('trang_thai').annotate(count=Count('id')).order_by('trang_thai')

    # Tổng số phiếu
    total_phieu = Phieu.objects.count()

    # <PERSON>ếu cần xử lý (chờ duyệt)
    phieu_cho_duyet = Phieu.objects.filter(
        trang_thai__in=['CHO_DMV_DUYET', 'CHO_CH_DUYET']
    ).count()

    # Phiếu đã hoàn thành
    phieu_hoan_thanh = Phieu.objects.filter(trang_thai='DA_DUYET_CH').count()

    # Phiếu bị hủy
    phieu_huy = Phieu.objects.filter(trang_thai='CH_HUY_PHIEU').count()

    # Danh sách phiếu gần đây (10 phiếu mới nhất)
    phieu_gan_day = Phieu.objects.select_related().order_by('-ngay_tao')[:10]

    # Thống kê báo kiểm vật tư
    total_bao_kiem = BaoKiemVatTu.objects.count()

    # Thống kê tồn kho
    total_ton_kho = TonKhoB3.objects.count()

    context = {
        'username': request.user.username,
        'phieu_stats': phieu_stats,
        'total_phieu': total_phieu,
        'phieu_cho_duyet': phieu_cho_duyet,
        'phieu_hoan_thanh': phieu_hoan_thanh,
        'phieu_huy': phieu_huy,
        'phieu_gan_day': phieu_gan_day,
        'total_bao_kiem': total_bao_kiem,
        'total_ton_kho': total_ton_kho,
    }

    return render(request, 'z115_app/home.html', context)

# API endpoints cho dashboard
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
import json

@login_required
@require_POST
def update_phieu_status(request):
    """API endpoint để cập nhật trạng thái phiếu"""
    try:
        phieu_id = request.POST.get('phieu_id')
        new_status = request.POST.get('trang_thai')
        ghi_chu = request.POST.get('ghi_chu', '')

        if not phieu_id or not new_status:
            return JsonResponse({'success': False, 'message': 'Thiếu thông tin bắt buộc'})

        # Kiểm tra phiếu tồn tại
        try:
            phieu = Phieu.objects.get(id=phieu_id)
        except Phieu.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Không tìm thấy phiếu'})

        # Kiểm tra quyền (có thể thêm logic phức tạp hơn)
        if not request.user.is_superuser:
            # Chỉ cho phép một số trạng thái nhất định
            allowed_statuses = ['TAO_PHIEU', 'CHO_DMV_DUYET']
            if new_status not in allowed_statuses:
                return JsonResponse({'success': False, 'message': 'Bạn không có quyền thay đổi trạng thái này'})

        # Cập nhật trạng thái
        old_status = phieu.trang_thai
        phieu.trang_thai = new_status
        phieu.save()

        # Tạo log duyệt nếu cần
        if ghi_chu:
            from .models import LogDuyet
            from django.utils import timezone
            LogDuyet.objects.create(
                phieu=phieu,
                nguoi_duyet=request.user.username,
                thoi_gian=timezone.now(),
                vai_tro='DMV' if 'DMV' in new_status else 'CH',
                ghi_chu=ghi_chu
            )

        return JsonResponse({
            'success': True,
            'message': f'Đã cập nhật trạng thái từ "{old_status}" thành "{new_status}"'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})

@login_required
def phieu_details_api(request, phieu_id):
    """API endpoint để lấy chi tiết phiếu"""
    try:
        phieu = Phieu.objects.get(id=phieu_id)
        vat_tu_list = PhieuVatTu.objects.filter(phieu=phieu)

        html_content = f"""
        <div class="row">
            <div class="col-md-6">
                <h5>Thông tin phiếu</h5>
                <table class="table table-sm">
                    <tr><td><strong>Số phiếu:</strong></td><td>{phieu.so_phieu}</td></tr>
                    <tr><td><strong>Đơn vị nhận:</strong></td><td>{phieu.don_vi_nhan}</td></tr>
                    <tr><td><strong>Kho:</strong></td><td>{phieu.kho}</td></tr>
                    <tr><td><strong>Ngày tạo:</strong></td><td>{phieu.ngay_tao.strftime('%d/%m/%Y %H:%M')}</td></tr>
                    <tr><td><strong>Tài khoản tạo:</strong></td><td>{phieu.tai_khoan_tao}</td></tr>
                    <tr><td><strong>Trạng thái:</strong></td><td>
                        <span class="badge badge-{'success' if phieu.trang_thai == 'DA_DUYET_CH' else 'warning' if 'CHO' in phieu.trang_thai else 'secondary'}">{phieu.get_trang_thai_display()}</span>
                    </td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Danh sách vật tư</h5>
                <div style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Tên vật tư</th>
                                <th>ĐVT</th>
                                <th>SL yêu cầu</th>
                            </tr>
                        </thead>
                        <tbody>
        """

        for vt in vat_tu_list:
            html_content += f"""
                            <tr>
                                <td>{vt.stt or ''}</td>
                                <td>{vt.ten_vat_tu}</td>
                                <td>{vt.don_vi_tinh}</td>
                                <td>{vt.so_luong_yeu_cau}</td>
                            </tr>
            """

        html_content += """
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        """

        return JsonResponse({'success': True, 'html': html_content})

    except Phieu.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Không tìm thấy phiếu'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})