# z115_app/views.py - PHIÊN BẢN HOÀN CHỈNH
import logging
import os
import shutil
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse, HttpResponseForbidden
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.contrib import messages
import pandas as pd
from django.contrib.auth.models import User, Group
from ..models import Phieu, PhieuVatTu, LogDuyet
from django.db.models import Max, F
from django.db.models.functions import Trim
import io
from django.utils import timezone
from django.views.decorators.http import require_POST

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def cap_vat_tu_khu_a(request):
    context = {'is_super_admin': request.user.username == 'hungpc892'}
    return render(request, 'z115_app/cap_vat_tu_khu_a.html', context)

@login_required
def tao_phieu(request):
    logger.debug(f"Checking permissions for user: {request.user.username}, groups: {request.user.groups.values_list('name', flat=True)}")
    if not request.user.groups.filter(name='Perm_tao_phieu').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập trang này!")

    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    search_ten_vat_tu = request.GET.get('search_ten_vat_tu', '').strip()
    search_kho = request.GET.get('search_kho', '').strip()
    search_muc_dich = request.GET.get('search_muc_dich', '').strip()
    search_don_vi_nhan = request.GET.get('search_don_vi_nhan', '').strip()
    search_so_phieu = request.GET.get('search_so_phieu', '').strip()
    search_trang_thai = request.GET.get('search_trang_thai', '')
    export_excel = request.GET.get('export_excel')

    if request.method == "POST":
        if request.FILES.get('excel_file'):
            excel_file = request.FILES.get('excel_file')
            don_vi_nhan = request.POST.get('don_vi_nhan')
            kho = request.POST.get('kho')

            if not don_vi_nhan or not kho:
                messages.error(request, "Vui lòng điền đầy đủ thông tin: Đơn vị nhận và Kho.")
                return redirect('tao_phieu')
            
            try:
                df = pd.read_excel(excel_file, sheet_name=0, skiprows=0)
                df.columns = [col.strip() for col in df.columns]
                required_columns = ['STT', 'MỤC ĐÍCH SỬ DỤNG', 'TÊN VẬT TƯ VÀ QUY CÁCH', 'ĐVT', 'SỐ LƯỢNG YÊU CẦU']
                if not all(col in df.columns for col in required_columns):
                    raise ValueError(f"File Excel thiếu cột. Các cột yêu cầu: {', '.join(required_columns)}")

                last_phieu = Phieu.objects.aggregate(max_so_phieu=Max('so_phieu'))
                phieu_so = (last_phieu['max_so_phieu'] or 0) + 1

                phieu = Phieu.objects.create(
                    so_phieu=phieu_so,
                    don_vi_nhan=don_vi_nhan,
                    kho=kho,
                    ngay_tao=datetime.now(),
                    tai_khoan_tao=request.user.username,
                    trang_thai='CHO_DMV_DUYET'
                )

                vat_tu_list = []
                for index, row in df.iterrows():
                    if pd.isna(row['STT']):
                        continue
                    muc_dich = str(row['MỤC ĐÍCH SỬ DỤNG']).strip()
                    ten_vat_tu = str(row['TÊN VẬT TƯ VÀ QUY CÁCH']).strip()
                    logger.debug(f"Thêm dữ liệu: Mục đích={muc_dich}, Tên vật tư={ten_vat_tu}")
                    vat_tu_list.append(PhieuVatTu(
                        phieu=phieu,
                        stt=int(row['STT']) if pd.notna(row['STT']) else None,
                        muc_dich_su_dung=muc_dich,
                        ten_vat_tu=ten_vat_tu,
                        don_vi_tinh=str(row['ĐVT']).strip(),
                        so_luong_yeu_cau=float(row['SỐ LƯỢNG YÊU CẦU']) if pd.notna(row['SỐ LƯỢNG YÊU CẦU']) else 0.0
                    ))
                
                PhieuVatTu.objects.bulk_create(vat_tu_list)

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'success': True, 'message': f'BẠN ĐÃ TẠO PHIẾU SỐ {phieu_so} THÀNH CÔNG!'})
                messages.success(request, f"BẠN ĐÃ TẠO PHIẾU SỐ {phieu_so} THÀNH CÔNG!")
                return redirect('tao_phieu')

            except Exception as e:
                logger.error(f"Lỗi khi xử lý file Excel: {e}")
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'success': False, 'error': f'Lỗi khi xử lý file Excel: {str(e)}. Vui lòng kiểm tra lại định dạng và nội dung file.'})
                messages.error(request, f"Lỗi khi xử lý file Excel: {str(e)}. Vui lòng kiểm tra lại định dạng và nội dung file.")
                return redirect('tao_phieu')

        if request.POST.get('reset_data') and request.user.username == 'hungpc892':
            password = request.POST.get('super_password')
            if password == '300892':
                Phieu.objects.all().delete()
                PhieuVatTu.objects.all().delete()
                LogDuyet.objects.all().delete()
                messages.success(request, "Dữ liệu đã được RESET thành công! Số phiếu sẽ bắt đầu lại từ 1.")
                return redirect('tao_phieu')
            else:
                messages.error(request, "Mật khẩu Super không đúng!")

        if request.POST.get('backup_data') and request.user.username == 'hungpc892':
            password = request.POST.get('super_password')
            if password == '300892':
                backup_dir = r"C:\\Users\\<USER>\\z115_project\\saoluu"
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                backup_file = os.path.join(backup_dir, f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip")
                shutil.make_archive(os.path.splitext(backup_file)[0], 'zip', os.path.dirname(__file__))
                messages.success(request, f"Dữ liệu đã được sao lưu thành công tại {backup_file}")
                return redirect('tao_phieu')
            else:
                messages.error(request, "Mật khẩu Super không đúng!")

    all_phieus = Phieu.objects.prefetch_related('phieuvattu_set').order_by('-ngay_tao')

    if from_date:
        all_phieus = all_phieus.filter(ngay_tao__gte=datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        all_phieus = all_phieus.filter(ngay_tao__lte=datetime.strptime(to_date, '%Y-%m-%d') + pd.Timedelta(days=1))
    if search_ten_vat_tu:
        all_phieus = all_phieus.annotate(ten_vt_trim=Trim(F('phieuvattu__ten_vat_tu'))).filter(ten_vt_trim__icontains=search_ten_vat_tu)
    if search_kho:
        all_phieus = all_phieus.filter(kho__iexact=search_kho)
    if search_muc_dich:
        all_phieus = all_phieus.annotate(muc_dich_trim=Trim(F('phieuvattu__muc_dich_su_dung'))).filter(muc_dich_trim__icontains=search_muc_dich)
    if search_don_vi_nhan:
        all_phieus = all_phieus.filter(don_vi_nhan__icontains=search_don_vi_nhan)
    if search_so_phieu:
        all_phieus = all_phieus.filter(so_phieu__exact=search_so_phieu)
    if search_trang_thai:
        all_phieus = all_phieus.filter(trang_thai=search_trang_thai)
    if request.GET.get('clear_date'):
        from_date = to_date = None

    paginator = Paginator(all_phieus, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if export_excel:
        output = io.BytesIO()
        phieu_data = Phieu.objects.all().values('id', 'so_phieu', 'ngay_tao', 'tai_khoan_tao', 'don_vi_nhan', 'kho', 'trang_thai')
        vattu_data = PhieuVatTu.objects.all().values('phieu_id', 'muc_dich_su_dung', 'ten_vat_tu', 'don_vi_tinh', 'so_luong_yeu_cau', 'so_luong_duyet')
        df_phieu = pd.DataFrame(phieu_data)
        df_vattu = pd.DataFrame(vattu_data)
        if not df_phieu.empty and not df_vattu.empty:
            df_merged = df_phieu.merge(df_vattu, left_on='id', right_on='phieu_id', how='left')
            if 'ngay_tao' in df_merged.columns:
                df_merged['ngay_tao'] = df_merged['ngay_tao'].apply(lambda x: x.replace(tzinfo=None) if pd.notna(x) else x)
            df_merged = df_merged[['so_phieu', 'ngay_tao', 'tai_khoan_tao', 'don_vi_nhan', 'kho', 'muc_dich_su_dung', 'ten_vat_tu', 'don_vi_tinh', 'so_luong_yeu_cau', 'so_luong_duyet', 'trang_thai']]
        else:
            df_merged = pd.DataFrame(columns=['so_phieu', 'ngay_tao', 'tai_khoan_tao', 'don_vi_nhan', 'kho', 'muc_dich_su_dung', 'ten_vat_tu', 'don_vi_tinh', 'so_luong_yeu_cau', 'so_luong_duyet', 'trang_thai'])
        df_merged.to_excel(output, index=False)
        output.seek(0)
        response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=phieu_cap_vat_tu.xlsx'
        return response

    next_so_phieu = (Phieu.objects.aggregate(max_so_phieu=Max('so_phieu'))['max_so_phieu'] or 0) + 1

    context = {
        'next_so_phieu': next_so_phieu,
        'all_phieus': page_obj,
        'is_super_admin': request.user.username == 'hungpc892',
        'from_date': from_date,
        'to_date': to_date,
        'search_ten_vat_tu': search_ten_vat_tu,
        'search_kho': search_kho,
        'search_muc_dich': search_muc_dich,
        'search_don_vi_nhan': search_don_vi_nhan,
        'search_so_phieu': search_so_phieu,
        'search_trang_thai': search_trang_thai,
        'page_obj': page_obj
    }
    return render(request, 'z115_app/tao_phieu.html', context)

@login_required
def danh_sach_cho_dmv_duyet(request):
    if not request.user.groups.filter(name='Perm_danh_sach_cho_dmv_duyet').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    if request.method == "POST" and 'duyet_phieu' in request.POST:
        if not request.user.groups.filter(name='Perm_duyet_phieu_dmv').exists() and request.user.username != 'hungpc892':
            return HttpResponseForbidden("Bạn không có quyền duyệt phiếu ĐMV!")
        phieu_id = request.POST.get('phieu_id')
        phieu = Phieu.objects.get(id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
        for vat_tu in vat_tus:
            so_luong_duyet = float(request.POST.get(f'so_luong_duyet_{vat_tu.id}')) if request.POST.get(f'so_luong_duyet_{vat_tu.id}') else None
            vat_tu.so_luong_duyet = so_luong_duyet
            vat_tu.save()
        log = LogDuyet.objects.create(
            phieu=phieu,
            nguoi_duyet=request.user.username,
            thoi_gian=datetime.now(),
            vai_tro='DMV',
            ghi_chu='Đã ký'
        )
        phieu.trang_thai = 'CHO_CH_DUYET'
        phieu.save()
        messages.success(request, f"PHIẾU SỐ {phieu.so_phieu} ĐÃ DUYỆT BỞI ĐMV!")
        return redirect('danh_sach_cho_ch_duyet')
    phieus = Phieu.objects.filter(trang_thai='CHO_DMV_DUYET')
    context = {'phieus': phieus, 'is_super_admin': request.user.username == 'hungpc892'}
    return render(request, 'z115_app/danh_sach_cho_dmv_duyet.html', context)

@login_required
def danh_sach_cho_ch_duyet(request):
    allowed_usernames = ['hungpc892', 'KIENTPB3', 'THAOPPB3']
    if not request.user.groups.filter(name='Perm_duyet_phieu_ch').exists() and request.user.username not in allowed_usernames:
    	return JsonResponse({'success': False, 'error': 'Bạn không có quyền duyệt phiếu Chỉ huy!'})

    if request.method == "POST" and (request.POST.get('duyet_phieu') or request.headers.get('X-Requested-With') == 'XMLHttpRequest'):
        if not request.user.groups.filter(name='Perm_duyet_phieu_ch').exists() and request.user.username != 'hungpc892':
            return JsonResponse({'success': False, 'error': 'Bạn không có quyền duyệt phiếu Chỉ huy!'})

        phieu_id = request.POST.get('phieu_id')
        phieu = get_object_or_404(Phieu, id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)

        for vat_tu in vat_tus:
            vat_tu.so_luong_thuc_cap = vat_tu.so_luong_duyet
            vat_tu.save()

        LogDuyet.objects.create(
            phieu=phieu,
            nguoi_duyet=request.user.username,
            thoi_gian=datetime.now(),
            vai_tro='CH',
            ghi_chu='Đã ký'
        )

        phieu.trang_thai = 'DA_DUYET_CH'
        phieu.save()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True})
        else:
            messages.success(request, f"PHIẾU SỐ {phieu.so_phieu} ĐÃ DUYỆT BỞI CHỈ HUY!")
            return redirect('danh_sach_phieu_ch_duyet')

    phieus = Phieu.objects.filter(trang_thai='CHO_CH_DUYET')

    if 'phieu_id' in request.GET:
        phieu_id = request.GET.get('phieu_id')
        phieu = get_object_or_404(Phieu, id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
        context = {'phieu': phieu, 'vat_tus': vat_tus, 'is_super_admin': request.user.username == 'hungpc892'}
        return render(request, 'z115_app/duyet_phieu_ch.html', context)

    context = {'phieus': phieus, 'is_super_admin': request.user.username == 'hungpc892'}
    return render(request, 'z115_app/danh_sach_cho_ch_duyet.html', context)

@login_required
def danh_sach_phieu_dmv_duyet(request):
    if not request.user.groups.filter(name='Perm_danh_sach_phieu_dmv_duyet').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    phieus = Phieu.objects.filter(trang_thai__in=['CHO_CH_DUYET', 'DA_DUYET_CH'])
    context = {'phieus': phieus, 'is_super_admin': request.user.username == 'hungpc892'}
    return render(request, 'z115_app/danh_sach_phieu_dmv_duyet.html', context)

@login_required
def danh_sach_phieu_ch_duyet(request):
    if not request.user.groups.filter(name='Perm_danh_sach_phieu_ch_duyet').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    phieus = Phieu.objects.filter(trang_thai='DA_DUYET_CH')
    context = {'phieus': phieus, 'is_super_admin': request.user.username == 'hungpc892'}
    return render(request, 'z115_app/danh_sach_phieu_ch_duyet.html', context)

@login_required
def quan_ly_nhom_user(request):
    if request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")

    permissions = [
        'tao_phieu', 'danh_sach_cho_dmv_duyet', 'danh_sach_cho_ch_duyet',
        'danh_sach_phieu_da_duyet', 'in_phieu', 'duyet_phieu_ch', 'duyet_phieu_dmv',
        'danh_sach_phieu_ch_duyet', 'danh_sach_phieu_dmv_duyet', 'chon_phieu'
    ]
    
    # Logic tự động tạo nhóm và gán quyền cho superuser
    for perm in permissions:
        group_name = f'Perm_{perm}'
        group, _ = Group.objects.get_or_create(name=group_name)
        if request.user.username == 'hungpc892' and not request.user.groups.filter(name=group_name).exists():
            request.user.groups.add(group)
            logger.debug(f"Auto-assigned permission: {group_name} to hungpc892")

    # Xử lý AJAX POST để cập nhật quyền
    if request.method == "POST":
        user_username = request.POST.get('user')
        if not user_username:
            return JsonResponse({'success': False, 'message': 'Tài khoản không hợp lệ!'})

        super_password = request.POST.get('super_password')
        if super_password != '300892':
            return JsonResponse({'success': False, 'message': 'Mật khẩu Super không đúng!'})

        try:
            target_user = User.objects.get(username=user_username)
            target_user.groups.clear() 
            
            selected_perms = request.POST.getlist('permissions')
            if selected_perms:
                for perm in selected_perms:
                    group_name = f'Perm_{perm}'
                    user_group, _ = Group.objects.get_or_create(name=group_name)
                    target_user.groups.add(user_group)
            
            return JsonResponse({'success': True, 'message': 'BẠN ĐÃ CẬP NHẬT PHÂN QUYỀN THÀNH CÔNG!'})
            
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Tài khoản không tồn tại!'})

    # Xử lý AJAX GET để lấy quyền hiện tại của một user
    if request.method == "GET" and request.headers.get('X-Requested-With') == 'XMLHttpRequest' and 'user' in request.GET:
        user_username = request.GET.get('user')
        try:
            target_user = User.objects.get(username=user_username)
            user_perms = [g.name.replace('Perm_', '') for g in target_user.groups.all()]
            return JsonResponse({'permissions': user_perms})
        except User.DoesNotExist:
            return JsonResponse({'permissions': []})

    # Render trang ban đầu
    users = User.objects.all()
    user_permissions = {}
    for user in users:
        user_permissions[user.username] = [g.name.replace('Perm_', '') for g in user.groups.all()]
        
    context = {
        'users': users,
        'permissions': permissions,
        'user_permissions': user_permissions,
        'is_super_admin': True
    }
    return render(request, 'z115_app/quan_ly_nhom_user.html', context)

@login_required
def quan_ly_nguoi_dung(request):
    if request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    
    permissions = [
        'view_baokiem', 'view_kehoach', 'view_thuocno',
        'view_cap_vat_tu_khu_a', 'view_nhap_kho_thanh_pham_khu_a'
    ]

    if request.method == "POST":
        super_password = request.POST.get('super_password')
        if super_password != '300892':
            messages.error(request, 'Mật khẩu Super không đúng!')
            return redirect('quan_ly_nguoi_dung')
        
        if 'add_user' in request.POST:
            username = request.POST.get('username')
            password = request.POST.get('password')
            if User.objects.filter(username=username).exists():
                messages.error(request, 'Tài khoản đã tồn tại!')
            else:
                user = User.objects.create_user(username=username, password=password)
                logger.debug(f"Đã tạo người dùng mới: {username}")
                messages.success(request, 'Thêm người dùng thành công!')
        elif 'update_edit' in request.POST:
            user_id = request.POST.get('user_id')
            if user_id:
                user = User.objects.get(id=user_id)
                user.set_password(request.POST.get('password'))
                user.save()
                for perm in permissions:
                    if request.POST.get(f'view_{perm}') == 'on':
                        group, _ = Group.objects.get_or_create(name=f'Perm_{perm}')
                        user.groups.add(group)
                    else:
                        group = Group.objects.filter(name=f'Perm_{perm}').first()
                        if group and user.groups.filter(name=f'Perm_{perm}').exists():
                            user.groups.remove(group)
                messages.success(request, 'Cập nhật người dùng thành công!')
            else:
                messages.error(request, 'Lỗi: Không tìm thấy ID người dùng!')
        elif 'confirm_delete' in request.POST:
            user_id = request.POST.get('delete_user_id')
            if user_id:
                User.objects.get(id=user_id).delete()
                messages.success(request, 'Xóa người dùng thành công!')
            else:
                messages.error(request, 'Lỗi: Không tìm thấy ID người dùng để xóa!')

        return redirect('quan_ly_nguoi_dung')

    edit_user_id = request.GET.get('edit_user_id')
    edit_user = User.objects.filter(id=edit_user_id).first() if edit_user_id else None
    edit_user_perms = {}
    if edit_user:
        edit_user_perms = {
            'view_baokiem': edit_user.groups.filter(name='Perm_view_baokiem').exists(),
            'view_kehoach': edit_user.groups.filter(name='Perm_view_kehoach').exists(),
            'view_thuocno': edit_user.groups.filter(name='Perm_view_thuocno').exists(),
            'view_cap_vat_tu_khu_a': edit_user.groups.filter(name='Perm_view_cap_vat_tu_khu_a').exists(),
            'view_nhap_kho_thanh_pham_khu_a': edit_user.groups.filter(name='Perm_view_nhap_kho_thanh_pham_khu_a').exists(),
        }

    users = User.objects.all()
    user_permissions = {}
    for user in users:
        user_permissions[user.username] = {
            'view_baokiem': user.groups.filter(name='Perm_view_baokiem').exists(),
            'view_kehoach': user.groups.filter(name='Perm_view_kehoach').exists(),
            'view_thuocno': user.groups.filter(name='Perm_view_thuocno').exists(),
            'view_cap_vat_tu_khu_a': user.groups.filter(name='Perm_view_cap_vat_tu_khu_a').exists(),
            'view_nhap_kho_thanh_pham_khu_a': user.groups.filter(name='Perm_view_nhap_kho_thanh_pham_khu_a').exists(),
        }

    paginator = Paginator(users, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'edit_user': edit_user,
        'edit_user_perms': edit_user_perms,
        'user_permissions': user_permissions,
        'page_obj': page_obj,
        'is_super_admin': request.user.username == 'hungpc892'
    }
    return render(request, 'z115_app/quan_ly_nguoi_dung.html', context)

@login_required
def get_phieu_details(request, phieu_id):
    phieu = get_object_or_404(Phieu, id=phieu_id)
    vat_tus = PhieuVatTu.objects.filter(phieu=phieu)

    data = {
        'phieu': {
            'so_phieu': phieu.so_phieu,
            'ngay_tao': phieu.ngay_tao.strftime('%d/%m/%Y'),
            'tai_khoan_tao': phieu.tai_khoan_tao,
            'don_vi_nhan': phieu.don_vi_nhan,
            'kho': phieu.kho,
            'trang_thai_display': phieu.get_trang_thai_display()
        },
        'vat_tus': [
            {
                'muc_dich_su_dung': vattu.muc_dich_su_dung,
                'ten_vat_tu': vattu.ten_vat_tu,
                'don_vi_tinh': vattu.don_vi_tinh,
                'so_luong_yeu_cau': vattu.so_luong_yeu_cau,
                'so_luong_duyet': vattu.so_luong_duyet if vattu.so_luong_duyet else ''
            } for vattu in vat_tus
        ]
    }
    return JsonResponse(data)

@login_required
def get_phieu_details_dmv(request, phieu_id):
    phieu = get_object_or_404(Phieu, id=phieu_id)
    vat_tus = PhieuVatTu.objects.filter(phieu=phieu)

    log_dmv = LogDuyet.objects.filter(phieu=phieu, vai_tro='DMV').order_by('-thoi_gian').first()
    log_ch = LogDuyet.objects.filter(phieu=phieu, vai_tro='CH').order_by('-thoi_gian').first()

    dmv_username = log_dmv.nguoi_duyet if log_dmv else ''
    ch_username = log_ch.nguoi_duyet if log_ch else ''

    data = {
        'phieu': {
            'so_phieu': phieu.so_phieu,
            'ngay_tao': phieu.ngay_tao.strftime('%d/%m/%Y'),
            'tai_khoan_tao': phieu.tai_khoan_tao,
            'don_vi_nhan': phieu.don_vi_nhan,
            'kho': phieu.kho,
            'trang_thai_display': phieu.get_trang_thai_display()
        },
        'vat_tus': [
            {
                'id': vattu.id,
                'muc_dich_su_dung': vattu.muc_dich_su_dung,
                'ten_vat_tu': vattu.ten_vat_tu,
                'don_vi_tinh': vattu.don_vi_tinh,
                'so_luong_yeu_cau': vattu.so_luong_yeu_cau,
                'so_luong_duyet': vattu.so_luong_duyet if vattu.so_luong_duyet else ''
            } for vattu in vat_tus
        ],
        'dmv_username': dmv_username,
        'ch_username': ch_username
    }
    return JsonResponse(data)

@login_required
def save_duyet_and_transfer(request):
    if request.method == 'POST' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        phieu_id = request.POST.get('phieu_id')
        phieu = get_object_or_404(Phieu, id=phieu_id)
        if not request.user.groups.filter(name='Perm_danh_sach_cho_dmv_duyet').exists() and request.user.username != 'hungpc892':
            return JsonResponse({'success': False, 'error': 'Bạn không có quyền thực hiện hành động này!'})

        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
        for vattu in vat_tus:
            muc_dich = request.POST.get(f'muc_dich_{vattu.id}')
            ten_vat_tu = request.POST.get(f'ten_vat_tu_{vattu.id}')
            don_vi_tinh = request.POST.get(f'don_vi_tinh_{vattu.id}')
            so_luong_duyet = request.POST.get(f'so_luong_duyet_{vattu.id}')

            if muc_dich:
                vattu.muc_dich_su_dung = muc_dich
            if ten_vat_tu:
                vattu.ten_vat_tu = ten_vat_tu
            if don_vi_tinh:
                vattu.don_vi_tinh = don_vi_tinh
            if so_luong_duyet:
                vattu.so_luong_duyet = float(so_luong_duyet)
            vattu.save()

        phieu.trang_thai = 'CHO_CH_DUYET'
        phieu.save()

        log = LogDuyet.objects.create(
            phieu=phieu,
            nguoi_duyet=request.user.username,
            thoi_gian=datetime.now(),
            vai_tro='DMV',
            ghi_chu='Đã ký'
        )

        return JsonResponse({'success': True, 'message': 'Đã lưu và chuyển duyệt thành công!'})
    return JsonResponse({'success': False, 'error': 'Yêu cầu không hợp lệ!'})

@login_required
def get_phieu_details_ch(request, phieu_id):
    if not request.user.groups.filter(name='Perm_danh_sach_cho_ch_duyet').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    
    phieu = get_object_or_404(Phieu, id=phieu_id)
    vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
    
    data = {
        'phieu': {
            'so_phieu': phieu.so_phieu,
            'ngay_tao': phieu.ngay_tao.strftime('%d/%m/%Y'),
            'tai_khoan_tao': phieu.tai_khoan_tao,
            'don_vi_nhan': phieu.don_vi_nhan,
            'kho': phieu.kho,
            'trang_thai_display': phieu.get_trang_thai_display()
        },
        'vat_tus': [
            {
                'muc_dich_su_dung': vattu.muc_dich_su_dung,
                'ten_vat_tu': vattu.ten_vat_tu,
                'don_vi_tinh': vattu.don_vi_tinh,
                'so_luong_yeu_cau': vattu.so_luong_yeu_cau,
                'so_luong_duyet': vattu.so_luong_duyet if vattu.so_luong_duyet else ''
            } for vattu in vat_tus
        ]
    }
    return JsonResponse(data)

@login_required
def get_phieu_details_dmv_duyet(request, phieu_id):
    if not request.user.groups.filter(name='Perm_danh_sach_phieu_dmv_duyet').exists() and request.user.username != 'hungpc892':
        return HttpResponseForbidden("Bạn không có quyền truy cập!")
    
    phieu = get_object_or_404(Phieu, id=phieu_id)
    vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
    
    data = {
        'phieu': {
            'so_phieu': phieu.so_phieu,
            'ngay_tao': phieu.ngay_tao.strftime('%d/%m/%Y'),
            'tai_khoan_tao': phieu.tai_khoan_tao,
            'don_vi_nhan': phieu.don_vi_nhan,
            'kho': phieu.kho,
            'trang_thai_display': phieu.get_trang_thai_display()
        },
        'vat_tus': [
            {
                'muc_dich_su_dung': vattu.muc_dich_su_dung,
                'ten_vat_tu': vattu.ten_vat_tu,
                'don_vi_tinh': vattu.don_vi_tinh,
                'so_luong_yeu_cau': vattu.so_luong_yeu_cau,
                'so_luong_duyet': vattu.so_luong_duyet if vattu.so_luong_duyet else ''
            } for vattu in vat_tus
        ]
    }
    return JsonResponse(data)
from django.views.decorators.http import require_POST

@login_required
@require_POST
def huy_phieu_ch(request, phieu_id):
    if not request.user.groups.filter(name='Perm_danh_sach_cho_ch_duyet').exists() and request.user.username != 'hungpc892':
        return JsonResponse({'success': False, 'error': 'Bạn không có quyền huỷ phiếu này!'})

    try:
        phieu = get_object_or_404(Phieu, id=phieu_id)
        phieu.trang_thai = 'CH_HUY_PHIEU'
        phieu.save()

        LogDuyet.objects.create(
            phieu=phieu,
            nguoi_duyet=request.user.username,
            thoi_gian=datetime.now(),
            vai_tro='CH',
            ghi_chu='Huỷ phiếu'
        )

        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
@login_required
def save_duyet_ch_ajax(request):
    allowed_usernames = ['hungpc892', 'KIENTPB3', 'THAOPPB3']
    if not request.user.groups.filter(name='Perm_duyet_phieu_ch').exists() and request.user.username not in allowed_usernames:
        return JsonResponse({'success': False, 'error': 'Bạn không có quyền duyệt phiếu Chỉ huy!'})
    phieu_id = request.POST.get('phieu_id')
    try:
        phieu = Phieu.objects.get(id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
        for vattu in vat_tus:
            vattu.so_luong_thuc_cap = vattu.so_luong_duyet
            vattu.save()

        LogDuyet.objects.create(
            phieu=phieu,
            nguoi_duyet=request.user.username,
            thoi_gian=datetime.now(),
            vai_tro='CH',
            ghi_chu='Đã ký'
        )
        phieu.trang_thai = 'DA_DUYET_CH'
        phieu.save()

        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def chi_tiet_phieu_dmv_da_duyet(request, phieu_id):
    try:
        phieu = Phieu.objects.get(id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)

        log_dmv = LogDuyet.objects.filter(phieu=phieu, vai_tro='DMV').order_by('-thoi_gian').first()

        data = {
            "phieu": {
                "trang_thai_display": phieu.get_trang_thai_display(),
                "nguoi_duyet": log_dmv.nguoi_duyet if log_dmv else '',
            },
            "vat_tus": [
                {
                    "muc_dich_su_dung": vt.muc_dich_su_dung,
                    "ten_vat_tu": vt.ten_vat_tu,
                    "don_vi_tinh": vt.don_vi_tinh,
                    "so_luong_yeu_cau": vt.so_luong_yeu_cau,
                    "so_luong_duyet": vt.so_luong_duyet,
                } for vt in vat_tus
            ]
        }
        return JsonResponse(data)

    except Phieu.DoesNotExist:
        return JsonResponse({"error": "Phiếu không tồn tại"}, status=404)

@login_required
def chi_tiet_phieu_ch_da_duyet(request, phieu_id):
    try:
        phieu = Phieu.objects.get(id=phieu_id)
        vat_tus = PhieuVatTu.objects.filter(phieu=phieu)

        # Lấy người duyệt theo vai trò DMV và CH
        log_dmv = LogDuyet.objects.filter(phieu=phieu, vai_tro='DMV').order_by('-thoi_gian').first()
        log_ch = LogDuyet.objects.filter(phieu=phieu, vai_tro='CH').order_by('-thoi_gian').first()

        data = {
            "phieu": {
                "trang_thai_display": phieu.get_trang_thai_display(),
                "nguoi_duyet_dmv": log_dmv.nguoi_duyet if log_dmv else '',
                "nguoi_duyet_ch": log_ch.nguoi_duyet if log_ch else '',
            },
            "vat_tus": [
                {
                    "muc_dich_su_dung": vt.muc_dich_su_dung,
                    "ten_vat_tu": vt.ten_vat_tu,
                    "don_vi_tinh": vt.don_vi_tinh,
                    "so_luong_yeu_cau": vt.so_luong_yeu_cau,
                    "so_luong_duyet": vt.so_luong_duyet,
                } for vt in vat_tus
            ]
        }
        return JsonResponse(data)

    except Phieu.DoesNotExist:
        return JsonResponse({"error": "Phiếu không tồn tại"}, status=404)
@login_required
def in_phieu_view(request, phieu_id):
    phieu = get_object_or_404(Phieu, id=phieu_id)
    vat_tus = PhieuVatTu.objects.filter(phieu=phieu)
    log_dmv = LogDuyet.objects.filter(phieu=phieu, vai_tro='DMV').order_by('-thoi_gian').first()
    log_ch = LogDuyet.objects.filter(phieu=phieu, vai_tro='CH').order_by('-thoi_gian').first()

    context = {
        'phieu': phieu,
        'vat_tus': vat_tus,
        'dmv_log': log_dmv,
        'ch_log': log_ch,
        'dmv_user': log_dmv.nguoi_duyet if log_dmv else '',
        'ch_user': log_ch.nguoi_duyet if log_ch else '',
    }
    return render(request, 'z115_app/in_phieu.html', context)