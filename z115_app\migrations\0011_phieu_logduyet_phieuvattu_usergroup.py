# Generated by Django 5.0.7 on 2025-07-04 06:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('z115_app', '0010_tonkhob3_tonkhob3backup'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Phieu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('so_phieu', models.IntegerField()),
                ('don_vi_nhan', models.CharField(max_length=50)),
                ('kho', models.CharField(max_length=10)),
                ('ngay_tao', models.DateTimeField(auto_now_add=True)),
                ('trang_thai', models.CharField(choices=[('TAO_PHIEU', 'Tạ<PERSON> phiếu'), ('CHO_DMV_DUYET', 'Chờ ĐMV duyệt'), ('CHO_CH_DUYET', 'Chờ Chỉ huy duyệt'), ('DA_DUYET', 'Đã duyệt')], max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='LogDuyet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nguoi_duyet', models.CharField(max_length=50)),
                ('thoi_gian', models.DateTimeField()),
                ('vai_tro', models.CharField(choices=[('DMV', 'Định mức viên'), ('CH', 'Chỉ huy')], max_length=10)),
                ('ghi_chu', models.CharField(max_length=100)),
                ('phieu', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.phieu')),
            ],
        ),
        migrations.CreateModel(
            name='PhieuVatTu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stt', models.IntegerField(blank=True, null=True)),
                ('muc_dich_su_dung', models.CharField(max_length=100)),
                ('ten_vat_tu', models.CharField(max_length=200)),
                ('don_vi_tinh', models.CharField(max_length=50)),
                ('so_luong_yeu_cau', models.FloatField()),
                ('so_luong_duyet', models.FloatField(blank=True, null=True)),
                ('so_luong_thuc_cap', models.FloatField(blank=True, null=True)),
                ('phieu', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.phieu')),
            ],
        ),
        migrations.CreateModel(
            name='UserGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('members', models.ManyToManyField(to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
