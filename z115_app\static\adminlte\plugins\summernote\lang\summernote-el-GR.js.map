{"version": 3, "file": "lang/summernote-el-GR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,QADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,gBAHP;AAIJC,QAAAA,KAAK,EAAE,YAJH;AAKJC,QAAAA,MAAM,EAAE,MALJ;AAMJC,QAAAA,IAAI,EAAE,eANF;AAOJC,QAAAA,aAAa,EAAE,aAPX;AAQJC,QAAAA,SAAS,EAAE,SARP;AASJC,QAAAA,WAAW,EAAE,SATT;AAUJC,QAAAA,IAAI,EAAE,SAVF;AAWJC,QAAAA,QAAQ,EAAE;AAXN,OADC;AAcPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,UAFH;AAGLC,QAAAA,UAAU,EAAE,gBAHP;AAILC,QAAAA,UAAU,EAAE,cAJP;AAKLC,QAAAA,aAAa,EAAE,aALV;AAMLC,QAAAA,UAAU,EAAE,gBANP;AAOLC,QAAAA,SAAS,EAAE,qBAPN;AAQLC,QAAAA,UAAU,EAAE,kBARP;AASLC,QAAAA,SAAS,EAAE,kBATN;AAULC,QAAAA,YAAY,EAAE,sBAVT;AAWLC,QAAAA,WAAW,EAAE,eAXR;AAYLC,QAAAA,cAAc,EAAE,oBAZX;AAaLC,QAAAA,SAAS,EAAE,eAbN;AAcLC,QAAAA,aAAa,EAAE,sBAdV;AAeLC,QAAAA,SAAS,EAAE,mBAfN;AAgBLC,QAAAA,eAAe,EAAE,oBAhBZ;AAiBLC,QAAAA,eAAe,EAAE,yBAjBZ;AAkBLC,QAAAA,oBAAoB,EAAE,uDAlBjB;AAmBLC,QAAAA,GAAG,EAAE,KAnBA;AAoBLC,QAAAA,MAAM,EAAE,UApBH;AAqBLC,QAAAA,QAAQ,EAAE;AArBL,OAdA;AAqCPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,WADF;AAEJpB,QAAAA,MAAM,EAAE,oBAFJ;AAGJqB,QAAAA,MAAM,EAAE,oBAHJ;AAIJC,QAAAA,IAAI,EAAE,uBAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJN,QAAAA,GAAG,EAAE,mDAND;AAOJO,QAAAA,eAAe,EAAE,yBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OArCC;AA+CPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGL3B,QAAAA,MAAM,EAAE,UAHH;AAILiB,QAAAA,GAAG,EAAE,KAJA;AAKLW,QAAAA,SAAS,EAAE;AALN,OA/CA;AAsDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,uBAFR;AAGLC,QAAAA,WAAW,EAAE,uBAHR;AAILC,QAAAA,UAAU,EAAE,0BAJP;AAKLC,QAAAA,WAAW,EAAE,uBALR;AAMLC,QAAAA,MAAM,EAAE,kBANH;AAOLC,QAAAA,MAAM,EAAE,iBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAtDA;AAgEPC,MAAAA,EAAE,EAAE;AACFrC,QAAAA,MAAM,EAAE;AADN,OAhEG;AAmEPsC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,UAFH;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,GAAG,EAAE,SAJA;AAKLC,QAAAA,EAAE,EAAE,YALC;AAMLC,QAAAA,EAAE,EAAE,YANC;AAOLC,QAAAA,EAAE,EAAE,YAPC;AAQLC,QAAAA,EAAE,EAAE,YARC;AASLC,QAAAA,EAAE,EAAE,YATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAnEA;AA+EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,mBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA/EA;AAmFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,UAAU,EAAE,cAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAnFF;AAwFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,YADF;AAETC,QAAAA,OAAO,EAAE,eAFA;AAGTC,QAAAA,MAAM,EAAE,eAHC;AAITC,QAAAA,IAAI,EAAE,mBAJG;AAKTC,QAAAA,MAAM,EAAE,qBALC;AAMTC,QAAAA,KAAK,EAAE,gBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAxFJ;AAiGPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,kBADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,UAAU,EAAE,SAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE,sCARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OAjGA;AA4GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,UAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,uBALb;AAMRC,QAAAA,aAAa,EAAE,eANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA5GH;AAqHP3B,MAAAA,IAAI,EAAE;AACJ,kBAAU,QADN;AAEJ,2BAAmB,qBAFf;AAGJ,gBAAQ,gCAHJ;AAIJ,gBAAQ,sCAJJ;AAKJ,eAAO,OALH;AAMJ,iBAAS,iBANL;AAOJ,gBAAQ,sBAPJ;AAQJ,kBAAU,sBARN;AASJ,qBAAa,6BATT;AAUJ,yBAAiB,2BAVb;AAWJ,wBAAgB,eAXZ;AAYJ,uBAAe,6BAZX;AAaJ,yBAAiB,6BAbb;AAcJ,wBAAgB,0BAdZ;AAeJ,uBAAe,2BAfX;AAgBJ,+BAAuB,iCAhBnB;AAiBJ,6BAAqB,8BAjBjB;AAkBJ,mBAAW,qBAlBP;AAmBJ,kBAAU,kBAnBN;AAoBJ,sBAAc,4DApBV;AAqBJ,oBAAY,6CArBR;AAsBJ,oBAAY,6CAtBR;AAuBJ,oBAAY,6CAvBR;AAwBJ,oBAAY,6CAxBR;AAyBJ,oBAAY,6CAzBR;AA0BJ,oBAAY,6CA1BR;AA2BJ,gCAAwB,6BA3BpB;AA4BJ,2BAAmB;AA5Bf,OArHC;AAmJP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAnJF;AAuJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG,OAvJN;AA2JPC,MAAAA,MAAM,EAAE;AACNC,QAAAA,WAAW,EAAE;AADP;AA3JD;AADiB,GAA5B;AAiKD,CAlKD,EAkKGC,MAlKH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-el-GR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'el-GR': {\n      font: {\n        bold: 'Έντονα',\n        italic: 'Πλάγια',\n        underline: 'Υπογραμμισμένα',\n        clear: 'Καθαρισμός',\n        height: 'Ύψος',\n        name: 'Γραμματοσειρ<PERSON>',\n        strikethrough: 'Διεγραμμένα',\n        subscript: 'Δείκτης',\n        superscript: 'Εκθέτης',\n        size: 'Μέγεθος',\n        sizeunit: 'Μονάδα μεγέθους',\n      },\n      image: {\n        image: 'Εικόνα',\n        insert: 'Εισαγωγή',\n        resizeFull: 'Πλήρες μέγεθος',\n        resizeHalf: 'Μισό μέγεθος',\n        resizeQuarter: '1/4 μέγεθος',\n        resizeNone: 'Αρχικό μέγεθος',\n        floatLeft: 'Μετατόπιση αριστερά',\n        floatRight: 'Μετατόπιση δεξιά',\n        floatNone: 'Χωρίς μετατόπιση',\n        shapeRounded: 'Σχήμα: Στρογγυλεμένο',\n        shapeCircle: 'Σχήμα: Κύκλος',\n        shapeThumbnail: 'Σχήμα: Μικρογραφία',\n        shapeNone: 'Σχήμα: Κανένα',\n        dragImageHere: 'Σύρτε την εικόνα εδώ',\n        dropImage: 'Αφήστε την εικόνα',\n        selectFromFiles: 'Επιλογή από αρχεία',\n        maximumFileSize: 'Μέγιστο μέγεθος αρχείου',\n        maximumFileSizeError: 'Το μέγεθος είναι μεγαλύτερο από το μέγιστο επιτρεπτό.',\n        url: 'URL',\n        remove: 'Αφαίρεση',\n        original: 'Αρχικό',\n      },\n      link: {\n        link: 'Σύνδεσμος',\n        insert: 'Εισαγωγή συνδέσμου',\n        unlink: 'Αφαίρεση συνδέσμου',\n        edit: 'Επεξεργασία συνδέσμου',\n        textToDisplay: 'Κείμενο συνδέσμου',\n        url: 'Σε ποιo URL πρέπει να πηγαίνει αυτός ο σύνδεσμος;',\n        openInNewWindow: 'Άνοιγμα σε νέο παράθυρο',\n        useProtocol: 'Χρήση προεπιλεγμένου πρωτοκόλλου',\n      },\n      video: {\n        video: 'Βίντεο',\n        videoLink: 'Σύνδεσμος Βίντεο',\n        insert: 'Εισαγωγή',\n        url: 'URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ή Youku)',\n      },\n      table: {\n        table: 'Πίνακας',\n        addRowAbove: 'Προσθήκη γραμμής πάνω',\n        addRowBelow: 'Προσθήκη γραμμής κάτω',\n        addColLeft: 'Προσθήκη στήλης αριστερά',\n        addColRight: 'Προσθήκη στήλης δεξία',\n        delRow: 'Διαγραφή γραμμής',\n        delCol: 'Διαγραφή στήλης',\n        delTable: 'Διαγραφή πίνακα',\n      },\n      hr: {\n        insert: 'Εισαγωγή οριζόντιας γραμμής',\n      },\n      style: {\n        style: 'Στυλ',\n        normal: 'Κανονικό',\n        blockquote: 'Παράθεση',\n        pre: 'Ως έχει',\n        h1: 'Κεφαλίδα 1',\n        h2: 'Κεφαλίδα 2',\n        h3: 'Κεφαλίδα 3',\n        h4: 'Κεφαλίδα 4',\n        h5: 'Κεφαλίδα 5',\n        h6: 'Κεφαλίδα 6',\n      },\n      lists: {\n        unordered: 'Αταξινόμητη λίστα',\n        ordered: 'Ταξινομημένη λίστα',\n      },\n      options: {\n        help: 'Βοήθεια',\n        fullscreen: 'Πλήρης οθόνη',\n        codeview: 'Προβολή HTML',\n      },\n      paragraph: {\n        paragraph: 'Παράγραφος',\n        outdent: 'Μείωση εσοχής',\n        indent: 'Άυξηση εσοχής',\n        left: 'Αριστερή στοίχιση',\n        center: 'Στοίχιση στο κέντρο',\n        right: 'Δεξιά στοίχιση',\n        justify: 'Πλήρης στοίχιση',\n      },\n      color: {\n        recent: 'Πρόσφατη επιλογή',\n        more: 'Περισσότερα',\n        background: 'Υπόβαθρο',\n        foreground: 'Μπροστά',\n        transparent: 'Διαφανές',\n        setTransparent: 'Επιλογή διαφάνειας',\n        reset: 'Επαναφορά',\n        resetToDefault: 'Επαναφορά στις προκαθορισμένες τιμές',\n        cpSelect: 'Επιλογή',\n      },\n      shortcut: {\n        shortcuts: 'Συντομεύσεις',\n        close: 'Κλείσιμο',\n        textFormatting: 'Διαμόρφωση κειμένου',\n        action: 'Ενέργεια',\n        paragraphFormatting: 'Διαμόρφωση παραγράφου',\n        documentStyle: 'Στυλ κειμένου',\n        extraKeys: 'Επιπλέον συντομεύσεις',\n      },\n      help: {\n        'escape': 'Έξοδος',\n        'insertParagraph': 'Εισαγωγή παραγράφου',\n        'undo': 'Αναιρεί την προηγούμενη εντολή',\n        'redo': 'Επαναλαμβάνει την προηγούμενη εντολή',\n        'tab': 'Εσοχή',\n        'untab': 'Αναίρεση εσοχής',\n        'bold': 'Ορισμός έντονου στυλ',\n        'italic': 'Ορισμός πλάγιου στυλ',\n        'underline': 'Ορισμός υπογεγραμμένου στυλ',\n        'strikethrough': 'Ορισμός διεγραμμένου στυλ',\n        'removeFormat': 'Αφαίρεση στυλ',\n        'justifyLeft': 'Ορισμός αριστερής στοίχισης',\n        'justifyCenter': 'Ορισμός κεντρικής στοίχισης',\n        'justifyRight': 'Ορισμός δεξιάς στοίχισης',\n        'justifyFull': 'Ορισμός πλήρους στοίχισης',\n        'insertUnorderedList': 'Ορισμός μη-ταξινομημένης λίστας',\n        'insertOrderedList': 'Ορισμός ταξινομημένης λίστας',\n        'outdent': 'Προεξοχή παραγράφου',\n        'indent': 'Εσοχή παραγράφου',\n        'formatPara': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε παράγραφο (P tag)',\n        'formatH1': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H1',\n        'formatH2': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H2',\n        'formatH3': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H3',\n        'formatH4': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H4',\n        'formatH5': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H5',\n        'formatH6': 'Αλλαγή της μορφής του τρέχοντος μπλοκ σε H6',\n        'insertHorizontalRule': 'Εισαγωγή οριζόντιας γραμμής',\n        'linkDialog.show': 'Εμφάνιση διαλόγου συνδέσμου',\n      },\n      history: {\n        undo: 'Αναίρεση',\n        redo: 'Επαναληψη',\n      },\n      specialChar: {\n        specialChar: 'ΕΙΔΙΚΟΙ ΧΑΡΑΚΤΗΡΕΣ',\n        select: 'Επιλέξτε ειδικούς χαρακτήρες',\n      },\n      output: {\n        noSelection: 'Δεν έγινε επιλογή!',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "video", "videoLink", "providers", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "normal", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "output", "noSelection", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}