# views/quan_ly_views.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden
from django.contrib.auth.models import User, Permission
from django.contrib import messages
from django.core.paginator import Paginator
from ..models import UserProfile  # Import UserProfile
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def quan_ly_nguoi_dung(request):
    if request.user.username != 'hungpc892' or not request.user.is_superuser:
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")
    
    users = User.objects.all().order_by('id')
    paginator = Paginator(users, 5)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    for user in page_obj:
        user_permissions = user.user_permissions.all()
        user.permissions = {
            'view_baokiem': any(p.codename == 'view_baokiem' for p in user_permissions),
            'view_kehoach': any(p.codename == 'view_kehoach' for p in user_permissions),
            'view_thuocno': any(p.codename == 'view_thuocno' for p in user_permissions),
            'view_cap_vat_tu_khu_a': any(p.codename == 'view_cap_vat_tu_khu_a' for p in user_permissions),
            'view_nhap_kho_thanh_pham_khu_a': any(p.codename == 'view_nhap_kho_thanh_pham_khu_a' for p in user_permissions),
        }
        try:
            profile = UserProfile.objects.get(user=user)
            user.last_password = profile.last_password
        except UserProfile.DoesNotExist:
            user.last_password = '123'

    edit_user_id = request.GET.get('edit_user_id')
    edit_user = None
    if edit_user_id:
        try:
            edit_user = User.objects.get(id=edit_user_id)
            edit_user_permissions = edit_user.user_permissions.all()
            edit_user.permissions = {
                'view_baokiem': any(p.codename == 'view_baokiem' for p in edit_user_permissions),
                'view_kehoach': any(p.codename == 'view_kehoach' for p in edit_user_permissions),
                'view_thuocno': any(p.codename == 'view_thuocno' for p in edit_user_permissions),
                'view_cap_vat_tu_khu_a': any(p.codename == 'view_cap_vat_tu_khu_a' for p in edit_user_permissions),
                'view_nhap_kho_thanh_pham_khu_a': any(p.codename == 'view_nhap_kho_thanh_pham_khu_a' for p in edit_user_permissions),
            }
            try:
                profile = UserProfile.objects.get(user=edit_user)
                edit_user.last_password = profile.last_password
            except UserProfile.DoesNotExist:
                edit_user.last_password = '123'
        except User.DoesNotExist:
            messages.error(request, "NGƯỜI DÙNG KHÔNG TỒN TẠI!")

    if request.method == "POST" and 'add_user' in request.POST:
        super_password = request.POST.get('super_password', '')
        if super_password == '300892':
            username = request.POST['username']
            password = request.POST['password']
            if not User.objects.filter(username=username).exists():
                user = User.objects.create_user(username=username, password=password)
                user.is_staff = True
                user.save()
                profile = UserProfile.objects.create(user=user, last_password=password)
                # Thêm quyền dựa trên checkbox trong form
                if 'view_baokiem' in request.POST:
                    permission = Permission.objects.get(codename='view_baokiem')
                    user.user_permissions.add(permission)
                if 'view_kehoach' in request.POST:
                    permission = Permission.objects.get(codename='view_kehoach')
                    user.user_permissions.add(permission)
                if 'view_thuocno' in request.POST:
                    permission = Permission.objects.get(codename='view_thuocno')
                    user.user_permissions.add(permission)
                if 'view_cap_vat_tu_khu_a' in request.POST:
                    permission = Permission.objects.get(codename='view_cap_vat_tu_khu_a')
                    user.user_permissions.add(permission)
                if 'view_nhap_kho_thanh_pham_khu_a' in request.POST:
                    permission = Permission.objects.get(codename='view_nhap_kho_thanh_pham_khu_a')
                    user.user_permissions.add(permission)
                messages.success(request, f"ĐÃ THÊM NGƯỜI DÙNG {username} THÀNH CÔNG!")
            else:
                messages.error(request, "TÊN TÀI KHOẢN ĐÃ TỒN TẠI!")
        else:
            messages.error(request, "MẬT KHẨU SUPER KHÔNG ĐÚNG!")
        return redirect('quan_ly_nguoi_dung')

    if request.method == "POST" and 'update_edit' in request.POST:
        super_password = request.POST.get('super_password', '')
        if super_password == '300892':
            user_id = request.POST.get('user_id')  # Sử dụng get() thay vì []
            if user_id:  # Kiểm tra nếu user_id tồn tại và hợp lệ
                try:
                    user_id = int(user_id)  # Chuyển đổi sang số nguyên
                    user = User.objects.get(id=user_id)
                    new_username = request.POST.get('username', '')
                    new_password = request.POST.get('password', '')
                    if new_username and new_username != user.username:
                        user.username = new_username
                    if new_password:
                        user.set_password(new_password)
                        profile, created = UserProfile.objects.get_or_create(user=user)
                        profile.last_password = new_password
                        profile.save()
                    # Xóa các quyền cũ và thêm quyền mới
                    user.user_permissions.clear()
                    if 'view_baokiem' in request.POST:
                        permission = Permission.objects.get(codename='view_baokiem')
                        user.user_permissions.add(permission)
                    if 'view_kehoach' in request.POST:
                        permission = Permission.objects.get(codename='view_kehoach')
                        user.user_permissions.add(permission)
                    if 'view_thuocno' in request.POST:
                        permission = Permission.objects.get(codename='view_thuocno')
                        user.user_permissions.add(permission)
                    if 'view_cap_vat_tu_khu_a' in request.POST:
                        permission = Permission.objects.get(codename='view_cap_vat_tu_khu_a')
                        user.user_permissions.add(permission)
                    if 'view_nhap_kho_thanh_pham_khu_a' in request.POST:
                        permission = Permission.objects.get(codename='view_nhap_kho_thanh_pham_khu_a')
                        user.user_permissions.add(permission)
                    user.save()
                    user._perm_cache = None
                    user.refresh_from_db()
                    messages.success(request, f"ĐÃ CẬP NHẬT NGƯỜI DÙNG {user.username} THÀNH CÔNG!")
                except (ValueError, User.DoesNotExist):
                    messages.error(request, "LỖI: ID NGƯỜI DÙNG KHÔNG HỢP LỆ!")
            else:
                messages.error(request, "LỖI: KHÔNG TÌM THẤY ID NGƯỜI DÙNG!")
        else:
            messages.error(request, "MẬT KHẨU SUPER KHÔNG ĐÚNG!")
        return redirect('quan_ly_nguoi_dung')

    if request.method == "POST" and 'confirm_delete' in request.POST:
        delete_user_id = request.POST.get('delete_user_id')
        if delete_user_id:
            super_password = request.POST.get('super_password', '')
            if super_password == '300892':
                try:
                    user = User.objects.get(id=delete_user_id)
                    username = user.username
                    UserProfile.objects.filter(user=user).delete()
                    user.delete()
                    messages.success(request, f"ĐÃ XÓA NGƯỜI DÙNG {username} THÀNH CÔNG!")
                except User.DoesNotExist:
                    messages.error(request, f"KHÔNG TÌM THẤY NGƯỜI DÙNG {delete_user_id}!")
            else:
                messages.error(request, "MẬT KHẨU SUPER KHÔNG ĐÚNG!")
        return redirect('quan_ly_nguoi_dung')

    return render(request, 'z115_app/quan_ly_nguoi_dung.html', {
        'page_obj': page_obj,
        'edit_user': edit_user,
    })