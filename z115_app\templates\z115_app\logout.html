{% extends 'z115_app/base.html' %}

{% block title %}Đ<PERSON>ng xuất{% endblock %}

{% block content %}
    <div class="wrapper">
        <div class="login-container">
            <div class="text-center">
                <h2 class="text-primary" style="font-size: 20pt; font-weight: bold; text-transform: uppercase;">BẠN ĐÃ ĐĂNG XUẤT THÀNH CÔNG!</h2>
                <a href="{% url 'login' %}" class="btn btn-outline-primary mt-4 custom-login-btn" style="font-weight: bold; font-size: 16pt; color: #0d6efd; border: 2px solid #0d6efd; padding: 10px 20px;">Đ<PERSON>ng nhập lại</a>
            </div>
        </div>
    </div>

    <!-- CSS tùy chỉnh để loại bỏ hiệu ứng mờ khi hover và bắt chước login -->
    <style>
        .wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e6e6fa; /* Nền tím nhạt giống login */
            margin: 0;
            font-family: 'Times New Roman', Times, serif;
        }
        .login-container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            border: 2px solid #333; /* Viền đậm giống login */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .custom-login-btn:hover {
            color: #0d6efd !important; /* Giữ màu xanh dương */
            background-color: transparent !important; /* Giữ nền trong suốt */
            opacity: 1 !important; /* Loại bỏ hiệu ứng mờ */
            text-decoration: none !important; /* Loại bỏ gạch chân nếu có */
        }
    </style>
{% endblock %}