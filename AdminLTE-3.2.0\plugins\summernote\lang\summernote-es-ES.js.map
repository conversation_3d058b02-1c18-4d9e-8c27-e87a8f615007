{"version": 3, "file": "lang/summernote-es-ES.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,0BAJH;AAKJC,QAAAA,MAAM,EAAE,iBALJ;AAMJC,QAAAA,IAAI,EAAE,eANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE,qBAVF;AAWJC,QAAAA,QAAQ,EAAE;AAXN,OADC;AAcPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,iBAFH;AAGLC,QAAAA,UAAU,EAAE,iCAHP;AAILC,QAAAA,UAAU,EAAE,0BAJP;AAKLC,QAAAA,aAAa,EAAE,2BALV;AAMLC,QAAAA,UAAU,EAAE,iBANP;AAOLC,QAAAA,SAAS,EAAE,uBAPN;AAQLC,QAAAA,UAAU,EAAE,qBARP;AASLC,QAAAA,SAAS,EAAE,WATN;AAULC,QAAAA,YAAY,EAAE,mBAVT;AAWLC,QAAAA,WAAW,EAAE,gBAXR;AAYLC,QAAAA,cAAc,EAAE,kBAZX;AAaLC,QAAAA,SAAS,EAAE,gBAbN;AAcLC,QAAAA,aAAa,EAAE,kCAdV;AAeLC,QAAAA,SAAS,EAAE,2BAfN;AAgBLC,QAAAA,eAAe,EAAE,uBAhBZ;AAiBLC,QAAAA,eAAe,EAAE,2BAjBZ;AAkBLC,QAAAA,oBAAoB,EAAE,uCAlBjB;AAmBLC,QAAAA,GAAG,EAAE,kBAnBA;AAoBLC,QAAAA,MAAM,EAAE,oBApBH;AAqBLC,QAAAA,QAAQ,EAAE;AArBL,OAdA;AAqCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLrB,QAAAA,MAAM,EAAE,mBAHH;AAILiB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OArCA;AA4CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJvB,QAAAA,MAAM,EAAE,oBAFJ;AAGJwB,QAAAA,MAAM,EAAE,kBAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,iBALX;AAMJT,QAAAA,GAAG,EAAE,+BAND;AAOJU,QAAAA,eAAe,EAAE,4BAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA5CC;AAsDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,wBAFR;AAGLC,QAAAA,WAAW,EAAE,wBAHR;AAILC,QAAAA,UAAU,EAAE,mCAJP;AAKLC,QAAAA,WAAW,EAAE,iCALR;AAMLC,QAAAA,MAAM,EAAE,gBANH;AAOLC,QAAAA,MAAM,EAAE,mBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAtDA;AAgEPC,MAAAA,EAAE,EAAE;AACFrC,QAAAA,MAAM,EAAE;AADN,OAhEG;AAmEPsC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,MAHP;AAILC,QAAAA,GAAG,EAAE,QAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAnEA;AA+EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,OADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA/EA;AAmFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,mBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAnFF;AAwFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,SADF;AAETC,QAAAA,OAAO,EAAE,oBAFA;AAGTC,QAAAA,MAAM,EAAE,qBAHC;AAITC,QAAAA,IAAI,EAAE,wBAJG;AAKTC,QAAAA,MAAM,EAAE,SALC;AAMTC,QAAAA,KAAK,EAAE,sBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAxFJ;AAiGPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,cADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,gBAHP;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,yBANX;AAOLC,QAAAA,KAAK,EAAE,aAPF;AAQLC,QAAAA,cAAc,EAAE,wCARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OAjGA;AA4GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,mBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,qBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA5GH;AAqHP3B,MAAAA,IAAI,EAAE;AACJ4B,QAAAA,eAAe,EAAE,qBADb;AAEJC,QAAAA,IAAI,EAAE,2BAFF;AAGJC,QAAAA,IAAI,EAAE,0BAHF;AAIJC,QAAAA,GAAG,EAAE,SAJD;AAKJC,QAAAA,KAAK,EAAE,qBALH;AAMJhG,QAAAA,IAAI,EAAE,2BANF;AAOJC,QAAAA,MAAM,EAAE,2BAPJ;AAQJC,QAAAA,SAAS,EAAE,6BARP;AASJI,QAAAA,aAAa,EAAE,2BATX;AAUJ2F,QAAAA,YAAY,EAAE,gBAVV;AAWJC,QAAAA,WAAW,EAAE,wBAXT;AAYJC,QAAAA,aAAa,EAAE,mBAZX;AAaJC,QAAAA,YAAY,EAAE,sBAbV;AAcJC,QAAAA,WAAW,EAAE,YAdT;AAeJC,QAAAA,mBAAmB,EAAE,gBAfjB;AAgBJC,QAAAA,iBAAiB,EAAE,yBAhBf;AAiBJnC,QAAAA,OAAO,EAAE,6BAjBL;AAkBJC,QAAAA,MAAM,EAAE,8BAlBJ;AAmBJmC,QAAAA,UAAU,EAAE,6DAnBR;AAoBJC,QAAAA,QAAQ,EAAE,2CApBN;AAqBJC,QAAAA,QAAQ,EAAE,2CArBN;AAsBJC,QAAAA,QAAQ,EAAE,2CAtBN;AAuBJC,QAAAA,QAAQ,EAAE,2CAvBN;AAwBJC,QAAAA,QAAQ,EAAE,2CAxBN;AAyBJC,QAAAA,QAAQ,EAAE,2CAzBN;AA0BJC,QAAAA,oBAAoB,EAAE,+BA1BlB;AA2BJ,2BAAmB;AA3Bf,OArHC;AAkJPC,MAAAA,OAAO,EAAE;AACPnB,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAlJF;AAsJPmB,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,uBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG,OAtJN;AA0JPC,MAAAA,MAAM,EAAE;AACNC,QAAAA,WAAW,EAAE;AADP;AA1JD;AADiB,GAA5B;AAgKD,CAjKD,EAiKGC,MAjKH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-es-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'es-ES': {\n      font: {\n        bold: 'Negrita',\n        italic: 'Curs<PERSON>',\n        underline: 'Subrayado',\n        clear: 'Eliminar estilo de letra',\n        height: 'Altura de línea',\n        name: 'Tipo de letra',\n        strikethrough: '<PERSON><PERSON><PERSON>',\n        subscript: 'Subíndice',\n        superscript: 'Superíndice',\n        size: 'Tamaño de la fuente',\n        sizeunit: 'Unidad del tamaño de letra',\n      },\n      image: {\n        image: 'Imagen',\n        insert: 'Insertar imagen',\n        resizeFull: 'Redimensionar a tamaño completo',\n        resizeHalf: 'Redimensionar a la mitad',\n        resizeQuarter: 'Redimensionar a un cuarto',\n        resizeNone: 'Tamaño original',\n        floatLeft: 'Flotar a la izquierda',\n        floatRight: 'Flotar a la derecha',\n        floatNone: 'No flotar',\n        shapeRounded: 'Forma: Redondeado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Miniatura',\n        shapeNone: 'Forma: Ninguna',\n        dragImageHere: 'Arrastre una imagen o texto aquí',\n        dropImage: 'Suelte una imagen o texto',\n        selectFromFiles: 'Seleccione un fichero',\n        maximumFileSize: 'Tamaño máximo del fichero',\n        maximumFileSizeError: 'Superado el tamaño máximo de fichero.',\n        url: 'URL de la imagen',\n        remove: 'Eliminar la imagen',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Enlace del vídeo',\n        insert: 'Insertar un vídeo',\n        url: 'URL del vídeo',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Enlace',\n        insert: 'Insertar un enlace',\n        unlink: 'Quitar el enlace',\n        edit: 'Editar',\n        textToDisplay: 'Texto a mostrar',\n        url: '¿A qué URL lleva este enlace?',\n        openInNewWindow: 'Abrir en una nueva ventana',\n        useProtocol: 'Usar el protocolo predefinido',\n      },\n      table: {\n        table: 'Tabla',\n        addRowAbove: 'Añadir una fila encima',\n        addRowBelow: 'Añadir una fila debajo',\n        addColLeft: 'Añadir una columna a la izquierda',\n        addColRight: 'Añadir una columna a la derecha',\n        delRow: 'Borrar la fila',\n        delCol: 'Borrar la columna',\n        delTable: 'Borrar la tabla',\n      },\n      hr: {\n        insert: 'Insertar una línea horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Cita',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ayuda',\n        fullscreen: 'Pantalla completa',\n        codeview: 'Ver el código fuente',\n      },\n      paragraph: {\n        paragraph: 'Párrafo',\n        outdent: 'Reducir la sangría',\n        indent: 'Aumentar la sangría',\n        left: 'Alinear a la izquierda',\n        center: 'Centrar',\n        right: 'Alinear a la derecha',\n        justify: 'Justificar',\n      },\n      color: {\n        recent: 'Último color',\n        more: 'Más colores',\n        background: 'Color de fondo',\n        foreground: 'Color del texto',\n        transparent: 'Transparente',\n        setTransparent: 'Establecer transparente',\n        reset: 'Restablecer',\n        resetToDefault: 'Restablecer a los valores predefinidos',\n        cpSelect: 'Seleccionar',\n      },\n      shortcut: {\n        shortcuts: 'Atajos de teclado',\n        close: 'Cerrar',\n        textFormatting: 'Formato de texto',\n        action: 'Acción',\n        paragraphFormatting: 'Formato de párrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Teclas adicionales',\n      },\n      help: {\n        insertParagraph: 'Insertar un párrafo',\n        undo: 'Deshacer la última acción',\n        redo: 'Rehacer la última acción',\n        tab: 'Tabular',\n        untab: 'Eliminar tabulación',\n        bold: 'Establecer estilo negrita',\n        italic: 'Establecer estilo cursiva',\n        underline: 'Establecer estilo subrayado',\n        strikethrough: 'Establecer estilo tachado',\n        removeFormat: 'Limpiar estilo',\n        justifyLeft: 'Alinear a la izquierda',\n        justifyCenter: 'Alinear al centro',\n        justifyRight: 'Alinear a la derecha',\n        justifyFull: 'Justificar',\n        insertUnorderedList: 'Insertar lista',\n        insertOrderedList: 'Insertar lista numerada',\n        outdent: 'Reducir sangría del párrafo',\n        indent: 'Aumentar sangría del párrafo',\n        formatPara: 'Cambiar el formato del bloque actual a párrafo (etiqueta P)',\n        formatH1: 'Cambiar el formato del bloque actual a H1',\n        formatH2: 'Cambiar el formato del bloque actual a H2',\n        formatH3: 'Cambiar el formato del bloque actual a H3',\n        formatH4: 'Cambiar el formato del bloque actual a H4',\n        formatH5: 'Cambiar el formato del bloque actual a H5',\n        formatH6: 'Cambiar el formato del bloque actual a H6',\n        insertHorizontalRule: 'Insertar una línea horizontal',\n        'linkDialog.show': 'Mostrar el panel de enlaces',\n      },\n      history: {\n        undo: 'Deshacer',\n        redo: 'Rehacer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIALES',\n        select: 'Seleccionar caracteres especiales',\n      },\n      output: {\n        noSelection: '¡No ha seleccionado nada!',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "output", "noSelection", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}