{"version": 3, "file": "lang/summernote-az-AZ.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,MAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,SAJH;AAKJC,QAAAA,MAAM,EAAE,kBALJ;AAMJC,QAAAA,IAAI,EAAE,WANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,SAAS,EAAE,YARP;AASJC,QAAAA,WAAW,EAAE,YATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,aAAa,EAAE,UALV;AAMLC,QAAAA,SAAS,EAAE,UANN;AAOLC,QAAAA,UAAU,EAAE,UAPP;AAQLC,QAAAA,SAAS,EAAE,6BARN;AASLC,QAAAA,YAAY,EAAE,sBATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,eAbV;AAcLC,QAAAA,SAAS,EAAE,2BAdN;AAeLC,QAAAA,eAAe,EAAE,aAfZ;AAgBLC,QAAAA,eAAe,EAAE,uBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,mCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,WAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,aAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,cAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,WAHJ;AAIJC,QAAAA,IAAI,EAAE,kBAJF;AAKJC,QAAAA,aAAa,EAAE,+BALX;AAMJT,QAAAA,GAAG,EAAE,cAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,uBAFR;AAGLC,QAAAA,WAAW,EAAE,sBAHR;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,WAAW,EAAE,qBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,YAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,UAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,eADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,WAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,UAJG;AAKTC,QAAAA,MAAM,EAAE,YALC;AAMTC,QAAAA,KAAK,EAAE,UANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,UADH;AAELC,QAAAA,IAAI,EAAE,eAFD;AAGLC,QAAAA,UAAU,EAAE,gBAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,YADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,sBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,0BALb;AAMRC,QAAAA,aAAa,EAAE,aANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,sBADf;AAEJ,gBAAQ,oBAFJ;AAGJ,gBAAQ,qBAHJ;AAIJ,eAAO,mBAJH;AAKJ,iBAAS,mBALL;AAMJ,gBAAQ,gCANJ;AAOJ,kBAAU,iCAPN;AAQJ,qBAAa,qCART;AASJ,yBAAiB,qCATb;AAUJ,wBAAgB,4BAVZ;AAWJ,uBAAe,mBAXX;AAYJ,yBAAiB,qBAZb;AAaJ,wBAAgB,mBAbZ;AAcJ,uBAAe,6BAdX;AAeJ,+BAAuB,0BAfnB;AAgBJ,6BAAqB,yBAhBjB;AAiBJ,mBAAW,sCAjBP;AAkBJ,kBAAU,sCAlBN;AAmBJ,sBAAc,uDAnBV;AAoBJ,oBAAY,wDApBR;AAqBJ,oBAAY,wDArBR;AAsBJ,oBAAY,wDAtBR;AAuBJ,oBAAY,wDAvBR;AAwBJ,oBAAY,wDAxBR;AAyBJ,oBAAY,wDAzBR;AA0BJ,gCAAwB,uBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,kBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,kBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-az-AZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "//Summernote WYSIWYG  editor ucun Azerbaycan dili fayli\n//Tercume etdi: RAMIL ALIYEV\n//Tarix: 20.07.2019\n//Baki Azerbaycan\n//Website: https://ramilaliyev.com\n\n//Azerbaijan language for Summernote WYSIWYG \n//Translated by: RAMIL ALIYEV\n//Date: 20.07.2019\n//Baku Azerbaijan\n//Website: https://ramilaliyev.com\n\n(function($) {\n  $.extend($.summernote.lang, {\n    'az-AZ': {\n      font: {\n        bold: 'Qalın',\n        italic: 'Əyri',\n        underline: 'Altı xətli',\n        clear: 'Təmizlə',\n        height: '<PERSON><PERSON><PERSON><PERSON> hünd<PERSON>rl<PERSON>',\n        name: '<PERSON><PERSON><PERSON> Tipi',\n        strikethrough: '<PERSON>st<PERSON> xətli',\n        subscript: 'Alt simvol',\n        superscript: 'Üst simvol',\n        size: 'Yazı ölçüsü',\n      },\n      image: {\n        image: 'Şəkil',\n        insert: 'Şəkil əlavə et',\n        resizeFull: 'Original ölçü',\n        resizeHalf: '1/2 ölçü',\n        resizeQuarter: '1/4 ölçü',\n        floatLeft: 'Sola çək',\n        floatRight: 'Sağa çək',\n        floatNone: 'Sola-sağa çəkilməni ləğv et',\n        shapeRounded: 'Şəkil: yuvarlaq künç',\n        shapeCircle: 'Şəkil: Dairə',\n        shapeThumbnail: 'Şəkil: Thumbnail',\n        shapeNone: 'Şəkil: Yox',\n        dragImageHere: 'Bura sürüşdür',\n        dropImage: 'Şəkil və ya mətni buraxın',\n        selectFromFiles: 'Sənəd seçin',\n        maximumFileSize: 'Maksimum sənəd ölçüsü',\n        maximumFileSizeError: 'Maksimum sənəd ölçüsünü keçdiniz.',\n        url: 'Şəkil linki',\n        remove: 'Şəkli sil',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video linki',\n        insert: 'Video əlavə et',\n        url: 'Video linki?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion və ya Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link əlavə et',\n        unlink: 'Linki sil',\n        edit: 'Linkə düzəliş et',\n        textToDisplay: 'Ekranda göstəriləcək link adı',\n        url: 'Link ünvanı?',\n        openInNewWindow: 'Yeni pəncərədə aç',\n      },\n      table: {\n        table: 'Cədvəl',\n        addRowAbove: 'Yuxarı sətir əlavə et',\n        addRowBelow: 'Aşağı sətir əlavə et',\n        addColLeft: 'Sola sütun əlavə et',\n        addColRight: 'Sağa sütun əlavə et',\n        delRow: 'Sətiri sil',\n        delCol: 'Sütunu sil',\n        delTable: 'Cədvəli sil',\n      },\n      hr: {\n        insert: 'Üfuqi xətt əlavə et',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'İstinad',\n        pre: 'Ön baxış',\n        h1: 'Başlıq 1',\n        h2: 'Başlıq 2',\n        h3: 'Başlıq 3',\n        h4: 'Başlıq 4',\n        h5: 'Başlıq 5',\n        h6: 'Başlıq 6',\n      },\n      lists: {\n        unordered: 'Nizamsız sıra',\n        ordered: 'Nizamlı sıra',\n      },\n      options: {\n        help: 'Kömək',\n        fullscreen: 'Tam ekran',\n        codeview: 'HTML Kodu',\n      },\n      paragraph: {\n        paragraph: 'Paraqraf',\n        outdent: 'Girintini artır',\n        indent: 'Girintini azalt',\n        left: 'Sola çək',\n        center: 'Ortaya çək',\n        right: 'Sağa çək',\n        justify: 'Sola və sağa çək',\n      },\n      color: {\n        recent: 'Son rənk',\n        more: 'Daha çox rənk',\n        background: 'Arxa fon rəngi',\n        foreground: 'Yazı rıngi',\n        transparent: 'Şəffaflıq',\n        setTransparent: 'Şəffaflığı nizamla',\n        reset: 'Sıfırla',\n        resetToDefault: 'Susyama görə sıfırla',\n      },\n      shortcut: {\n        shortcuts: 'Qısayollar',\n        close: 'Bağla',\n        textFormatting: 'Yazı formatlandırmaq',\n        action: 'Hadisə',\n        paragraphFormatting: 'Paraqraf formatlandırmaq',\n        documentStyle: 'Sənəd stili',\n        extraKeys: 'Əlavə',\n      },\n      help: {\n        'insertParagraph': 'Paraqraf əlavə etmək',\n        'undo': 'Son əmri geri alır',\n        'redo': 'Son əmri irəli alır',\n        'tab': 'Girintini artırır',\n        'untab': 'Girintini azaltır',\n        'bold': 'Qalın yazma stilini nizamlayır',\n        'italic': 'İtalik yazma stilini nizamlayır',\n        'underline': 'Altı xətli yazma stilini nizamlayır',\n        'strikethrough': 'Üstü xətli yazma stilini nizamlayır',\n        'removeFormat': 'Formatlandırmanı ləğv edir',\n        'justifyLeft': 'Yazını sola çəkir',\n        'justifyCenter': 'Yazını ortaya çəkir',\n        'justifyRight': 'Yazını sağa çəkir',\n        'justifyFull': 'Yazını hər iki tərəfə yazır',\n        'insertUnorderedList': 'Nizamsız sıra əlavə edir',\n        'insertOrderedList': 'Nizamlı sıra əlavə edir',\n        'outdent': 'Aktiv paraqrafın girintisini azaltır',\n        'indent': 'Aktiv paragrafın girintisini artırır',\n        'formatPara': 'Aktiv bloqun formatını paraqraf (p) olaraq dəyişdirir',\n        'formatH1': 'Aktiv bloqun formatını başlıq 1 (h1) olaraq dəyişdirir',\n        'formatH2': 'Aktiv bloqun formatını başlıq 2 (h2) olaraq dəyişdirir',\n        'formatH3': 'Aktiv bloqun formatını başlıq 3 (h3) olaraq dəyişdirir',\n        'formatH4': 'Aktiv bloqun formatını başlıq 4 (h4) olaraq dəyişdirir',\n        'formatH5': 'Aktiv bloqun formatını başlıq 5 (h5) olaraq dəyişdirir',\n        'formatH6': 'Aktiv bloqun formatını başlıq 6 (h6) olaraq dəyişdirir',\n        'insertHorizontalRule': 'Üfuqi xətt əlavə edir',\n        'linkDialog.show': 'Link parametrləri qutusunu göstərir',\n      },\n      history: {\n        undo: 'Əvvəlki vəziyyət',\n        redo: 'Sonrakı vəziyyət',\n      },\n      specialChar: {\n        specialChar: 'Xüsusi simvollar',\n        select: 'Xüsusi simvolları seçin',\n      },\n    },\n  });\n})(jQuery);"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}