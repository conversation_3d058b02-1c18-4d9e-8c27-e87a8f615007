{% load static %}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>In Phiếu Cấp V<PERSON><PERSON></title>
    <style>
        @page {
            size: {% if phieu.don_vi_nhan == "PHÂN XƯỞNG A2" %}A4 landscape{% else %}A4{% endif %};
            margin: 10mm;
        }

        @media print {
            html, body {
                width: 100%;
                margin: 0;
                padding: 0;
                font-family: 'Times New Roman', Times, serif;
                font-size: 14px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .no-print {
                display: none;
            }

            table, th, td {
                page-break-inside: avoid;
            }
        }

        body {
            margin: 5mm auto 10mm auto;
            color: black;
            font-family: 'Times New Roman', Times, serif;
            font-size: 14px;
            max-width: 100%;
        }

        h1 {
            font-size: 18px;
            text-align: center;
            margin: 0;
            font-weight: bold;
            line-height: 1.3;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8mm;
            table-layout: fixed;
            word-wrap: break-word;
            font-size: 14px;
        }

        .table th {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: center;
            word-break: break-word;
            background-color: white;
        }

        .table td {
            border-left: 1px solid #000;
            border-right: 1px solid #000;
            border-bottom: 1px dashed #666;
            padding: 4px 6px;
            text-align: center;
            word-break: break-word;
        }

        .table tbody tr:last-child td {
            border-bottom: 1px solid #000;
        }

        .footer {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
            text-align: center;
            page-break-inside: avoid;
        }

        .footer-column {
            width: 40%;
        }

        .footer .label {
            font-weight: bold;
            margin-bottom: 30px;
        }

        .footer .signed {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .footer .name {
            font-weight: normal;
        }

        .no-print {
            margin-bottom: 10px;
        }

        .content-section {
            margin-top: 10mm;
            line-height: 1.5;
        }

        .content-section p {
            margin: 0 0 3px 0;
        }
    </style>
</head>
<body>

<!-- Thanh chỉnh Zoom và nút in -->
<div class="no-print">
    <p><em>⚠️ Nếu phiếu rộng, hãy chỉnh tỷ lệ hoặc chọn Landscape khi in PDF</em></p>
    <label for="scale">Tùy chỉnh tỷ lệ in (%): </label>
    <input type="number" id="scale" value="100" min="50" max="150" onchange="adjustZoom()">
    <button onclick="window.print()">In phiếu (PDF)</button>
</div>

<!-- Header bên trái -->
<div style="text-align: left; margin-bottom: 5px; margin-top: -5mm;">
    <div style="line-height: 1.4; margin-left: 5px;">
        <div style="margin-left: 2mm;">NHÀ MÁY Z115</div>
        <strong style="font-size: 15px;">PHÒNG VẬT TƯ</strong><br>
        <img src="{% static 'images/logo.png' %}" alt="Logo" width="90" style="margin-left: 4mm;">
    </div>
</div>

<!-- Tiêu đề căn giữa -->
<h1 style="margin-top: -5mm;">
    LỆNH TẠM CẤP VẬT TƯ<br>PHỤC VỤ SẢN XUẤT
</h1>

<!-- Số phiếu bên phải (dịch sang trái 1cm và tăng font-size) -->
<div style="text-align: right; margin-top: -60px; margin-right: 1cm;">
    <strong style="font-size: 16px;">Số phiếu:</strong> {{ phieu.so_phieu }}
</div>

<!-- Nội dung chính -->
<div class="content-section" style="margin-top: 10mm;">
    <p><strong>Tài khoản tạo phiếu:</strong> {{ phieu.tai_khoan_tao }}</p>
    <p><strong>Ngày tạo phiếu:</strong> {{ phieu.ngay_tao|date:"d/m/Y" }}</p>
    <p><strong>Đơn vị nhận hàng:</strong> {{ phieu.don_vi_nhan }}</p>
    <p><strong>Kho cấp vật tư:</strong> {{ phieu.kho }}</p>
</div>

<!-- Bảng vật tư -->
<table class="table">
    <thead>
        <tr>
            <th style="width: 4%;">STT</th>
            <th style="width: 10%;">Mục đích sử dụng</th>
            <th style="width: 18%;">Tên vật tư và quy cách</th>
            <th style="width: 5%;">Đvt</th>
            <th style="width: 7%;">Số lượng yêu cầu</th>
            <th style="width: 9%;">Số lượng ĐM duyệt</th>
            <th style="width: 9%;">Số lượng thủ kho cấp</th>
            <th style="width: 7%;">Thủ kho ký giao</th>
            <th style="width: 7%;">Thủ kho ký nhận</th>
            <th style="width: 7%;">Ghi chú</th>

            {% if phieu.don_vi_nhan == "PHÂN XƯỞNG A2" %}
                <th style="width: 8%;">Ngày nhận vật tư</th>
                <th style="width: 6%;">Số lượng nhận</th>
                <th style="width: 7%;">Số phiếu xuất</th>
                <th style="width: 8%;">Ngày cấp vật tư</th>
                <th style="width: 8%;">Số lượng cấp SX</th>
                <th style="width: 10%;">Người nhận</th>
            {% endif %}
        </tr>
    </thead>
    <tbody>
        {% for vt in vat_tus %}
        <tr>
            <td>{{ forloop.counter }}</td>
            <td>{{ vt.muc_dich_su_dung }}</td>
            <td>{{ vt.ten_vat_tu }}</td>
            <td>{{ vt.don_vi_tinh }}</td>
            <td>{{ vt.so_luong_yeu_cau }}</td>
            <td>{{ vt.so_luong_duyet }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>

            {% if phieu.don_vi_nhan == "PHÂN XƯỞNG A2" %}
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            {% endif %}
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Chữ ký -->
<div class="footer">
    <div class="footer-column">
        <div class="label">ĐỊNH MỨC VIÊN DUYỆT</div>
        <div class="signed">ĐÃ KÝ</div>
        <div class="signed">{{ dmv_log.thoi_gian|date:"H:i d/m/Y" }}</div>
        <div class="name">{{ dmv_log.nguoi_duyet }}</div>
    </div>
    <div class="footer-column">
        <div class="label">CHỈ HUY PHÊ DUYỆT</div>
        <div class="signed">ĐÃ KÝ</div>
        <div class="signed">{{ ch_log.thoi_gian|date:"H:i d/m/Y" }}</div>
        <div class="name">{{ ch_log.nguoi_duyet }}</div>
    </div>
</div>

<!-- Script chỉnh zoom -->
<script>
    function adjustZoom() {
        const scale = document.getElementById('scale').value;
        document.body.style.zoom = scale + "%";
    }
</script>

</body>
</html>
