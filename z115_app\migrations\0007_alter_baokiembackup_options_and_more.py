# Generated by Django 5.0.7 on 2025-06-25 09:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('z115_app', '0006_baokiembackup_remove_baokiemvattu_backup_date'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='baokiembackup',
            options={'verbose_name': 'Backup B<PERSON>', 'verbose_name_plural': 'Backups Báo <PERSON>ểm <PERSON>ư'},
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='dung_vao_viec',
            field=models.Char<PERSON>ield(max_length=255),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='ket_qua_kiem_tra',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='ma_kho',
            field=models.Char<PERSON>ield(blank=True, max_length=10),
        ),
        migrations.Alter<PERSON>ield(
            model_name='baokiembackup',
            name='ma_vlspp',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='noi_de_vat_tu',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='so_hop_cach',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='so_luong',
            field=models.FloatField(),
        ),
        migrations.AlterField(
            model_name='baokiembackup',
            name='ten_quy_cach',
            field=models.CharField(max_length=255),
        ),
    ]
