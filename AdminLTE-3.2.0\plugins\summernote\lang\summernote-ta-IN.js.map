{"version": 3, "file": "lang/summernote-ta-IN.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,QADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,QAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,iBANF;AAOJC,QAAAA,aAAa,EAAE,kBAPX;AAQJC,QAAAA,IAAI,EAAE,gBARF;AASJC,QAAAA,WAAW,EAAE,YATT;AAUJC,QAAAA,SAAS,EAAE;AAVP,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,eAFH;AAGLC,QAAAA,UAAU,EAAE,WAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,aAAa,EAAE,WALV;AAMLC,QAAAA,SAAS,EAAE,gBANN;AAOLC,QAAAA,UAAU,EAAE,gBAPP;AAQLC,QAAAA,SAAS,EAAE,mBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,aAXX;AAYLC,QAAAA,SAAS,EAAE,iBAZN;AAaLC,QAAAA,aAAa,EAAE,wBAbV;AAcLC,QAAAA,SAAS,EAAE,aAdN;AAeLC,QAAAA,eAAe,EAAE,wBAfZ;AAgBLC,QAAAA,eAAe,EAAE,uBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,mCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,eAlBA;AAmBLC,QAAAA,MAAM,EAAE,eAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,SAAS,EAAE,iBAFN;AAGLpB,QAAAA,MAAM,EAAE,iBAHH;AAILgB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,SADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,gBAHJ;AAIJC,QAAAA,IAAI,EAAE,eAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,eAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,CAAC,EAAE,OAFE;AAGLC,QAAAA,UAAU,EAAE,WAHP;AAILC,QAAAA,GAAG,EAAE,UAJA;AAKLC,QAAAA,EAAE,EAAE,WALC;AAMLC,QAAAA,EAAE,EAAE,WANC;AAOLC,QAAAA,EAAE,EAAE,WAPC;AAQLC,QAAAA,EAAE,EAAE,WARC;AASLC,QAAAA,EAAE,EAAE,WATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,YADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,YAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,OADF;AAETC,QAAAA,OAAO,EAAE,aAFA;AAGTC,QAAAA,MAAM,EAAE,aAHC;AAITC,QAAAA,IAAI,EAAE,gBAJG;AAKTC,QAAAA,MAAM,EAAE,eALC;AAMTC,QAAAA,KAAK,EAAE,gBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,QAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,cAAc,EAAE,cANX;AAOLC,QAAAA,KAAK,EAAE,YAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,aADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,oBAHR;AAIRC,QAAAA,MAAM,EAAE,cAJA;AAKRC,QAAAA,mBAAmB,EAAE,kBALb;AAMRC,QAAAA,aAAa,EAAE,UANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ta-IN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ta-IN': {\n      font: {\n        bold: 'தடித்த',\n        italic: 'சாய்வு',\n        underline: 'அடிக்கோடு',\n        clear: 'நீக்கு',\n        height: 'வரி  உயரம்',\n        name: 'எழுத்துரு பெயர்',\n        strikethrough: 'குறுக்குக் கோடு',\n        size: 'எழுத்துரு அளவு',\n        superscript: 'மேல் ஒட்டு',\n        subscript: 'கீழ் ஒட்டு',\n      },\n      image: {\n        image: 'படம்',\n        insert: 'படத்தை செருகு',\n        resizeFull: 'முழு அளவை',\n        resizeHalf: 'அரை அளவை',\n        resizeQuarter: 'கால் அளவை',\n        floatLeft: 'இடப்பக்கமாக வை',\n        floatRight: 'வலப்பக்கமாக வை',\n        floatNone: 'இயல்புநிலையில் வை',\n        shapeRounded: 'வட்டமான வடிவம்',\n        shapeCircle: 'வட்ட வடிவம்',\n        shapeThumbnail: 'சிறு வடிவம்',\n        shapeNone: 'வடிவத்தை நீக்கு',\n        dragImageHere: 'படத்தை இங்கே இழுத்துவை',\n        dropImage: 'படத்தை விடு',\n        selectFromFiles: 'கோப்புகளை தேர்வு செய்',\n        maximumFileSize: 'அதிகபட்ச கோப்பு அளவு',\n        maximumFileSizeError: 'கோப்பு அதிகபட்ச அளவை மீறிவிட்டது',\n        url: 'இணையதள முகவரி',\n        remove: 'படத்தை நீக்கு',\n        original: 'Original',\n      },\n      video: {\n        video: 'காணொளி',\n        videoLink: 'காணொளி இணைப்பு',\n        insert: 'காணொளியை செருகு',\n        url: 'இணையதள முகவரி',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'இணைப்பு',\n        insert: 'இணைப்பை செருகு',\n        unlink: 'இணைப்பை நீக்கு',\n        edit: 'இணைப்பை தொகு',\n        textToDisplay: 'காட்சி வாசகம்',\n        url: 'இணையதள முகவரி',\n        openInNewWindow: 'புதிய சாளரத்தில் திறக்க',\n      },\n      table: {\n        table: 'அட்டவணை',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'கிடைமட்ட கோடு',\n      },\n      style: {\n        style: 'தொகுப்பு',\n        p: 'பத்தி',\n        blockquote: 'மேற்கோள்',\n        pre: 'குறியீடு',\n        h1: 'தலைப்பு 1',\n        h2: 'தலைப்பு 2',\n        h3: 'தலைப்பு 3',\n        h4: 'தலைப்பு 4',\n        h5: 'தலைப்பு 5',\n        h6: 'தலைப்பு 6',\n      },\n      lists: {\n        unordered: 'வரிசையிடாத',\n        ordered: 'வரிசையிட்ட',\n      },\n      options: {\n        help: 'உதவி',\n        fullscreen: 'முழுத்திரை',\n        codeview: 'நிரலாக்க காட்சி',\n      },\n      paragraph: {\n        paragraph: 'பத்தி',\n        outdent: 'வெளித்தள்ளு',\n        indent: 'உள்ளே தள்ளு',\n        left: 'இடது சீரமைப்பு',\n        center: 'நடு சீரமைப்பு',\n        right: 'வலது சீரமைப்பு',\n        justify: 'இருபுற சீரமைப்பு',\n      },\n      color: {\n        recent: 'அண்மை நிறம்',\n        more: 'மேலும்',\n        background: 'பின்புல நிறம்',\n        foreground: 'முன்புற நிறம்',\n        transparent: 'தெளிமையான',\n        setTransparent: 'தெளிமையாக்கு',\n        reset: 'மீட்டமைக்க',\n        resetToDefault: 'இயல்புநிலைக்கு மீட்டமை',\n      },\n      shortcut: {\n        shortcuts: 'குறுக்குவழி',\n        close: 'மூடு',\n        textFormatting: 'எழுத்து வடிவமைப்பு',\n        action: 'செயல்படுத்து',\n        paragraphFormatting: 'பத்தி வடிவமைப்பு',\n        documentStyle: 'ஆவண பாணி',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'மீளமை',\n        redo: 'மீண்டும்',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "size", "superscript", "subscript", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}