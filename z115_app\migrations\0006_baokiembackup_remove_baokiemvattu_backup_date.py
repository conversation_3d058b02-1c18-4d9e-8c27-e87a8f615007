# Generated by Django 5.0.7 on 2025-06-25 01:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('z115_app', '0005_baokiemvattu_backup_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='BaoKiemBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ngay', models.DateField()),
                ('ma_vlspp', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('ma_kho', models.Char<PERSON>ield(max_length=20)),
                ('ten_quy_cach', models.Char<PERSON>ield(max_length=200)),
                ('dung_vao_viec', models.Char<PERSON>ield(max_length=200)),
                ('don_vi_tinh', models.Char<PERSON>ield(max_length=50)),
                ('so_luong', models.FloatField(default=0.0)),
                ('nguoi_bao_kiem', models.Char<PERSON>ield(max_length=100)),
                ('noi_de_vat_tu', models.Char<PERSON><PERSON>(max_length=100)),
                ('so_hop_cach', models.FloatField(blank=True, default=0.0, null=True)),
                ('ket_qua_kiem_tra', models.CharField(blank=True, max_length=200)),
                ('ngay_tra_ket_qua', models.DateField(blank=True, null=True)),
                ('phong_b12_ky', models.CharField(blank=True, max_length=100)),
                ('phong_b3_ky', models.CharField(blank=True, max_length=100)),
                ('backup_date', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='baokiemvattu',
            name='backup_date',
        ),
    ]
