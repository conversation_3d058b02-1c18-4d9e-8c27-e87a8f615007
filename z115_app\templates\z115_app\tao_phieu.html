{% extends 'z115_app/cap_vat_tu_khu_a.html' %}
{% load static %}

{% block sub_content %}
<div class="container mt-4" style="font-family: 'Times New Roman', Times, serif; font-size: 14px;">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- FORM TẠO PHIẾU -->
    <div class="card border-primary mb-4">
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" id="createPhieuForm">
                {% csrf_token %}
                <div class="row mb-3 align-items-end">
                    <div class="col-md-4">
                        <h5 style="font-size: 14px; font-weight: bold; color: #0056b3;">NHÀ MÁY Z115</h5>
                        <h5 style="font-size: 14px; font-weight: bold; color: #0056b3;">PHÒNG VẬT TƯ</h5>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 style="font-size: 16px; font-weight: bold; color: #0056b3;">LỆNH TẠM CẤP VẬT TƯ PHỤC VỤ SẢN XUẤT</h4>
                    </div>
                    <div class="col-md-4 text-end">
                        <h5 style="font-size: 14px; font-weight: bold; color: #0056b3;">SỐ PHIẾU: {{ next_so_phieu }}</h5>
                    </div>
                </div>

                <div class="row mb-3 align-items-end">
                    <div class="col-md-4">
                        <label for="don_vi_nhan" class="form-label"><strong>Đơn vị nhận hàng:</strong></label>
                        <select name="don_vi_nhan" id="don_vi_nhan" class="form-select" required>
                            <option value="" disabled selected>-- Chọn đơn vị --</option>
                            <option value="PHÂN XƯỞNG A1">PHÂN XƯỞNG A1</option>
                            <option value="PHÂN XƯỞNG A2">PHÂN XƯỞNG A2</option>
                            <option value="PHÂN XƯỞNG A3">PHÂN XƯỞNG A3</option>
                            <option value="PHÂN XƯỞNG A4">PHÂN XƯỞNG A4</option>
                            <option value="PHÂN XƯỞNG A7">PHÂN XƯỞNG A7</option>
                            <option value="PHÂN XƯỞNG A12">PHÂN XƯỞNG A12</option>
                            <option value="PHÒNG B7">PHÒNG B7</option>
                        </select>
                        <div class="invalid-feedback">Vui lòng chọn đơn vị nhận.</div>
                    </div>
                    <div class="col-md-2">
                        <label for="kho" class="form-label"><strong>Kho:</strong></label>
                        <select name="kho" id="kho" class="form-select" required>
                            <option value="" disabled selected>-- Chọn kho --</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                        <div class="invalid-feedback">Vui lòng chọn kho.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="excel_file" class="form-label"><strong>Tải file Excel mẫu:</strong></label>
                        <div class="input-group">
                            <input type="file" name="excel_file" id="excel_file" class="form-control" accept=".xlsx, .xls" required>
                            <div class="invalid-feedback">Vui lòng chọn file Excel.</div>
                            <button type="button" class="btn btn-primary" id="createPhieuBtn">
                                <i class="fas fa-plus-circle me-2"></i>TẠO PHIẾU
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<form method="get" action="">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="from_date">TỪ NGÀY:</label>
                <input type="date" name="from_date" id="from_date" value="{% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}" class="form-control">
            </div>
            <div class="col-md-3">
                <label for="to_date">ĐẾN NGÀY:</label>
                <input type="date" name="to_date" id="to_date" value="{% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}" class="form-control">
            </div>
            <div class="col-md-2">
                <label> </label>
                <button type="submit" name="clear_date" value="1" class="btn btn-secondary w-100">BỎ LỌC THEO NGÀY</button>
            </div>
            <div class="col-md-4">
                <label for="search_ten_vat_tu">TÌM KIẾM TÊN VẬT TƯ:</label>
                <input type="text" name="search_ten_vat_tu" id="search_ten_vat_tu" value="{{ search_ten_vat_tu|default:'' }}" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="search_kho">TÌM KIẾM KHO:</label>
                <input type="text" name="search_kho" id="search_kho" value="{{ search_kho|default:'' }}" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="search_muc_dich">MỤC ĐÍCH SỬ DỤNG:</label>
                <input type="text" name="search_muc_dich" id="search_muc_dich" value="{{ search_muc_dich|default:'' }}" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="search_don_vi_nhan">ĐƠN VỊ NHẬN:</label>
                <input type="text" name="search_don_vi_nhan" id="search_don_vi_nhan" value="{{ search_don_vi_nhan|default:'' }}" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="search_so_phieu">SỐ PHIẾU:</label>
                <input type="text" name="search_so_phieu" id="search_so_phieu" value="{{ search_so_phieu|default:'' }}" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="search_trang_thai">TRẠNG THÁI:</label>
                <select name="search_trang_thai" id="search_trang_thai" class="form-control">
                    <option value="" {% if not search_trang_thai %}selected{% endif %}>Tất cả</option>
                    <option value="CHO_DMV_DUYET" {% if search_trang_thai == 'CHO_DMV_DUYET' %}selected{% endif %}>CHỜ ĐMV DUYỆT</option>
                    <option value="CHO_CH_DUYET" {% if search_trang_thai == 'CHO_CH_DUYET' %}selected{% endif %}>CHỜ CH DUYỆT</option>
                    <option value="DA_DUYET_CH" {% if search_trang_thai == 'DA_DUYET_CH' %}selected{% endif %}>CH ĐÃ DUYỆT</option>
                    <option value="CH_HUY_PHIEU" {% if search_trang_thai == 'CH_HUY_PHIEU' %}selected{% endif %}>CH HỦY PHIẾU</option>
                </select>
            </div>
            <div class="col-md-2">
                <label> </label>
                <button type="submit" class="btn btn-primary w-100">TÌM KIẾM</button>
            </div>
            <div class="col-md-2">
                <label> </label>
                <button type="submit" name="export_excel" value="1" class="btn btn-success w-100">XUẤT EXCEL</button>
            </div>
        </div>
    </form>

    <!-- DANH SÁCH PHIẾU -->
    <div class="card border-success mt-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i>DANH SÁCH PHIẾU CẤP VẬT TƯ</h5>
        </div>
        <div class="card-body">
            <div id="phieuList" class="list-group mb-4 scrollable-phieu-list">
    		{% if all_phieus %}
       		 {% for phieu in all_phieus %}
          	  <a href="#" class="list-group-item list-group-item-action phieu-link
   			{% if phieu.trang_thai == 'CHO_DMV_DUYET' or phieu.trang_thai == 'CHO_CH_DUYET' %} flashing-background {% elif phieu.trang_thai == 'DA_DUYET_CH' %} text-primary {% endif %}"
               		data-phieu-id="{{ phieu.id }}"
               		onclick="showPhieuDetails({{ phieu.id }})"
               		style="font-family: 'Times New Roman'; font-size: 16px;">
                	Phiếu số: {{ phieu.so_phieu }} | Đơn vị: {{ phieu.don_vi_nhan }} | Kho: {{ phieu.kho }} | Tài khoản tạo: {{ phieu.tai_khoan_tao }} | Ngày tạo: {{ phieu.ngay_tao|date:"d/m/Y" }} | Trạng thái: {{ phieu.get_trang_thai_display }}
           		 </a>
       		 {% endfor %}
    		{% else %}
        		<p class="text-center">Chưa có phiếu nào được tạo.</p>
   		 {% endif %}
	</div>

            <!-- CHI TIẾT PHIẾU -->
            <div id="phieuDetails" class="table-responsive" style="display: none;">
                <table class="table table-striped table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th>STT</th>
                            <th>Mục đích sử dụng</th>
                            <th>Tên vật tư và quy cách</th>
                            <th>ĐVT</th>
                            <th>Số lượng yêu cầu</th>
                            <th>Số lượng ĐMV duyệt</th>
                            <th>Trạng thái</th>
                            <th>Tài khoản ĐMV duyệt</th>
                            <th>Tài khoản CH duyệt</th>
                        </tr>
                    </thead>
                    <tbody id="phieuDetailsBody"></tbody>
                </table>
                <button class="btn btn-secondary mt-2" onclick="hidePhieuDetails()">Đóng</button>
            </div>
        </div>
    </div>
</div>
{% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_ten_vat_tu={{ search_ten_vat_tu|default:'' }}&search_kho={{ search_kho|default:'' }}&search_muc_dich={{ search_muc_dich|default:'' }}&search_don_vi_nhan={{ search_don_vi_nhan|default:'' }}&search_so_phieu={{ search_so_phieu|default:'' }}&search_trang_thai={{ search_trang_thai|default:'' }}">Trước</a></li>
                {% else %}
                    <li class="page-item disabled"><span class="page-link">Trước</span></li>
                {% endif %}
                {% for num in page_obj.paginator.page_range %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_ten_vat_tu={{ search_ten_vat_tu|default:'' }}&search_kho={{ search_kho|default:'' }}&search_muc_dich={{ search_muc_dich|default:'' }}&search_don_vi_nhan={{ search_don_vi_nhan|default:'' }}&search_so_phieu={{ search_so_phieu|default:'' }}&search_trang_thai={{ search_trang_thai|default:'' }}">{{ num }}</a>
                    </li>
                {% endfor %}
                {% if page_obj.has_next %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_ten_vat_tu={{ search_ten_vat_tu|default:'' }}&search_kho={{ search_kho|default:'' }}&search_muc_dich={{ search_muc_dich|default:'' }}&search_don_vi_nhan={{ search_don_vi_nhan|default:'' }}&search_so_phieu={{ search_so_phieu|default:'' }}&search_trang_thai={{ search_trang_thai|default:'' }}">Tiếp</a></li>
                {% else %}
                    <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}

    {% if request.user.username == 'hungpc892' %}
        <h4 class="mt-4">Quản lý dữ liệu</h4>
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="super_password">Mật khẩu:</label>
                    <input type="password" name="super_password" id="super_password" class="form-control">
                </div>
                <div class="col-md-2">
                    <button type="submit" name="reset_data" value="1" class="btn btn-danger w-100">RESET</button>
                </div>
                <div class="col-md-2">
                    <button type="submit" name="backup_data" value="1" class="btn btn-warning w-100">SAO LƯU</button>
                </div>
            </div>
        </form>
    {% endif %}

<!-- SCRIPT XỬ LÝ -->
<script>
    function showPhieuDetails(phieuId) {
        fetch(`/get_phieu_details_dmv/${phieuId}/`)
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('phieuDetailsBody');
                tbody.innerHTML = '';
                if (data.vat_tus && data.vat_tus.length > 0) {
                    data.vat_tus.forEach((vattu, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${vattu.muc_dich_su_dung || ''}</td>
                            <td>${vattu.ten_vat_tu || ''}</td>
                            <td>${vattu.don_vi_tinh || ''}</td>
                            <td>${vattu.so_luong_yeu_cau || ''}</td>
                            <td>${vattu.so_luong_duyet || ''}</td>
                            <td>${data.phieu.trang_thai_display || ''}</td>
                            <td>${data.dmv_username || ''}</td>
                            <td>${data.ch_username || ''}</td>
                        `;
                        tbody.appendChild(row);
                    });
                    document.getElementById('phieuDetails').style.display = 'block';
                }
            })
            .catch(error => console.error('Lỗi khi tải chi tiết phiếu:', error));
    }

    function hidePhieuDetails() {
        const phieuDetails = document.getElementById('phieuDetails');
        phieuDetails.style.display = 'none';
        document.getElementById('phieuDetailsBody').innerHTML = '';
    }

    document.querySelectorAll('.phieu-link').forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const phieuId = this.getAttribute('data-phieu-id');
            if (phieuId) {
                showPhieuDetails(phieuId);
            }
        });
    });

    // XỬ LÝ TẠO PHIẾU
    document.getElementById('createPhieuBtn').addEventListener('click', function () {
        const form = document.getElementById('createPhieuForm');
        const donViNhan = document.getElementById('don_vi_nhan').value;
        const kho = document.getElementById('kho').value;
        const excelFile = document.getElementById('excel_file').files[0];

        if (!donViNhan || !kho || !excelFile) {
            if (!donViNhan) document.getElementById('don_vi_nhan').classList.add('is-invalid');
            if (!kho) document.getElementById('kho').classList.add('is-invalid');
            if (!excelFile) document.getElementById('excel_file').classList.add('is-invalid');
            return;
        }

        form.submit();
    });

    ['don_vi_nhan', 'kho', 'excel_file'].forEach(id => {
        document.getElementById(id).addEventListener('change', function () {
            this.classList.remove('is-invalid');
        });
    });
</script>
<style>
    /* Danh sách có thanh cuộn */
    .scrollable-phieu-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ccc;
        padding-right: 5px;
    }

    /* Nền nhấp nháy đỏ nhẹ và mượt cho trạng thái chờ duyệt */
    .flashing-background {
        animation: flash-bg 3s ease-in-out infinite;
    }

    @keyframes flash-bg {
        0%   { background-color: #ffe5e5; }
        50%  { background-color: #ff9999; }
        100% { background-color: #ffe5e5; }
    }

    /* Style cơ bản cho các phiếu */
    .phieu-link {
        display: block;
        padding: 5px 10px;
        text-decoration: none;
        color: #212529;
        font-family: 'Times New Roman', Times, serif;
        font-size: 16px;
    }

    .phieu-link:hover {
        background-color: #f1f1f1;
        text-decoration: none;
    }

    /* Màu xanh dương cho phiếu đã duyệt */
    .text-primary {
        color: #0d6efd !important;
    }
</style>

{% endblock %}
