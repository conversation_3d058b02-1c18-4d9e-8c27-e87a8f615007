{"version": 3, "file": "lang/summernote-sv-SE.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,oBAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,aANF;AAOJC,QAAAA,aAAa,EAAE,cAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,aAFH;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,cAJP;AAKLC,QAAAA,aAAa,EAAE,wBALV;AAMLC,QAAAA,SAAS,EAAE,iBANN;AAOLC,QAAAA,UAAU,EAAE,eAPP;AAQLC,QAAAA,SAAS,EAAE,iBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,iBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,iBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,gBAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,WADF;AAELC,QAAAA,SAAS,EAAE,qBAFN;AAGLpB,QAAAA,MAAM,EAAE,kBAHH;AAILgB,QAAAA,GAAG,EAAE,qBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,aAFJ;AAGJuB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,UAJF;AAKJC,QAAAA,aAAa,EAAE,cALX;AAMJT,QAAAA,GAAG,EAAE,sCAND;AAOJU,QAAAA,eAAe,EAAE,0BAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,YADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,WAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,cADF;AAETC,QAAAA,OAAO,EAAE,eAFA;AAGTC,QAAAA,MAAM,EAAE,YAHC;AAITC,QAAAA,IAAI,EAAE,iBAJG;AAKTC,QAAAA,MAAM,EAAE,WALC;AAMTC,QAAAA,KAAK,EAAE,eANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,qBADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,kBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA/FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,eADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,iBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,qBALb;AAMRC,QAAAA,aAAa,EAAE,cANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sv-SE.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'sv-SE': {\n      font: {\n        bold: 'Fet',\n        italic: 'Kursiv',\n        underline: 'Understruken',\n        clear: 'Radera formatering',\n        height: 'Radavstånd',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Genomstruken',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Teckenstorlek',\n      },\n      image: {\n        image: 'Bild',\n        insert: 'Infoga bild',\n        resizeFull: 'Full storlek',\n        resizeHalf: 'Halv storlek',\n        resizeQuarter: 'En fjärdedel i storlek',\n        floatLeft: 'Vänsterjusterad',\n        floatRight: 'Högerjusterad',\n        floatNone: 'Ingen justering',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Dra en bild hit',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Välj från filer',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Länk till bild',\n        remove: 'Ta bort bild',\n        original: 'Original',\n      },\n      video: {\n        video: 'Filmklipp',\n        videoLink: 'Länk till filmklipp',\n        insert: 'Infoga filmklipp',\n        url: 'Länk till filmklipp',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Länk',\n        insert: 'Infoga länk',\n        unlink: 'Ta bort länk',\n        edit: 'Redigera',\n        textToDisplay: 'Visningstext',\n        url: 'Till vilken URL ska denna länk peka?',\n        openInNewWindow: 'Öppna i ett nytt fönster',\n        useProtocol: 'Använd standardprotokoll',\n      },\n      table: {\n        table: 'Tabell',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Infoga horisontell linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Kod',\n        h1: 'Rubrik 1',\n        h2: 'Rubrik 2',\n        h3: 'Rubrik 3',\n        h4: 'Rubrik 4',\n        h5: 'Rubrik 5',\n        h6: 'Rubrik 6',\n      },\n      lists: {\n        unordered: 'Punktlista',\n        ordered: 'Numrerad lista',\n      },\n      options: {\n        help: 'Hjälp',\n        fullscreen: 'Fullskärm',\n        codeview: 'HTML-visning',\n      },\n      paragraph: {\n        paragraph: 'Justera text',\n        outdent: 'Minska indrag',\n        indent: 'Öka indrag',\n        left: 'Vänsterjusterad',\n        center: 'Centrerad',\n        right: 'Högerjusterad',\n        justify: 'Justera text',\n      },\n      color: {\n        recent: 'Senast använda färg',\n        more: 'Fler färger',\n        background: 'Bakgrundsfärg',\n        foreground: 'Teckenfärg',\n        transparent: 'Genomskinlig',\n        setTransparent: 'Gör genomskinlig',\n        reset: 'Nollställ',\n        resetToDefault: 'Återställ till standard',\n      },\n      shortcut: {\n        shortcuts: 'Kortkommandon',\n        close: 'Stäng',\n        textFormatting: 'Textformatering',\n        action: 'Funktion',\n        paragraphFormatting: 'Avsnittsformatering',\n        documentStyle: 'Dokumentstil',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Ångra',\n        redo: 'Gör om',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}