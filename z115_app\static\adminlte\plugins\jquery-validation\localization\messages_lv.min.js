/*! jQuery Validation Plugin - v1.19.3 - 1/9/2021
 * https://jqueryvalidation.org/
 * Copyright (c) 2021 <PERSON><PERSON><PERSON>; Licensed MIT */
!function(a){"function"==typeof define&&define.amd?define(["jquery","../jquery.validate.min"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){return a.extend(a.validator.messages,{required:"Šis lauks ir obligāts.",remote:"<PERSON>ū<PERSON><PERSON>, pārbaudiet šo lauku.",email:"<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu e-pasta adresi.",url:"<PERSON>ū<PERSON><PERSON>, ievadiet derīgu URL adresi.",date:"<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu datumu.",dateISO:"<PERSON>ū<PERSON><PERSON>, ievadiet derīgu datumu (ISO).",number:"<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu numuru.",digits:"<PERSON><PERSON><PERSON><PERSON>, ievadiet tikai ciparus.",creditcard:"<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu kredītkartes numuru.",equalTo:"<PERSON><PERSON><PERSON><PERSON>, ievadiet to pašu vēlreiz.",extension:"Lūdzu, ievadiet vērtību ar derīgu paplašinājumu.",maxlength:a.validator.format("Lūdzu, ievadiet ne vairāk kā {0} rakstzīmes."),minlength:a.validator.format("Lūdzu, ievadiet vismaz {0} rakstzīmes."),rangelength:a.validator.format("Lūdzu ievadiet {0} līdz {1} rakstzīmes."),range:a.validator.format("Lūdzu, ievadiet skaitli no {0} līdz {1}."),max:a.validator.format("Lūdzu, ievadiet skaitli, kurš ir mazāks vai vienāds ar {0}."),min:a.validator.format("Lūdzu, ievadiet skaitli, kurš ir lielāks vai vienāds ar {0}.")}),a});