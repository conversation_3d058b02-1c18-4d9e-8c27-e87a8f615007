{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}Trang chủ - NHÀ MÁY Z115{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block sidebar_menu %}
{% include 'z115_app/components/sidebar.html' %}
{% endblock %}

{% block extra_css %}
<style>
    .info-box {
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        border-radius: .25rem;
        background-color: #fff;
        display: flex;
        margin-bottom: 1rem;
        min-height: 80px;
        padding: .5rem;
        position: relative;
        width: 100%;
    }
    .info-box .info-box-icon {
        border-radius: .25rem;
        align-items: center;
        display: flex;
        font-size: 1.875rem;
        justify-content: center;
        text-align: center;
        width: 70px;
    }
    .info-box .info-box-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        line-height: 1.8;
        flex: 1;
        padding: 0 10px;
    }
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .feature-card {
        transition: transform 0.3s ease;
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Card -->
<div class="welcome-card">
    <h1 class="mb-3">
        <i class="fas fa-home mr-2"></i>
        WELCOME WEBSITE PHÒNG VẬT TƯ
    </h1>
    <h3>Xin chào, {{ user.get_full_name|default:user.username }}!</h3>
    <p class="mb-0">Hệ thống quản lý vật tư - Nhà máy Z115</p>
</div>

<!-- Info boxes -->
<div class="row">
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1"><i class="fas fa-clipboard-check"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Báo kiểm vật tư</span>
                <span class="info-box-number">
                    <small>Quản lý</small>
                </span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-warehouse"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Quản lý kho</span>
                <span class="info-box-number">
                    <small>Tồn kho</small>
                </span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-success elevation-1"><i class="fas fa-file-alt"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Phiếu</span>
                <span class="info-box-number">
                    <small>Quản lý</small>
                </span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-industry"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Thành phẩm</span>
                <span class="info-box-number">
                    <small>Sản xuất</small>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<div class="row">
    <div class="col-md-8">
        <!-- Quick Access -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tachometer-alt mr-1"></i>
                    Truy cập nhanh
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if perms.z115_app.view_baokiem %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-primary">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-clipboard-check fa-3x mb-3"></i>
                                <h5>BÁO KIỂM VẬT TƯ PVSX</h5>
                                <a href="{% url 'bao_kiem_vat_tu' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_kehoach %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-success">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                <h5>BÁO CÁO TỒN KHO B3</h5>
                                <a href="{% url 'bao_cao_ton_kho_b3' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_cap_vat_tu_khu_a %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-warning">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-truck-loading fa-3x mb-3"></i>
                                <h5>CẤP VẬT TƯ PVSX KHU A</h5>
                                <a href="{% url 'cap_vat_tu_khu_a' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_nhap_kho_thanh_pham_khu_a %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-info">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-warehouse fa-3x mb-3"></i>
                                <h5>NHẬP KHO THÀNH PHẨM KHU A</h5>
                                <a href="{% url 'nhap_kho_thanh_pham_khu_a' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Welcome Image -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-image mr-1"></i>
                    Hình ảnh
                </h3>
            </div>
            <div class="card-body text-center">
                <img src="{% static 'images/trang_chu.jpg' %}"
                     alt="Hình nền trang chủ"
                     class="img-fluid rounded"
                     style="max-height: 300px; object-fit: cover;">
            </div>
        </div>

        <!-- System Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-1"></i>
                    Thông tin hệ thống
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Phiên bản:</strong> 1.0.0</p>
                <p><strong>Người dùng:</strong> {{ user.username }}</p>
                <p><strong>Nhóm quyền:</strong>
                    {% for group in user.groups.all %}
                        <span class="badge badge-primary">{{ group.name }}</span>
                    {% empty %}
                        <span class="badge badge-secondary">Chưa có nhóm</span>
                    {% endfor %}
                </p>
                <p><strong>Đăng nhập lần cuối:</strong> {{ user.last_login|date:"d/m/Y H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}