{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}Trang chủ - NHÀ MÁY Z115{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}



{% block extra_css %}
<style>
    .info-box {
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        border-radius: .25rem;
        background-color: #fff;
        display: flex;
        margin-bottom: 1rem;
        min-height: 80px;
        padding: .5rem;
        position: relative;
        width: 100%;
    }
    .info-box .info-box-icon {
        border-radius: .25rem;
        align-items: center;
        display: flex;
        font-size: 1.875rem;
        justify-content: center;
        text-align: center;
        width: 70px;
    }
    .info-box .info-box-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        line-height: 1.8;
        flex: 1;
        padding: 0 10px;
    }
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .feature-card {
        transition: transform 0.3s ease;
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }
    .table th {
        border-top: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Card -->
<div class="welcome-card">
    <h1 class="mb-3">
        <i class="fas fa-home mr-2"></i>
        WELCOME WEBSITE PHÒNG VẬT TƯ
    </h1>
    <h3>Xin chào, {{ user.get_full_name|default:user.username }}!</h3>
    <p class="mb-0">Hệ thống quản lý vật tư - Nhà máy Z115</p>
</div>

<!-- Info boxes -->
<div class="row">
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-info elevation-1"><i class="fas fa-file-alt"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Tổng số phiếu</span>
                <span class="info-box-number">{{ total_phieu }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-clock"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Chờ duyệt</span>
                <span class="info-box-number">{{ phieu_cho_duyet }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-success elevation-1"><i class="fas fa-check-circle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Đã hoàn thành</span>
                <span class="info-box-number">{{ phieu_hoan_thanh }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-times-circle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Đã hủy</span>
                <span class="info-box-number">{{ phieu_huy }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Thống kê chi tiết -->
<div class="row">
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box">
            <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-clipboard-check"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Báo kiểm vật tư</span>
                <span class="info-box-number">{{ total_bao_kiem }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-secondary elevation-1"><i class="fas fa-warehouse"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Tồn kho B3</span>
                <span class="info-box-number">{{ total_ton_kho }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-teal elevation-1"><i class="fas fa-users"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Người dùng</span>
                <span class="info-box-number">{{ user.username }}</span>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-md-3">
        <div class="info-box mb-3">
            <span class="info-box-icon bg-indigo elevation-1"><i class="fas fa-calendar"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Hôm nay</span>
                <span class="info-box-number">
                    <small>{{ "now"|date:"d/m/Y" }}</small>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<div class="row">
    <div class="col-md-8">
        <!-- Phiếu gần đây -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt mr-1"></i>
                    Phiếu gần đây
                </h3>
                <div class="card-tools">
                    <a href="{% url 'tao_phieu' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Tạo phiếu mới
                    </a>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th>Số phiếu</th>
                            <th>Đơn vị nhận</th>
                            <th>Kho</th>
                            <th>Ngày tạo</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for phieu in phieu_gan_day %}
                        <tr>
                            <td><strong>{{ phieu.so_phieu }}</strong></td>
                            <td>{{ phieu.don_vi_nhan }}</td>
                            <td><span class="badge badge-secondary">{{ phieu.kho }}</span></td>
                            <td>{{ phieu.ngay_tao|date:"d/m/Y H:i" }}</td>
                            <td>
                                {% if phieu.trang_thai == 'TAO_PHIEU' %}
                                    <span class="badge badge-secondary">Tạo phiếu</span>
                                {% elif phieu.trang_thai == 'CHO_DMV_DUYET' %}
                                    <span class="badge badge-warning">Chờ ĐMV duyệt</span>
                                {% elif phieu.trang_thai == 'CHO_CH_DUYET' %}
                                    <span class="badge badge-info">Chờ CH duyệt</span>
                                {% elif phieu.trang_thai == 'DA_DUYET_CH' %}
                                    <span class="badge badge-success">Đã duyệt</span>
                                {% elif phieu.trang_thai == 'CH_HUY_PHIEU' %}
                                    <span class="badge badge-danger">Đã hủy</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editPhieuStatus({{ phieu.id }}, '{{ phieu.trang_thai }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewPhieuDetails({{ phieu.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">Chưa có phiếu nào</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Access -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tachometer-alt mr-1"></i>
                    Truy cập nhanh
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if perms.z115_app.view_baokiem %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-primary">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                                <h6>BÁO KIỂM VẬT TƯ PVSX</h6>
                                <a href="{% url 'bao_kiem_vat_tu' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_kehoach %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-success">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <h6>BÁO CÁO TỒN KHO B3</h6>
                                <a href="{% url 'bao_cao_ton_kho_b3' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_cap_vat_tu_khu_a %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-warning">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-truck-loading fa-2x mb-2"></i>
                                <h6>CẤP VẬT TƯ PVSX KHU A</h6>
                                <a href="{% url 'cap_vat_tu_khu_a' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if perms.z115_app.view_nhap_kho_thanh_pham_khu_a %}
                    <div class="col-md-6 mb-3">
                        <div class="card feature-card bg-info">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-warehouse fa-2x mb-2"></i>
                                <h6>NHẬP KHO THÀNH PHẨM KHU A</h6>
                                <a href="{% url 'nhap_kho_thanh_pham_khu_a' %}" class="btn btn-light btn-sm">Truy cập</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Biểu đồ thống kê phiếu -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-pie mr-1"></i>
                    Thống kê phiếu theo trạng thái
                </h3>
            </div>
            <div class="card-body">
                <canvas id="phieuChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- System Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-1"></i>
                    Thông tin hệ thống
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Phiên bản:</strong> 1.0.0</p>
                <p><strong>Người dùng:</strong> {{ user.username }}</p>
                <p><strong>Nhóm quyền:</strong>
                    {% for group in user.groups.all %}
                        <span class="badge badge-primary">{{ group.name }}</span>
                    {% empty %}
                        <span class="badge badge-secondary">Chưa có nhóm</span>
                    {% endfor %}
                </p>
                <p><strong>Đăng nhập lần cuối:</strong> {{ user.last_login|date:"d/m/Y H:i" }}</p>
            </div>
        </div>

        <!-- Welcome Image -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-image mr-1"></i>
                    Hình ảnh
                </h3>
            </div>
            <div class="card-body text-center">
                <img src="{% static 'images/trang_chu.jpg' %}"
                     alt="Hình nền trang chủ"
                     class="img-fluid rounded"
                     style="max-height: 200px; object-fit: cover;">
            </div>
        </div>
    </div>
</div>

<!-- Modal sửa trạng thái phiếu -->
<div class="modal fade" id="editStatusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Sửa trạng thái phiếu</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editStatusForm">
                    {% csrf_token %}
                    <input type="hidden" id="phieu_id" name="phieu_id">
                    <div class="form-group">
                        <label for="trang_thai">Trạng thái mới:</label>
                        <select class="form-control" id="trang_thai" name="trang_thai" required>
                            <option value="TAO_PHIEU">Tạo phiếu</option>
                            <option value="CHO_DMV_DUYET">Chờ ĐMV duyệt</option>
                            <option value="CHO_CH_DUYET">Chờ Chỉ huy duyệt</option>
                            <option value="DA_DUYET_CH">Chỉ huy đã duyệt</option>
                            <option value="CH_HUY_PHIEU">Chỉ huy huỷ phiếu</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="ghi_chu">Ghi chú:</label>
                        <textarea class="form-control" id="ghi_chu" name="ghi_chu" rows="3" placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="savePhieuStatus()">Lưu thay đổi</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal xem chi tiết phiếu -->
<div class="modal fade" id="viewDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Chi tiết phiếu</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="phieuDetailsContent">
                <!-- Nội dung sẽ được load bằng AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="{% static 'adminlte/plugins/chart.js/Chart.min.js' %}"></script>
<script>
// Dữ liệu cho biểu đồ
var phieuData = {
    labels: [
        {% for stat in phieu_stats %}
            {% if stat.trang_thai == 'TAO_PHIEU' %}'Tạo phiếu'
            {% elif stat.trang_thai == 'CHO_DMV_DUYET' %}'Chờ ĐMV duyệt'
            {% elif stat.trang_thai == 'CHO_CH_DUYET' %}'Chờ CH duyệt'
            {% elif stat.trang_thai == 'DA_DUYET_CH' %}'Đã duyệt'
            {% elif stat.trang_thai == 'CH_HUY_PHIEU' %}'Đã hủy'
            {% endif %}{% if not forloop.last %},{% endif %}
        {% endfor %}
    ],
    datasets: [{
        data: [
            {% for stat in phieu_stats %}
                {{ stat.count }}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        backgroundColor: [
            '#6c757d', // Tạo phiếu - gray
            '#ffc107', // Chờ ĐMV duyệt - warning
            '#17a2b8', // Chờ CH duyệt - info
            '#28a745', // Đã duyệt - success
            '#dc3545'  // Đã hủy - danger
        ]
    }]
};

// Tạo biểu đồ
var ctx = document.getElementById('phieuChart').getContext('2d');
var phieuChart = new Chart(ctx, {
    type: 'doughnut',
    data: phieuData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        legend: {
            position: 'bottom'
        }
    }
});

// Hàm sửa trạng thái phiếu
function editPhieuStatus(phieuId, currentStatus) {
    document.getElementById('phieu_id').value = phieuId;
    document.getElementById('trang_thai').value = currentStatus;
    document.getElementById('ghi_chu').value = '';
    $('#editStatusModal').modal('show');
}

// Hàm xem chi tiết phiếu
function viewPhieuDetails(phieuId) {
    $('#phieuDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
    $('#viewDetailsModal').modal('show');

    // AJAX call để lấy chi tiết phiếu
    $.get('/api/phieu-details/' + phieuId + '/', function(response) {
        if (response.success) {
            $('#phieuDetailsContent').html(response.html);
        } else {
            $('#phieuDetailsContent').html('<div class="alert alert-danger">' + response.message + '</div>');
        }
    }).fail(function() {
        $('#phieuDetailsContent').html('<div class="alert alert-danger">Không thể tải chi tiết phiếu</div>');
    });
}

// Hàm lưu trạng thái phiếu
function savePhieuStatus() {
    var formData = {
        'phieu_id': document.getElementById('phieu_id').value,
        'trang_thai': document.getElementById('trang_thai').value,
        'ghi_chu': document.getElementById('ghi_chu').value,
        'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
    };

    $.post('/api/update-phieu-status/', formData, function(response) {
        if (response.success) {
            $('#editStatusModal').modal('hide');
            // Reload trang để cập nhật dữ liệu
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + response.message);
        }
    }).fail(function() {
        alert('Không thể cập nhật trạng thái phiếu');
    });
}
</script>
{% endblock %}