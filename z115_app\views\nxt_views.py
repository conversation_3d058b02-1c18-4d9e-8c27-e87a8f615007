# views/nxt_views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def XNT_kho_xi_nghiep_1(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to XNT_kho_xi_nghiep_1.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")
    return render(request, 'z115_app/XNT_kho_xi_nghiep_1.html')

@login_required
def vat_tu_pvsx(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to vat_tu_pvsx.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")
    return render(request, 'z115_app/vat_tu_pvsx.html')

@login_required
def thanh_pham(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to thanh_pham.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")
    return render(request, 'z115_app/thanh_pham.html')