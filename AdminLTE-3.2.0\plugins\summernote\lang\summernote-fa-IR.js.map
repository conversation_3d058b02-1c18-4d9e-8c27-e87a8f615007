{"version": 3, "file": "lang/summernote-fa-IR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,MADF;AAEJC,QAAAA,MAAM,EAAE,OAFJ;AAGJC,QAAAA,SAAS,EAAE,SAHP;AAIJC,QAAAA,KAAK,EAAE,oBAJH;AAKJC,QAAAA,MAAM,EAAE,aALJ;AAMJC,QAAAA,IAAI,EAAE,UANF;AAOJC,QAAAA,aAAa,EAAE,QAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,iBAFH;AAGLC,QAAAA,UAAU,EAAE,wBAHP;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,aAAa,EAAE,0BALV;AAMLC,QAAAA,SAAS,EAAE,eANN;AAOLC,QAAAA,UAAU,EAAE,iBAPP;AAQLC,QAAAA,SAAS,EAAE,cARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,yBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,wBAfZ;AAgBLC,QAAAA,eAAe,EAAE,sBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,YAlBA;AAmBLC,QAAAA,MAAM,EAAE,WAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,cAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,UAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,kCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,WAAW,EAAE,kBAFR;AAGLC,QAAAA,WAAW,EAAE,mBAHR;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,UANH;AAOLC,QAAAA,MAAM,EAAE,UAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,OAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,IAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,iBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,UAAU,EAAE,iBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,eAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,QAJG;AAKTC,QAAAA,MAAM,EAAE,UALC;AAMTC,QAAAA,KAAK,EAAE,UANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,uBADH;AAELC,QAAAA,IAAI,EAAE,WAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,SAJP;AAKLC,QAAAA,WAAW,EAAE,QALR;AAMLC,QAAAA,cAAc,EAAE,mBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,kBADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,UAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,eALb;AAMRC,QAAAA,aAAa,EAAE,WANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,iBADf;AAEJ,gBAAQ,2BAFJ;AAGJ,gBAAQ,iCAHJ;AAIJ,eAAO,IAJH;AAKJ,iBAAS,QALL;AAMJ,gBAAQ,mBANJ;AAOJ,kBAAU,mBAPN;AAQJ,qBAAa,wBART;AASJ,yBAAiB,uBATb;AAUJ,wBAAgB,mBAVZ;AAWJ,uBAAe,QAXX;AAYJ,yBAAiB,SAZb;AAaJ,wBAAgB,UAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,yBAfnB;AAgBJ,6BAAqB,sBAhBjB;AAiBJ,mBAAW,sBAjBP;AAkBJ,kBAAU,+BAlBN;AAmBJ,sBAAc,0BAnBV;AAoBJ,oBAAY,2BApBR;AAqBJ,oBAAY,2BArBR;AAsBJ,oBAAY,2BAtBR;AAuBJ,oBAAY,2BAvBR;AAwBJ,oBAAY,2BAxBR;AAyBJ,oBAAY,2BAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,aADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fa-IR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'fa-IR': {\n      font: {\n        bold: 'درشت',\n        italic: 'خمیده',\n        underline: 'میان خط',\n        clear: 'پاک کردن فرمت فونت',\n        height: 'فاصله ی خطی',\n        name: 'اسم فونت',\n        strikethrough: 'Strike',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'اندازه ی فونت',\n      },\n      image: {\n        image: 'تصویر',\n        insert: 'وارد کردن تصویر',\n        resizeFull: 'تغییر به اندازه ی کامل',\n        resizeHalf: 'تغییر به اندازه نصف',\n        resizeQuarter: 'تغییر به اندازه یک چهارم',\n        floatLeft: 'چسباندن به چپ',\n        floatRight: 'چسباندن به راست',\n        floatNone: 'بدون چسبندگی',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'یک تصویر را اینجا بکشید',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'فایل ها را انتخاب کنید',\n        maximumFileSize: 'حداکثر اندازه پرونده',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'آدرس تصویر',\n        remove: 'حذف تصویر',\n        original: 'Original',\n      },\n      video: {\n        video: 'ویدیو',\n        videoLink: 'لینک ویدیو',\n        insert: 'افزودن ویدیو',\n        url: 'آدرس ویدیو ؟',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion یا Youku)',\n      },\n      link: {\n        link: 'لینک',\n        insert: 'اضافه کردن لینک',\n        unlink: 'حذف لینک',\n        edit: 'ویرایش',\n        textToDisplay: 'متن جهت نمایش',\n        url: 'این لینک به چه آدرسی باید برود ؟',\n        openInNewWindow: 'در یک پنجره ی جدید باز شود',\n      },\n      table: {\n        table: 'جدول',\n        addRowAbove: 'افزودن ردیف بالا',\n        addRowBelow: 'افزودن ردیف پایین',\n        addColLeft: 'افزودن ستون چپ',\n        addColRight: 'افزودن ستون راست',\n        delRow: 'حذف ردیف',\n        delCol: 'حذف ستون',\n        delTable: 'حذف جدول',\n      },\n      hr: {\n        insert: 'افزودن خط افقی',\n      },\n      style: {\n        style: 'استیل',\n        p: 'نرمال',\n        blockquote: 'نقل قول',\n        pre: 'کد',\n        h1: 'سرتیتر 1',\n        h2: 'سرتیتر 2',\n        h3: 'سرتیتر 3',\n        h4: 'سرتیتر 4',\n        h5: 'سرتیتر 5',\n        h6: 'سرتیتر 6',\n      },\n      lists: {\n        unordered: 'لیست غیر ترتیبی',\n        ordered: 'لیست ترتیبی',\n      },\n      options: {\n        help: 'راهنما',\n        fullscreen: 'نمایش تمام صفحه',\n        codeview: 'مشاهده ی کد',\n      },\n      paragraph: {\n        paragraph: 'پاراگراف',\n        outdent: 'کاهش تو رفتگی',\n        indent: 'افزایش تو رفتگی',\n        left: 'چپ چین',\n        center: 'میان چین',\n        right: 'راست چین',\n        justify: 'بلوک چین',\n      },\n      color: {\n        recent: 'رنگ اخیرا استفاده شده',\n        more: 'رنگ بیشتر',\n        background: 'رنگ پس زمینه',\n        foreground: 'رنگ متن',\n        transparent: 'بی رنگ',\n        setTransparent: 'تنظیم حالت بی رنگ',\n        reset: 'بازنشاندن',\n        resetToDefault: 'حالت پیش فرض',\n      },\n      shortcut: {\n        shortcuts: 'دکمه های میان بر',\n        close: 'بستن',\n        textFormatting: 'فرمت متن',\n        action: 'عملیات',\n        paragraphFormatting: 'فرمت پاراگراف',\n        documentStyle: 'استیل سند',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'افزودن پاراگراف',\n        'undo': 'آخرین فرمان را لغو می کند',\n        'redo': 'دستور آخر را دوباره اجرا می کند',\n        'tab': 'تب',\n        'untab': 'لغو تب',\n        'bold': 'استایل ضخیم میدهد',\n        'italic': 'استایل مورب میدهد',\n        'underline': 'استایل زیرخط دار میدهد',\n        'strikethrough': 'استایل خط خورده میدهد',\n        'removeFormat': 'حذف همه استایل ها',\n        'justifyLeft': 'چپ چین',\n        'justifyCenter': 'وسط چین',\n        'justifyRight': 'راست چین',\n        'justifyFull': 'چینش در کل عرض',\n        'insertUnorderedList': 'تغییر بع لیست غیرترتیبی',\n        'insertOrderedList': 'تغییر بع لیست ترتیبی',\n        'outdent': 'گذر از پاراگراف فعلی',\n        'indent': 'قرارگیری بر روی پاراگراف جاری',\n        'formatPara': 'تغییر فرمت متن به تگ <p>',\n        'formatH1': 'تغییر فرمت متن به تگ <h1>',\n        'formatH2': 'تغییر فرمت متن به تگ <h2>',\n        'formatH3': 'تغییر فرمت متن به تگ <h3>',\n        'formatH4': 'تغییر فرمت متن به تگ <h4>',\n        'formatH5': 'تغییر فرمت متن به تگ <h5>',\n        'formatH6': 'تغییر فرمت متن به تگ <h6>',\n        'insertHorizontalRule': 'وارد کردن به صورت افقی',\n        'linkDialog.show': 'نمایش پیام لینک',\n      },\n      history: {\n        undo: 'واچیدن',\n        redo: 'بازچیدن',\n      },\n      specialChar: {\n        specialChar: 'کاراکتر خاص',\n        select: 'انتخاب کاراکتر خاص',\n      },      \n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}