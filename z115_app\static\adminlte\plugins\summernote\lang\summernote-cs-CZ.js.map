{"version": 3, "file": "lang/summernote-cs-CZ.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,sBAJH;AAKJC,QAAAA,MAAM,EAAE,aALJ;AAMJC,QAAAA,aAAa,EAAE,aANX;AAOJC,QAAAA,IAAI,EAAE;AAPF,OADC;AAUPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,oBAJP;AAKLC,QAAAA,aAAa,EAAE,oBALV;AAMLC,QAAAA,SAAS,EAAE,gBANN;AAOLC,QAAAA,UAAU,EAAE,iBAPP;AAQLC,QAAAA,SAAS,EAAE,kBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,YAVR;AAWLC,QAAAA,cAAc,EAAE,cAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,wBAbV;AAcLC,QAAAA,SAAS,EAAE,8BAdN;AAeLC,QAAAA,eAAe,EAAE,eAfZ;AAgBLC,QAAAA,GAAG,EAAE,aAhBA;AAiBLC,QAAAA,MAAM,EAAE,iBAjBH;AAkBLC,QAAAA,QAAQ,EAAE;AAlBL,OAVA;AA8BPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,aAFN;AAGLlB,QAAAA,MAAM,EAAE,cAHH;AAILc,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OA9BA;AAqCPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJpB,QAAAA,MAAM,EAAE,gBAFJ;AAGJqB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,kCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OArCC;AA8CPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,kBAFR;AAGLC,QAAAA,WAAW,EAAE,kBAHR;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,WAAW,EAAE,uBALR;AAMLC,QAAAA,MAAM,EAAE,cANH;AAOLC,QAAAA,MAAM,EAAE,gBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OA9CA;AAwDPC,MAAAA,EAAE,EAAE;AACFjC,QAAAA,MAAM,EAAE;AADN,OAxDG;AA2DPkC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,UAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OA3DA;AAuEPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,kBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OAvEA;AA2EPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,UAAU,EAAE,gBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OA3EF;AAgFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,kBAHC;AAITC,QAAAA,IAAI,EAAE,iBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAhFJ;AAyFPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,sBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE,iBARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OAzFA;AAoGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,mBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,mBAHR;AAIRC,QAAAA,MAAM,EAAE,MAJA;AAKRC,QAAAA,mBAAmB,EAAE,sBALb;AAMRC,QAAAA,aAAa,EAAE;AANP,OApGH;AA4GP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,iBADf;AAEJ,gBAAQ,wBAFJ;AAGJ,gBAAQ,0BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,gBANJ;AAOJ,kBAAU,kBAPN;AAQJ,qBAAa,qBART;AASJ,yBAAiB,sBATb;AAUJ,wBAAgB,yBAVZ;AAWJ,uBAAe,0BAXX;AAYJ,yBAAiB,6BAZb;AAaJ,wBAAgB,2BAbZ;AAcJ,uBAAe,6BAdX;AAeJ,+BAAuB,4BAfnB;AAgBJ,6BAAqB,0BAhBjB;AAiBJ,mBAAW,sCAjBP;AAkBJ,kBAAU,2BAlBN;AAmBJ,sBAAc,yDAnBV;AAoBJ,oBAAY,iDApBR;AAqBJ,oBAAY,iDArBR;AAsBJ,oBAAY,iDAtBR;AAuBJ,oBAAY,iDAvBR;AAwBJ,oBAAY,iDAxBR;AAyBJ,oBAAY,iDAzBR;AA0BJ,gCAAwB,0BA1BpB;AA2BJ,2BAAmB;AA3Bf,OA5GC;AAyIP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAzIF;AA6IPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,iBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AA7IN;AADiB,GAA5B;AAoJD,CArJD,EAqJGC,MArJH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-cs-CZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'cs-CZ': {\n      font: {\n        bold: 'Tučn<PERSON>',\n        italic: '<PERSON><PERSON><PERSON><PERSON>',\n        underline: 'Podtržené',\n        clear: 'Odstranit styl písma',\n        height: 'Výška řádku',\n        strikethrough: 'Přeškrtnuté',\n        size: 'Velikost písma',\n      },\n      image: {\n        image: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        insert: 'Vložit obrázek',\n        resizeFull: 'Původní velikost',\n        resizeHalf: 'Poloviční velikost',\n        resizeQuarter: 'Čtvrteční velikost',\n        floatLeft: 'Umístit doleva',\n        floatRight: 'Umístit doprava',\n        floatNone: 'Neobtékat textem',\n        shapeRounded: 'Tvar: zaoblený',\n        shapeCircle: 'Tvar: kruh',\n        shapeThumbnail: 'Tvar: náhled',\n        shapeNone: 'Tvar: ž<PERSON><PERSON><PERSON>',\n        dragImageHere: '<PERSON><PERSON><PERSON><PERSON>hnout sem obrázek',\n        dropImage: '<PERSON><PERSON>etáhnout obrázek nebo text',\n        selectFromFiles: 'Vy<PERSON>t soubor',\n        url: 'URL obrázku',\n        remove: 'Odebrat obrázek',\n        original: 'Originál',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Odkaz videa',\n        insert: 'Vložit video',\n        url: 'URL videa?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion nebo Youku)',\n      },\n      link: {\n        link: 'Odkaz',\n        insert: 'Vytvořit odkaz',\n        unlink: 'Zrušit odkaz',\n        edit: 'Upravit',\n        textToDisplay: 'Zobrazovaný text',\n        url: 'Na jaké URL má tento odkaz vést?',\n        openInNewWindow: 'Otevřít v novém okně',\n      },\n      table: {\n        table: 'Tabulka',\n        addRowAbove: 'Přidat řádek nad',\n        addRowBelow: 'Přidat řádek pod',\n        addColLeft: 'Přidat sloupec vlevo',\n        addColRight: 'Přidat sloupec vpravo',\n        delRow: 'Smazat řádek',\n        delCol: 'Smazat sloupec',\n        delTable: 'Smazat tabulku',\n      },\n      hr: {\n        insert: 'Vložit vodorovnou čáru',\n      },\n      style: {\n        style: 'Styl',\n        p: 'Normální',\n        blockquote: 'Citace',\n        pre: 'Kód',\n        h1: 'Nadpis 1',\n        h2: 'Nadpis 2',\n        h3: 'Nadpis 3',\n        h4: 'Nadpis 4',\n        h5: 'Nadpis 5',\n        h6: 'Nadpis 6',\n      },\n      lists: {\n        unordered: 'Odrážkový seznam',\n        ordered: 'Číselný seznam',\n      },\n      options: {\n        help: 'Nápověda',\n        fullscreen: 'Celá obrazovka',\n        codeview: 'HTML kód',\n      },\n      paragraph: {\n        paragraph: 'Odstavec',\n        outdent: 'Zvětšit odsazení',\n        indent: 'Zmenšit odsazení',\n        left: 'Zarovnat doleva',\n        center: 'Zarovnat na střed',\n        right: 'Zarovnat doprava',\n        justify: 'Zarovnat oboustranně',\n      },\n      color: {\n        recent: 'Aktuální barva',\n        more: 'Další barvy',\n        background: 'Barva pozadí',\n        foreground: 'Barva písma',\n        transparent: 'Průhlednost',\n        setTransparent: 'Nastavit průhlednost',\n        reset: 'Obnovit',\n        resetToDefault: 'Obnovit výchozí',\n        cpSelect: 'Vybrat',\n      },\n      shortcut: {\n        shortcuts: 'Klávesové zkratky',\n        close: 'Zavřít',\n        textFormatting: 'Formátování textu',\n        action: 'Akce',\n        paragraphFormatting: 'Formátování odstavce',\n        documentStyle: 'Styl dokumentu',\n      },\n      help: {\n        'insertParagraph': 'Vložit odstavec',\n        'undo': 'Vrátit poslední příkaz',\n        'redo': 'Opakovat poslední příkaz',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Nastavit tučně',\n        'italic': 'Nastavit kurzívu',\n        'underline': 'Nastavit podtrhnutí',\n        'strikethrough': 'Nastavit přeškrtnutí',\n        'removeFormat': 'Ostranit nastavený styl',\n        'justifyLeft': 'Nastavit zarovnání vlevo',\n        'justifyCenter': 'Nastavit zarovnání na střed',\n        'justifyRight': 'Nastavit zarovnání vpravo',\n        'justifyFull': 'Nastavit zarovnání do bloku',\n        'insertUnorderedList': 'Aplikovat odrážkový seznam',\n        'insertOrderedList': 'Aplikovat číselný seznam',\n        'outdent': 'Zmenšit odsazení aktuálního odstavec',\n        'indent': 'Odsadit aktuální odstavec',\n        'formatPara': 'Změnit formátování aktuálního bloku na odstavec (P tag)',\n        'formatH1': 'Změnit formátování aktuálního bloku na Nadpis 1',\n        'formatH2': 'Změnit formátování aktuálního bloku na Nadpis 2',\n        'formatH3': 'Změnit formátování aktuálního bloku na Nadpis 3',\n        'formatH4': 'Změnit formátování aktuálního bloku na Nadpis 4',\n        'formatH5': 'Změnit formátování aktuálního bloku na Nadpis 5',\n        'formatH6': 'Změnit formátování aktuálního bloku na Nadpis 6',\n        'insertHorizontalRule': 'Vložit horizontální čáru',\n        'linkDialog.show': 'Zobrazit dialog pro odkaz',\n      },\n      history: {\n        undo: 'Krok vzad',\n        redo: 'Krok vpřed',\n      },\n      specialChar: {\n        specialChar: 'SPECIÁLNÍ ZNAKY',\n        select: 'Vyberte speciální znaky',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}