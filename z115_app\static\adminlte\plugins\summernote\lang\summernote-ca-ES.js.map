{"version": 3, "file": "lang/summernote-ca-ES.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,wBAJH;AAKJC,QAAAA,MAAM,EAAE,iBALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,SAAS,EAAE,UARP;AASJC,QAAAA,WAAW,EAAE,YATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,+BAHP;AAILC,QAAAA,UAAU,EAAE,2BAJP;AAKLC,QAAAA,aAAa,EAAE,0BALV;AAMLC,QAAAA,SAAS,EAAE,uBANN;AAOLC,QAAAA,UAAU,EAAE,oBAPP;AAQLC,QAAAA,SAAS,EAAE,YARN;AASLC,QAAAA,YAAY,EAAE,kBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,aAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,oCAbV;AAcLC,QAAAA,SAAS,EAAE,sCAdN;AAeLC,QAAAA,eAAe,EAAE,6BAfZ;AAgBLC,QAAAA,eAAe,EAAE,yBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,2CAjBjB;AAkBLC,QAAAA,GAAG,EAAE,kBAlBA;AAmBLC,QAAAA,MAAM,EAAE,iBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLpB,QAAAA,MAAM,EAAE,eAHH;AAILgB,QAAAA,GAAG,EAAE,gBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,eAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,kCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,MAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,SALC;AAMLC,QAAAA,EAAE,EAAE,SANC;AAOLC,QAAAA,EAAE,EAAE,SAPC;AAQLC,QAAAA,EAAE,EAAE,SARC;AASLC,QAAAA,EAAE,EAAE,SATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,qBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,kBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,eAHC;AAITC,QAAAA,IAAI,EAAE,uBAJG;AAKTC,QAAAA,MAAM,EAAE,gBALC;AAMTC,QAAAA,KAAK,EAAE,oBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,YAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,sBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,gBAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,oBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,wBAFJ;AAGJ,gBAAQ,uBAHJ;AAIJ,eAAO,SAJH;AAKJ,iBAAS,oBALL;AAMJ,gBAAQ,wBANJ;AAOJ,kBAAU,wBAPN;AAQJ,qBAAa,2BART;AASJ,yBAAiB,wBATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,uBAXX;AAYJ,yBAAiB,mBAZb;AAaJ,wBAAgB,oBAbZ;AAcJ,uBAAe,YAdX;AAeJ,+BAAuB,6BAfnB;AAgBJ,6BAAqB,0BAhBjB;AAiBJ,mBAAW,+BAjBP;AAkBJ,kBAAU,kCAlBN;AAmBJ,sBAAc,0DAnBV;AAoBJ,oBAAY,uCApBR;AAqBJ,oBAAY,uCArBR;AAsBJ,oBAAY,uCAtBR;AAuBJ,oBAAY,uCAvBR;AAwBJ,oBAAY,uCAxBR;AAyBJ,oBAAY,uCAzBR;AA0BJ,gCAAwB,+BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,qBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ca-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ca-ES': {\n      font: {\n        bold: 'Negreta',\n        italic: 'Curs<PERSON>',\n        underline: 'Subratllat',\n        clear: 'Treure estil de lletra',\n        height: 'Alçada de línia',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Ratllat',\n        subscript: 'Subíndex',\n        superscript: 'Superíndex',\n        size: 'Mida de lletra',\n      },\n      image: {\n        image: 'Imatge',\n        insert: 'Inserir imatge',\n        resizeFull: 'Redimensionar a mida completa',\n        resizeHalf: 'Redimensionar a la meitat',\n        resizeQuarter: 'Redimensionar a un quart',\n        floatLeft: 'Alinear a l\\'esquerra',\n        floatRight: 'Alinear a la dreta',\n        floatNone: 'No alinear',\n        shapeRounded: 'Forma: Arrodonit',\n        shapeCircle: 'Forma: Cercle',\n        shapeThumbnail: 'Forma: Marc',\n        shapeNone: 'Forma: Cap',\n        dragImageHere: 'Arrossegueu una imatge o text aquí',\n        dropImage: 'Deixa anar aquí una imatge o un text',\n        selectFromFiles: 'Seleccioneu des dels arxius',\n        maximumFileSize: 'Mida màxima de l\\'arxiu',\n        maximumFileSizeError: 'La mida màxima de l\\'arxiu s\\'ha superat.',\n        url: 'URL de la imatge',\n        remove: 'Eliminar imatge',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Enllaç del vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL del vídeo?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Enllaç',\n        insert: 'Inserir enllaç',\n        unlink: 'Treure enllaç',\n        edit: 'Editar',\n        textToDisplay: 'Text per mostrar',\n        url: 'Cap a quina URL porta l\\'enllaç?',\n        openInNewWindow: 'Obrir en una finestra nova',\n      },\n      table: {\n        table: 'Taula',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Inserir línia horitzontal',\n      },\n      style: {\n        style: 'Estil',\n        p: 'p',\n        blockquote: 'Cita',\n        pre: 'Codi',\n        h1: 'Títol 1',\n        h2: 'Títol 2',\n        h3: 'Títol 3',\n        h4: 'Títol 4',\n        h5: 'Títol 5',\n        h6: 'Títol 6',\n      },\n      lists: {\n        unordered: 'Llista desendreçada',\n        ordered: 'Llista endreçada',\n      },\n      options: {\n        help: 'Ajut',\n        fullscreen: 'Pantalla sencera',\n        codeview: 'Veure codi font',\n      },\n      paragraph: {\n        paragraph: 'Paràgraf',\n        outdent: 'Menys tabulació',\n        indent: 'Més tabulació',\n        left: 'Alinear a l\\'esquerra',\n        center: 'Alinear al mig',\n        right: 'Alinear a la dreta',\n        justify: 'Justificar',\n      },\n      color: {\n        recent: 'Últim color',\n        more: 'Més colors',\n        background: 'Color de fons',\n        foreground: 'Color de lletra',\n        transparent: 'Transparent',\n        setTransparent: 'Establir transparent',\n        reset: 'Restablir',\n        resetToDefault: 'Restablir per defecte',\n      },\n      shortcut: {\n        shortcuts: 'Dreceres de teclat',\n        close: 'Tancar',\n        textFormatting: 'Format de text',\n        action: 'Acció',\n        paragraphFormatting: 'Format de paràgraf',\n        documentStyle: 'Estil del document',\n        extraKeys: 'Tecles adicionals',\n      },\n      help: {\n        'insertParagraph': 'Inserir paràgraf',\n        'undo': 'Desfer l\\'última acció',\n        'redo': 'Refer l\\'última acció',\n        'tab': 'Tabular',\n        'untab': 'Eliminar tabulació',\n        'bold': 'Establir estil negreta',\n        'italic': 'Establir estil cursiva',\n        'underline': 'Establir estil subratllat',\n        'strikethrough': 'Establir estil ratllat',\n        'removeFormat': 'Netejar estil',\n        'justifyLeft': 'Alinear a l\\'esquerra',\n        'justifyCenter': 'Alinear al centre',\n        'justifyRight': 'Alinear a la dreta',\n        'justifyFull': 'Justificar',\n        'insertUnorderedList': 'Inserir llista desendreçada',\n        'insertOrderedList': 'Inserir llista endreçada',\n        'outdent': 'Reduïr tabulació del paràgraf',\n        'indent': 'Augmentar tabulació del paràgraf',\n        'formatPara': 'Canviar l\\'estil del bloc com a un paràgraf (etiqueta P)',\n        'formatH1': 'Canviar l\\'estil del bloc com a un H1',\n        'formatH2': 'Canviar l\\'estil del bloc com a un H2',\n        'formatH3': 'Canviar l\\'estil del bloc com a un H3',\n        'formatH4': 'Canviar l\\'estil del bloc com a un H4',\n        'formatH5': 'Canviar l\\'estil del bloc com a un H5',\n        'formatH6': 'Canviar l\\'estil del bloc com a un H6',\n        'insertHorizontalRule': 'Inserir una línia horitzontal',\n        'linkDialog.show': 'Mostrar panel d\\'enllaços',\n      },\n      history: {\n        undo: 'Desfer',\n        redo: 'Refer',\n      },\n      specialChar: {\n        specialChar: 'CARÀCTERS ESPECIALS',\n        select: 'Selecciona caràcters especials',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}