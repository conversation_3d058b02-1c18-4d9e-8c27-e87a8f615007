{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}QUẢN LÝ NGƯỜI DÙNG{% endblock %}

{% block content %}
    <div class="container mt-5">
        <h1 class="text-center mb-4">QUẢN LÝ NGƯỜI DÙNG</h1>
        <a href="{% url 'home' %}" class="btn btn-primary mb-4">Quay lại trang chủ</a>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Form Thêm/Sửa Người Dùng -->
        <div class="card mb-4">
            <div class="card-header">
                {% if edit_user %}Sửa Người Dùng{% else %}Thêm Người Dùng{% endif %}
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="username" class="form-label">Tên tài khoản:</label>
                        <input type="text" name="username" id="username" class="form-control" value="{{ edit_user.username|default:'' }}" required>
                        <div class="invalid-feedback">Vui lòng nhập tên tài khoản.</div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mật khẩu:</label>
                        <input type="password" name="password" id="password" class="form-control" required>
                        <div class="invalid-feedback">Vui lòng nhập mật khẩu.</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Phân quyền:</label>
                        <div class="form-check">
                            <input type="checkbox" name="view_baokiem" class="form-check-input" {% if edit_user and edit_user.permissions.view_baokiem %}checked{% endif %}>
                            <label class="form-check-label">BÁO KIỂM VẬT TƯ PVSX</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="view_kehoach" class="form-check-input" {% if edit_user and edit_user.permissions.view_kehoach %}checked{% endif %}>
                            <label class="form-check-label">BÁO CÁC TỒN KHO B3</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="view_thuocno" class="form-check-input" {% if edit_user and edit_user.permissions.view_thuocno %}checked{% endif %}>
                            <label class="form-check-label">NXT KHO XÍ NGHIỆP I</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="view_cap_vat_tu_khu_a" class="form-check-input" {% if edit_user and edit_user.permissions.view_cap_vat_tu_khu_a %}checked{% endif %}>
                            <label class="form-check-label">CẤP VẬT TƯ PVSX KHU A</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="view_nhap_kho_thanh_pham_khu_a" class="form-check-input" {% if edit_user and edit_user.permissions.view_nhap_kho_thanh_pham_khu_a %}checked{% endif %}>
                            <label class="form-check-label">NHẬP KHO THÀNH PHẨM, BTP KHU A</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="super_password" class="form-label">Mật khẩu Super:</label>
                        <input type="password" name="super_password" id="super_password" class="form-control" required>
                        <div class="invalid-feedback">Vui lòng nhập mật khẩu Super.</div>
                    </div>
                    {% if edit_user %}
                        <input type="hidden" name="user_id" value="{{ edit_user.id }}">
                    {% endif %}
                    <button type="submit" name="update_edit" class="btn btn-primary" {% if not edit_user %}style="display:none;"{% endif %}>Cập Nhật</button>
                    <button type="submit" name="add_user" class="btn btn-success" {% if edit_user %}style="display:none;"{% endif %}>Thêm</button>
                </form>
            </div>
        </div>

        <!-- Form Xóa Người Dùng -->
        {% if edit_user %}
            <div class="card mb-4">
                <div class="card-header">Xóa Người Dùng</div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="super_password_delete" class="form-label">Mật khẩu Super:</label>
                            <input type="password" name="super_password" id="super_password_delete" class="form-control" required>
                            <div class="invalid-feedback">Vui lòng nhập mật khẩu Super.</div>
                        </div>
                        <input type="hidden" name="delete_user_id" value="{{ edit_user.id }}">
                        <input type="hidden" name="confirm_delete" value="1">
                        <button type="submit" class="btn btn-danger">Xóa</button>
                    </form>
                </div>
            </div>
        {% endif %}

        <!-- Danh Sách Người Dùng -->
        <div class="card">
            <div class="card-header">Danh Sách Người Dùng</div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Tên tài khoản</th>
                            <th>Mật khẩu</th>
                            <th>Phân quyền</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in page_obj %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.last_password }}</td>
                                <td>
                                    {% if user.permissions.view_baokiem %}BÁO KIỂM VẬT TƯ PVSX{% if user.permissions.view_kehoach or user.permissions.view_thuocno or user.permissions.view_cap_vat_tu_khu_a or user.permissions.view_nhap_kho_thanh_pham_khu_a %}, {% endif %}{% endif %}
                                    {% if user.permissions.view_kehoach %}BÁO CÁC TỒN KHO B3{% if user.permissions.view_thuocno or user.permissions.view_cap_vat_tu_khu_a or user.permissions.view_nhap_kho_thanh_pham_khu_a %}, {% endif %}{% endif %}
                                    {% if user.permissions.view_thuocno %}NXT KHO XÍ NGHIỆP I{% if user.permissions.view_cap_vat_tu_khu_a or user.permissions.view_nhap_kho_thanh_pham_khu_a %}, {% endif %}{% endif %}
                                    {% if user.permissions.view_cap_vat_tu_khu_a %}CẤP VẬT TƯ PVSX KHU A{% if user.permissions.view_nhap_kho_thanh_pham_khu_a %}, {% endif %}{% endif %}
                                    {% if user.permissions.view_nhap_kho_thanh_pham_khu_a %}NHẬP KHO THÀNH PHẨM, BTP KHU A{% endif %}
                                </td>
                                <td>
                                    <a href="?edit_user_id={{ user.id }}" class="btn btn-warning btn-sm">Sửa</a>
                                    <a href="?edit_user_id={{ user.id }}&delete=1" class="btn btn-danger btn-sm">Xóa</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Phân trang -->
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Trước</a>
                            </li>
                        {% endif %}
                        <li class="page-item disabled">
                            <span class="page-link">Trang {{ page_obj.number }} của {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Tiếp</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script>
        // Validation Bootstrap
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
{% endblock %}