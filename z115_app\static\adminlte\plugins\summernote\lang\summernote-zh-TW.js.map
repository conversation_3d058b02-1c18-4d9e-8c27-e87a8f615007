{"version": 3, "file": "lang/summernote-zh-TW.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,IADF;AAEJC,QAAAA,MAAM,EAAE,IAFJ;AAGJC,QAAAA,SAAS,EAAE,IAHP;AAIJC,QAAAA,KAAK,EAAE,MAJH;AAKJC,QAAAA,MAAM,EAAE,IALJ;AAMJC,QAAAA,IAAI,EAAE,IANF;AAOJC,QAAAA,aAAa,EAAE,KAPX;AAQJC,QAAAA,SAAS,EAAE,IARP;AASJC,QAAAA,WAAW,EAAE,IATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,MAAM,EAAE,MAFH;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,UAAU,EAAE,SAJP;AAKLC,QAAAA,aAAa,EAAE,SALV;AAMLC,QAAAA,SAAS,EAAE,MANN;AAOLC,QAAAA,UAAU,EAAE,MAPP;AAQLC,QAAAA,SAAS,EAAE,MARN;AASLC,QAAAA,YAAY,EAAE,QATT;AAULC,QAAAA,WAAW,EAAE,OAVR;AAWLC,QAAAA,cAAc,EAAE,SAXX;AAYLC,QAAAA,SAAS,EAAE,OAZN;AAaLC,QAAAA,aAAa,EAAE,UAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,OAfZ;AAgBLC,QAAAA,eAAe,EAAE,SAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,YAjBjB;AAkBLC,QAAAA,GAAG,EAAE,MAlBA;AAmBLC,QAAAA,MAAM,EAAE,MAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,SAAS,EAAE,MAFN;AAGLpB,QAAAA,MAAM,EAAE,MAHH;AAILgB,QAAAA,GAAG,EAAE,MAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,IADF;AAEJtB,QAAAA,MAAM,EAAE,MAFJ;AAGJuB,QAAAA,MAAM,EAAE,MAHJ;AAIJC,QAAAA,IAAI,EAAE,MAJF;AAKJC,QAAAA,aAAa,EAAE,MALX;AAMJT,QAAAA,GAAG,EAAE,MAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,WAAW,EAAE,OAFR;AAGLC,QAAAA,WAAW,EAAE,OAHR;AAILC,QAAAA,UAAU,EAAE,OAJP;AAKLC,QAAAA,WAAW,EAAE,OALR;AAMLC,QAAAA,MAAM,EAAE,KANH;AAOLC,QAAAA,MAAM,EAAE,KAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,CAAC,EAAE,IAFE;AAGLC,QAAAA,UAAU,EAAE,MAHP;AAILC,QAAAA,GAAG,EAAE,OAJA;AAKLC,QAAAA,EAAE,EAAE,MALC;AAMLC,QAAAA,EAAE,EAAE,MANC;AAOLC,QAAAA,EAAE,EAAE,MAPC;AAQLC,QAAAA,EAAE,EAAE,MARC;AASLC,QAAAA,EAAE,EAAE,MATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,MADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,IADC;AAEPC,QAAAA,UAAU,EAAE,KAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,IADF;AAETC,QAAAA,OAAO,EAAE,MAFA;AAGTC,QAAAA,MAAM,EAAE,MAHC;AAITC,QAAAA,IAAI,EAAE,MAJG;AAKTC,QAAAA,MAAM,EAAE,MALC;AAMTC,QAAAA,KAAK,EAAE,MANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,MADH;AAELC,QAAAA,IAAI,EAAE,IAFD;AAGLC,QAAAA,UAAU,EAAE,IAHP;AAILC,QAAAA,UAAU,EAAE,IAJP;AAKLC,QAAAA,WAAW,EAAE,IALR;AAMLC,QAAAA,cAAc,EAAE,IANX;AAOLC,QAAAA,KAAK,EAAE,IAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,KADH;AAERC,QAAAA,KAAK,EAAE,IAFC;AAGRC,QAAAA,cAAc,EAAE,MAHR;AAIRC,QAAAA,MAAM,EAAE,IAJA;AAKRC,QAAAA,mBAAmB,EAAE,MALb;AAMRC,QAAAA,aAAa,EAAE,MANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,IADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-zh-TW.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'zh-TW': {\n      font: {\n        bold: '粗體',\n        italic: '斜體',\n        underline: '底線',\n        clear: '清除格式',\n        height: '行高',\n        name: '字體',\n        strikethrough: '刪除線',\n        subscript: '下標',\n        superscript: '上標',\n        size: '字號',\n      },\n      image: {\n        image: '圖片',\n        insert: '插入圖片',\n        resizeFull: '縮放至100%',\n        resizeHalf: '縮放至 50%',\n        resizeQuarter: '縮放至 25%',\n        floatLeft: '靠左浮動',\n        floatRight: '靠右浮動',\n        floatNone: '取消浮動',\n        shapeRounded: '形狀: 圓角',\n        shapeCircle: '形狀: 圓',\n        shapeThumbnail: '形狀: 縮略圖',\n        shapeNone: '形狀: 無',\n        dragImageHere: '將圖片拖曳至此處',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: '從本機上傳',\n        maximumFileSize: '文件大小最大值',\n        maximumFileSizeError: '文件大小超出最大值。',\n        url: '圖片網址',\n        remove: '移除圖片',\n        original: 'Original',\n      },\n      video: {\n        video: '影片',\n        videoLink: '影片連結',\n        insert: '插入影片',\n        url: '影片網址',\n        providers: '(優酷, Instagram, DailyMotion, Youtube等)',\n      },\n      link: {\n        link: '連結',\n        insert: '插入連結',\n        unlink: '取消連結',\n        edit: '編輯連結',\n        textToDisplay: '顯示文字',\n        url: '連結網址',\n        openInNewWindow: '在新視窗開啟',\n      },\n      table: {\n        table: '表格',\n        addRowAbove: '上方插入列',\n        addRowBelow: '下方插入列',\n        addColLeft: '左方插入欄',\n        addColRight: '右方插入欄',\n        delRow: '刪除列',\n        delCol: '刪除欄',\n        delTable: '刪除表格',\n      },\n      hr: {\n        insert: '水平線',\n      },\n      style: {\n        style: '樣式',\n        p: '一般',\n        blockquote: '引用區塊',\n        pre: '程式碼區塊',\n        h1: '標題 1',\n        h2: '標題 2',\n        h3: '標題 3',\n        h4: '標題 4',\n        h5: '標題 5',\n        h6: '標題 6',\n      },\n      lists: {\n        unordered: '項目清單',\n        ordered: '編號清單',\n      },\n      options: {\n        help: '幫助',\n        fullscreen: '全螢幕',\n        codeview: '原始碼',\n      },\n      paragraph: {\n        paragraph: '段落',\n        outdent: '取消縮排',\n        indent: '增加縮排',\n        left: '靠右對齊',\n        center: '靠中對齊',\n        right: '靠右對齊',\n        justify: '左右對齊',\n      },\n      color: {\n        recent: '字型顏色',\n        more: '更多',\n        background: '背景',\n        foreground: '字體',\n        transparent: '透明',\n        setTransparent: '透明',\n        reset: '重設',\n        resetToDefault: '預設',\n      },\n      shortcut: {\n        shortcuts: '快捷鍵',\n        close: '關閉',\n        textFormatting: '文字格式',\n        action: '動作',\n        paragraphFormatting: '段落格式',\n        documentStyle: '文件格式',\n        extraKeys: '額外按鍵',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: '復原',\n        redo: '取消復原',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}