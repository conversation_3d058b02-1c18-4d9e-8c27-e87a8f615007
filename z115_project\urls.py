"""
URL configuration for z115_project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from z115_app.views import get_phieu_details

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('z115_app.urls')),
    path('tao_phieu/<int:phieu_id>/', get_phieu_details, name='get_phieu_details'),  # <PERSON>h<PERSON>ng cần redirect, để z115_app.urls xử lý root
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)