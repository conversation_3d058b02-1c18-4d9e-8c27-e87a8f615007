{% extends 'z115_app/base.html' %}

{% block title %}BÁO CÁO TỒN KHO B3{% endblock %}

{% block content %}
    <div class="container mt-4 text-center">
        <h2 style="font-weight: bold; color: #008000;">BÁO CÁO TỒN KHO B3{% if upload_date %} TÍNH ĐẾN NGÀY {{ upload_date }}{% endif %}</h2>
        <p>Nội dung báo cáo tồn kho B3 hằng ngày...</p>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
        {% endif %}

        <form method="get" action="">
            <div class="row mb-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search_vat_tu" value="{{ search_vat_tu }}" placeholder="Tên vật tư">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search_ma_vat_tu" value="{{ search_ma_vat_tu }}" placeholder="Mã vật tư">
                </div>
                <div class="col-md-3">
                    <select class="form-control" name="filter_phan_loai">
                        <option value="all" {% if filter_phan_loai == 'all' %}selected{% endif %}>Tất cả</option>
                        <option value="chinh" {% if filter_phan_loai == 'chinh' %}selected{% endif %}>Mã chính</option>
                        <option value="phu" {% if filter_phan_loai == 'phu' %}selected{% endif %}>Mã phụ</option>
                    </select>
                </div>
                <div class="col-md-3 text-end">
                    <button type="submit" class="btn btn-primary">Tìm kiếm</button>
                    <a href="?export_excel=1" class="btn btn-success ml-2">Xuất Excel</a>
                </div>
            </div>
        </form>

        <table class="table table-striped table-hover">
            <thead style="background-color: #0000FF; color: white; font-weight: bold;">
                <tr>
                    <th style="padding: 10px;">STT</th>
                    <th style="padding: 10px;">Mã vật tư</th>
                    <th style="padding: 10px;">Vật tư</th>
                    <th style="padding: 10px;">Đvt</th>
                    <th style="padding: 10px;">Số lượng</th>
                    <th style="padding: 10px;">Giá trị</th>
                    <th style="padding: 10px;">Phân loại</th>
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                    <tr>
                        <td style="padding: 10px;">{{ item.stt }}</td>
                        <td style="padding: 10px;">{{ item.ma_vat_tu }}</td>
                        <td style="padding: 10px;">{{ item.vat_tu }}</td>
                        <td style="padding: 10px;">{{ item.don_vi_tinh }}</td>
                        <td style="padding: 10px;">{{ item.so_luong|floatformat:2 }}</td>
                        <td style="padding: 10px;">{{ item.gia_tri|floatformat:0 }}</td>
                        <td style="padding: 10px;">{{ item.phan_loai }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_vat_tu %}&search_vat_tu={{ search_vat_tu }}{% endif %}{% if search_ma_vat_tu %}&search_ma_vat_tu={{ search_ma_vat_tu }}{% endif %}{% if filter_phan_loai %}&filter_phan_loai={{ filter_phan_loai }}{% endif %}">Trước</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Trước</span></li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                            <a class="page-link" href="?page={{ num }}{% if search_vat_tu %}&search_vat_tu={{ search_vat_tu }}{% endif %}{% if search_ma_vat_tu %}&search_ma_vat_tu={{ search_ma_vat_tu }}{% endif %}{% if filter_phan_loai %}&filter_phan_loai={{ filter_phan_loai }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endfor %}
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_vat_tu %}&search_vat_tu={{ search_vat_tu }}{% endif %}{% if search_ma_vat_tu %}&search_ma_vat_tu={{ search_ma_vat_tu }}{% endif %}{% if filter_phan_loai %}&filter_phan_loai={{ filter_phan_loai }}{% endif %}">Tiếp</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}

        {% if user.username == 'PHUONGB3' or user.username == 'THUYB3' %}
            <div class="row mt-4">
                <div class="col-md-12 text-center">
                    <form method="post" enctype="multipart/form-data" class="d-inline-block">
                        {% csrf_token %}
                        <input type="file" name="excel_file" accept=".xlsx" class="form-control-file d-inline-block mr-2">
                        <input type="password" name="password" class="form-control d-inline-block" placeholder="Nhập mật khẩu" style="width: 150px;">
                        <button type="submit" name="upload_file" class="btn btn-info d-inline-block">Tải lên</button>
                    </form>
                    <form method="post" class="d-inline-block ml-2">
                        {% csrf_token %}
                        <button type="submit" name="reset_data" class="btn btn-danger">RESET</button>
                    </form>
                    <form method="post" class="d-inline-block ml-2">
                        {% csrf_token %}
                        <button type="submit" name="backup_data" class="btn btn-warning">Sao lưu</button>
                    </form>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12 text-center">
                    <form method="post" class="d-inline-block">
                        {% csrf_token %}
                        <select name="backup_date" class="form-control d-inline-block w-auto">
                            <option value="">Chọn ngày sao lưu</option>
                            {% for date in backup_dates %}
                                <option value="{{ date|date:'Y-m-d H:i:s' }}">{{ date|date:'d/m/Y H:i' }}</option>
                            {% endfor %}
                        </select>
                        <button type="submit" name="restore_data" class="btn btn-success d-inline-block ml-2">Khôi phục</button>
                    </form>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}