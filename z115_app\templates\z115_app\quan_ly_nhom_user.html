{% extends 'z115_app/base.html' %}
{% load static %}
{% load custom_filters %}

{% block content %}
<div class="container mt-5">
    <h1 class="text-center mb-4">QUẢN LÝ NHÓM USER</h1>
    <a href="{% url 'home' %}" class="btn btn-primary mb-4">Quay lại trang chủ</a>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Danh Sách Người Dùng -->
    <div class="card">
        <div class="card-header">Danh Sách Người Dùng</div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>STT</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th><PERSON><PERSON><PERSON><PERSON> hiện tại</th>
                        <th>H<PERSON>nh động</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ user.username }}</td>
                            <td>
                                {% for perm in user_permissions|get_item:user.username %}
                                    <span class="badge bg-secondary">{{ perm }}</span>
                                {% empty %}
                                    <span class="text-muted">Không có quyền</span>
                                {% endfor %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-warning btn-sm edit-btn"
                                        data-bs-toggle="modal"
                                        data-bs-target="#permissionModal"
                                        data-username="{{ user.username }}">
                                    Sửa
                                </button>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal Phân Quyền -->
    <div class="modal fade" id="permissionModal" tabindex="-1" aria-labelledby="permissionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post" id="permissionForm" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="modal-header">
                        <h5 class="modal-title" id="permissionModalLabel">Phân quyền cho người dùng: <strong id="modalUsername"></strong></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="user" id="editUsername">
                        <div class="mb-3">
                            <label for="superPassword" class="form-label">Mật khẩu Super:</label>
                            <input type="password" class="form-control" id="superPassword" name="super_password" required>
                            <div class="invalid-feedback">Vui lòng nhập mật khẩu Super.</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Danh sách quyền:</label>
                            {% for perm in permissions %}
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="permissions" value="{{ perm }}" id="perm_{{ perm }}">
                                    <label class="form-check-label" for="perm_{{ perm }}">
                                        {% if perm == 'tao_phieu' %}Tạo phiếu cấp VT{% endif %}
                                        {% if perm == 'danh_sach_cho_dmv_duyet' %}Danh sách chờ ĐMV duyệt{% endif %}
                                        {% if perm == 'danh_sach_cho_ch_duyet' %}Danh sách chờ Chỉ huy duyệt{% endif %}
                                        {% if perm == 'danh_sach_phieu_da_duyet' %}Danh sách phiếu đã duyệt{% endif %}
                                        {% if perm == 'in_phieu' %}In phiếu{% endif %}
                                        {% if perm == 'duyet_phieu_ch' %}Duyệt phiếu Chỉ huy{% endif %}
                                        {% if perm == 'duyet_phieu_dmv' %}Duyệt phiếu ĐMV{% endif %}
                                        {% if perm == 'danh_sach_phieu_ch_duyet' %}Danh sách phiếu Chỉ huy đã duyệt{% endif %}
                                        {% if perm == 'danh_sach_phieu_dmv_duyet' %}Danh sách phiếu ĐMV đã duyệt{% endif %}
                                        {% if perm == 'chon_phieu' %}Chọn phiếu{% endif %}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Lưu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Validation Bootstrap
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
    })()

    // Xử lý Modal và AJAX
    document.addEventListener('DOMContentLoaded', function () {
        var permissionModal = new bootstrap.Modal(document.getElementById('permissionModal'));
        var permissionForm = document.getElementById('permissionForm');
        var modalUsername = document.getElementById('modalUsername');
        var editUsername = document.getElementById('editUsername');

        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function () {
                var username = this.getAttribute('data-username');
                modalUsername.textContent = username;
                editUsername.value = username;

                // Lấy quyền hiện tại qua AJAX
                fetch(`/quan-ly-nhom-user/?user=${encodeURIComponent(username)}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
                        checkbox.checked = data.permissions.includes(checkbox.value);
                    });
                })
                .catch(error => console.error('Lỗi khi tải quyền:', error));
            });
        });

        permissionForm.addEventListener('submit', function (event) {
            event.preventDefault();

            var formData = new FormData(this);

            fetch('/quan-ly-nhom-user/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    permissionModal.hide();
                    location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Lỗi khi cập nhật quyền:', error);
                alert('Đã xảy ra lỗi khi gửi yêu cầu. Vui lòng thử lại.');
            });
        });
    });
</script>
{% endblock %}