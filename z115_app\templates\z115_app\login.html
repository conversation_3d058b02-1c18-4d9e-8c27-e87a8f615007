{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}Đ<PERSON><PERSON> nhập{% endblock %}

{% block content %}
<!-- Link Bootstrap -->
<link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
<script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

<!-- CSS tùy chỉnh -->
<style>
    body {
        background-color: #90ee90; /* Nền xanh lá nhạt */
        margin: 0;
        font-family: 'Times New Roman', Times, serif;
    }
    .wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh; /* Chiếm toàn màn hình */
    }
    .login-container {
        background-color: #fff;
        padding: 30px;
        border-radius: 10px;
        border: 2px solid #333; /* Viền đậm */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 500px;
        width: 100%;
        text-align: center;
    }
    .login-logo {
        max-width: 150px;
        margin-bottom: 10px;
    }
    .title-primary {
        color: #0d6efd;
        font-weight: bold;
    }
    .title-secondary {
        color: #6c757d;
        font-weight: bold;
    }
    .footer-text {
        position: fixed;
        bottom: 10px;
        left: 10px;
        font-size: 12pt;
        font-weight: bold;
        color: #007bff; /* Màu xanh dương */
    }
</style>

<!-- Nội dung đăng nhập -->
<div class="wrapper">
    <div class="login-container">
        <img src="{% static 'images/logo.jpg' %}" alt="Logo Z115" class="login-logo img-fluid">
        <h2 class="title-primary mb-0" style="font-size: 20pt;">
            NHÀ MÁY Z115
        </h2>
        <h2 class="title-primary mb-3" style="font-size: 20pt;">
            TRANG CHỦ PHÒNG VẬT TƯ
        </h2>
        <p class="title-secondary mb-3" style="font-size: 14pt;">
            Đồng chí đăng nhập thông tin
        </p>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-danger" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_username" class="form-label title-secondary" style="font-size: 14pt;">
                    Tên đăng nhập
                </label>
                <input type="text" name="username" id="id_username" class="form-control" required>
                <div class="invalid-feedback">Vui lòng nhập tên đăng nhập.</div>
            </div>
            <div class="mb-3">
                <label for="id_password" class="form-label title-secondary" style="font-size: 14pt;">
                    Mật khẩu
                </label>
                <input type="password" name="password" id="id_password" class="form-control" required>
                <div class="invalid-feedback">Vui lòng nhập mật khẩu.</div>
            </div>
            <button type="submit" class="btn btn-primary w-100" style="font-family: 'Times New Roman', Times, serif;">
                Đăng nhập
            </button>
        </form>
    </div>
</div>

<!-- Footer text -->
<div class="footer-text">
    @2025 Copyrigt Trần Đình Hưng - Phòng B3
</div>

<!-- JS kích hoạt validate của Bootstrap -->
<script>
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
</script>
{% endblock %}