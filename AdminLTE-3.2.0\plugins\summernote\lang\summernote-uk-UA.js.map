{"version": 3, "file": "lang/summernote-uk-UA.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,aADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,uBAJH;AAKJC,QAAAA,MAAM,EAAE,cALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,aAPX;AAQJC,QAAAA,SAAS,EAAE,eARP;AASJC,QAAAA,WAAW,EAAE,gBATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,MAAM,EAAE,mBAFH;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,aAAa,EAAE,iBALV;AAMLC,QAAAA,SAAS,EAAE,qBANN;AAOLC,QAAAA,UAAU,EAAE,sBAPP;AAQLC,QAAAA,SAAS,EAAE,wBARN;AASLC,QAAAA,YAAY,EAAE,oBATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,cAZN;AAaLC,QAAAA,aAAa,EAAE,2BAbV;AAcLC,QAAAA,SAAS,EAAE,sBAdN;AAeLC,QAAAA,eAAe,EAAE,kBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,mBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,oBAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,WAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,WADF;AAEJtB,QAAAA,MAAM,EAAE,oBAFJ;AAGJuB,QAAAA,MAAM,EAAE,oBAHJ;AAIJC,QAAAA,IAAI,EAAE,YAJF;AAKJC,QAAAA,aAAa,EAAE,0BALX;AAMJT,QAAAA,GAAG,EAAE,kBAND;AAOJU,QAAAA,eAAe,EAAE,2BAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,YAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,aALC;AAMLC,QAAAA,EAAE,EAAE,aANC;AAOLC,QAAAA,EAAE,EAAE,aAPC;AAQLC,QAAAA,EAAE,EAAE,aARC;AASLC,QAAAA,EAAE,EAAE,aATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,mBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,UAAU,EAAE,eAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,mBAHC;AAITC,QAAAA,IAAI,EAAE,0BAJG;AAKTC,QAAAA,MAAM,EAAE,qBALC;AAMTC,QAAAA,KAAK,EAAE,2BANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,YAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,cAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,kBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA/FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,mBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,KAJA;AAKRC,QAAAA,mBAAmB,EAAE,wBALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-uk-UA.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'uk-UA': {\n      font: {\n        bold: 'Напівжирний',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Підкреслений',\n        clear: 'Прибрати стилі шрифту',\n        height: 'Висота лінії',\n        name: 'Шриф<PERSON>',\n        strikethrough: 'Закреслений',\n        subscript: 'Нижній індекс',\n        superscript: 'Верхній індекс',\n        size: 'Розмір шрифту',\n      },\n      image: {\n        image: 'Картинка',\n        insert: 'Вставити картинку',\n        resizeFull: 'Відновити розмір',\n        resizeHalf: 'Зменшити до 50%',\n        resizeQuarter: 'Зменшити до 25%',\n        floatLeft: 'Розташувати ліворуч',\n        floatRight: 'Розташувати праворуч',\n        floatNone: 'Початкове розташування',\n        shapeRounded: 'Форма: Заокруглена',\n        shapeCircle: 'Форма: Коло',\n        shapeThumbnail: 'Форма: Мініатюра',\n        shapeNone: 'Форма: Немає',\n        dragImageHere: 'Перетягніть сюди картинку',\n        dropImage: 'Перетягніть картинку',\n        selectFromFiles: 'Вибрати з файлів',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL картинки',\n        remove: 'Видалити картинку',\n        original: 'Original',\n      },\n      video: {\n        video: 'Відео',\n        videoLink: 'Посилання на відео',\n        insert: 'Вставити відео',\n        url: 'URL відео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion чи Youku)',\n      },\n      link: {\n        link: 'Посилання',\n        insert: 'Вставити посилання',\n        unlink: 'Прибрати посилання',\n        edit: 'Редагувати',\n        textToDisplay: 'Текст, що відображається',\n        url: 'URL для переходу',\n        openInNewWindow: 'Відкривати у новому вікні',\n        useProtocol: 'Використовувати протокол за замовчуванням',\n      },\n      table: {\n        table: 'Таблиця',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Вставити горизонтальну лінію',\n      },\n      style: {\n        style: 'Стиль',\n        p: 'Нормальний',\n        blockquote: 'Цитата',\n        pre: 'Код',\n        h1: 'Заголовок 1',\n        h2: 'Заголовок 2',\n        h3: 'Заголовок 3',\n        h4: 'Заголовок 4',\n        h5: 'Заголовок 5',\n        h6: 'Заголовок 6',\n      },\n      lists: {\n        unordered: 'Маркований список',\n        ordered: 'Нумерований список',\n      },\n      options: {\n        help: 'Допомога',\n        fullscreen: 'На весь екран',\n        codeview: 'Початковий код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Зменшити відступ',\n        indent: 'Збільшити відступ',\n        left: 'Вирівняти по лівому краю',\n        center: 'Вирівняти по центру',\n        right: 'Вирівняти по правому краю',\n        justify: 'Розтягнути по ширині',\n      },\n      color: {\n        recent: 'Останній колір',\n        more: 'Ще кольори',\n        background: 'Колір фону',\n        foreground: 'Колір шрифту',\n        transparent: 'Прозорий',\n        setTransparent: 'Зробити прозорим',\n        reset: 'Відновити',\n        resetToDefault: 'Відновити початкові',\n      },\n      shortcut: {\n        shortcuts: 'Комбінації клавіш',\n        close: 'Закрити',\n        textFormatting: 'Форматування тексту',\n        action: 'Дія',\n        paragraphFormatting: 'Форматування параграфу',\n        documentStyle: 'Стиль документу',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Відмінити',\n        redo: 'Повторити',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}