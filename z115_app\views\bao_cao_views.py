import logging
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.core.paginator import Paginator
from django.contrib import messages
import pandas as pd
import io
import os
from datetime import datetime
from ..models import TonKhoB3, TonKhoB3Backup

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def bao_cao_ton_kho_b3(request):
    upload_date = None
    if request.method == "POST" and request.user.username in ['PHUONGB3', 'THUYB3']:
        if 'upload_file' in request.POST:
            password = request.POST.get('password', '')
            if (request.user.username == 'PHUONGB3' and password != '123456') or (request.user.username == 'THUYB3' and password != '123456'):
                messages.error(request, "MẬT KHẨU KHÔNG ĐÚNG!")
                return redirect('bao_cao_ton_kho_b3')
            if request.FILES.get('excel_file'):
                excel_file = request.FILES['excel_file']
                upload_date = datetime.now().strftime('%d/%m/%Y')
                try:
                    # Xóa toàn bộ dữ liệu cũ trước khi nhập dữ liệu mới
                    TonKhoB3.objects.all().delete()
                    logger.debug("Old data cleared successfully.")

                    df = pd.read_excel(excel_file)
                    logger.debug(f"Columns in Excel file: {df.columns.tolist()}")
                    required_columns = ['STT', 'Mã vật tư', 'Vật tư', 'Đvt', 'Số lượng', 'Giá trị', 'PL.vật tư']
                    if not all(col in df.columns for col in required_columns):
                        raise ValueError(f"Thiếu cột cần thiết. Các cột yêu cầu: {required_columns}")

                    new_records = 0
                    total_rows = len(df)
                    processed_rows = 0

                    for index, row in df.iterrows():
                        processed_rows += 1
                        if processed_rows % 200 == 0 or processed_rows == total_rows:
                            messages.info(request, f"Đang tải dữ liệu... {processed_rows}/{total_rows}")
                        try:
                            stt = int(row['STT']) if pd.notna(row['STT']) else None
                            ma_vat_tu = str(row['Mã vật tư']) if pd.notna(row['Mã vật tư']) else ''
                            vat_tu = str(row['Vật tư'])
                            don_vi_tinh = str(row['Đvt'])
                            so_luong = float(row['Số lượng']) if pd.notna(row['Số lượng']) else 0.0
                            gia_tri = float(row['Giá trị']) if pd.notna(row['Giá trị']) else 0.0
                            phan_loai = str(row['PL.vật tư'])
                            TonKhoB3.objects.create(
                                stt=stt, ma_vat_tu=ma_vat_tu, vat_tu=vat_tu, don_vi_tinh=don_vi_tinh,
                                so_luong=so_luong, gia_tri=gia_tri, phan_loai=phan_loai
                            )
                            new_records += 1
                            logger.debug(f"Added new record: ma_vat_tu={ma_vat_tu}, vat_tu={vat_tu}")
                        except ValueError as ve:
                            logger.error(f"Error at row {index + 2}: Invalid value - {ve}")
                            messages.error(request, f"Lỗi tại dòng {index + 2}: Giá trị không hợp lệ - {str(ve)}.")
                            continue
                        except Exception as e:
                            logger.error(f"Error at row {index + 2}: {e}")
                            messages.error(request, f"Lỗi tại dòng {index + 2}: {str(e)}")
                            continue
                    total_pages = (total_rows + 9) // 10  # Tính số trang (10 bản ghi/trang)
                    if new_records > 0:
                        messages.success(request, f"BẠN ĐÃ TẢI FILE THÀNH CÔNG {total_pages} TRANG/{total_pages} TRANG - DỮ LIỆU ĐÃ ĐƯỢC CẬP NHẬT TỚI NGÀY {upload_date}")
                    else:
                        messages.info(request, "Không có dữ liệu để bổ sung.")
                except Exception as e:
                    logger.error(f"Error processing Excel file: {e}")
                    messages.error(request, f"Lỗi khi xử lý file Excel: {str(e)}, vui lòng kiểm tra định dạng!")
            return redirect('bao_cao_ton_kho_b3')
        elif 'reset_data' in request.POST:
            count_before = TonKhoB3.objects.count()
            TonKhoB3.objects.all().delete()
            count_after = TonKhoB3.objects.count()
            logger.debug(f"Reset: Before count={count_before}, After count={count_after}")
            messages.success(request, "ĐÃ RESET DỮ LIỆU THÀNH CÔNG!")
            return redirect('bao_cao_ton_kho_b3')
        elif 'backup_data' in request.POST:
            data = TonKhoB3.objects.all()
            backup_date = datetime.now()
            for item in data:
                TonKhoB3Backup.objects.create(
                    stt=item.stt, ma_vat_tu=item.ma_vat_tu, vat_tu=item.vat_tu, don_vi_tinh=item.don_vi_tinh,
                    so_luong=item.so_luong, gia_tri=item.gia_tri, phan_loai=item.phan_loai, backup_date=backup_date
                )
            output = io.BytesIO()
            df = pd.DataFrame(list(data.values('stt', 'ma_vat_tu', 'vat_tu', 'don_vi_tinh', 'so_luong', 'gia_tri', 'phan_loai')))
            df.to_excel(output, index=False)
            output.seek(0)
            backup_folder = r"C:\Users\<USER>\z115_project\saoluu"
            os.makedirs(backup_folder, exist_ok=True)
            backup_filename = f'ton_kho_b3_backup_{backup_date.strftime("%Y%m%d_%H%M%S")}.xlsx'
            backup_file_path = os.path.join(backup_folder, backup_filename)
            try:
                with open(backup_file_path, 'wb') as f:
                    f.write(output.getvalue())
                logger.debug(f"Backup file saved to {backup_file_path}")
                response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename="{backup_filename}"'
                messages.success(request, f"ĐÃ SAO LƯU DỮ LIỆU THÀNH CÔNG - FILE ĐÃ TẢI VỀ VÀ LƯU TẠI {backup_file_path}")
                return response
            except Exception as e:
                logger.error(f"Failed to save backup file: {e}")
                messages.error(request, f"LỖI KHI LƯU FILE SAO LƯU: {str(e)}")
                return redirect('bao_cao_ton_kho_b3')
        elif 'restore_data' in request.POST:
            backup_date_str = request.POST.get('backup_date')
            if backup_date_str:
                backup_date = datetime.strptime(backup_date_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=None).date()
                TonKhoB3.objects.all().delete()
                backups = TonKhoB3Backup.objects.filter(backup_date__date=backup_date)
                for backup in backups:
                    TonKhoB3.objects.create(
                        stt=backup.stt, ma_vat_tu=backup.ma_vat_tu, vat_tu=backup.vat_tu, don_vi_tinh=backup.don_vi_tinh,
                        so_luong=backup.so_luong, gia_tri=backup.gia_tri, phan_loai=backup.phan_loai
                    )
                logger.debug(f"Restored data from backup date {backup_date}")
                messages.success(request, f"ĐÃ KHÔI PHỤC DỮ LIỆU TỪ NGÀY SAO LƯU {backup_date}")
            else:
                messages.error(request, "VUI LÒNG CHỌN NGÀY SAO LƯU!")
            return redirect('bao_cao_ton_kho_b3')

    tonkho_list = TonKhoB3.objects.all().order_by('stt')
    search_vat_tu = request.GET.get('search_vat_tu', '').strip()
    search_ma_vat_tu = request.GET.get('search_ma_vat_tu', '').strip()
    filter_phan_loai = request.GET.get('filter_phan_loai', 'all')

    if search_vat_tu:
        tonkho_list = tonkho_list.filter(vat_tu__icontains=search_vat_tu)
    if search_ma_vat_tu:
        tonkho_list = tonkho_list.filter(ma_vat_tu__icontains=search_ma_vat_tu)
    if filter_phan_loai == 'chinh':
        tonkho_list = tonkho_list.filter(phan_loai='Chính')
    elif filter_phan_loai == 'phu':
        tonkho_list = tonkho_list.filter(phan_loai='Phụ')

    paginator = Paginator(tonkho_list, 10)  # Tự động phân trang 10 bản ghi/trang
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    backup_dates = TonKhoB3Backup.objects.values_list('backup_date', flat=True).distinct().order_by('-backup_date')
    # Lấy ngày tải gần nhất từ database để hiển thị
    latest_upload = TonKhoB3.objects.order_by('-id').first()
    if latest_upload:
        upload_date = latest_upload.created_at.strftime('%d/%m/%Y')

    if request.GET.get('export_excel'):
        output = io.BytesIO()
        df = pd.DataFrame(list(tonkho_list.values('stt', 'ma_vat_tu', 'vat_tu', 'don_vi_tinh', 'so_luong', 'gia_tri', 'phan_loai')))
        df.to_excel(output, index=False)
        output.seek(0)
        response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="bao_cao_ton_kho_b3.xlsx"'
        return response

    return render(request, 'z115_app/bao_cao_ton_kho_b3.html', {
        'page_obj': page_obj,
        'search_vat_tu': search_vat_tu,
        'search_ma_vat_tu': search_ma_vat_tu,
        'filter_phan_loai': filter_phan_loai,
        'backup_dates': backup_dates if request.user.username in ['PHUONGB3', 'THUYB3'] else None,
        'upload_date': upload_date
    })