{"version": 3, "file": "summernote.css", "mappings": ";;;;;;;;;;;;AAMA;IACE;IACA;IACA;IACA;IACA;ACLF;ADSA;;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ACPF;;ADYA;IACE;IACA;ACTF;;ADYA;IACE;IACA;IACA;ACTF;;ADYA;IACE;ACTF;;ADYA;IACE;ACTF;;ADaE;IACE;ACVJ;ADYE;IACE;ACVJ;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;AC5QA;6CAAA;AAQA;6CAAA;AAEA;IACE;ADyQF;ACpQE;IACE;IACA;IACA;IACA,mBANe;IAOf;IACA;ADsQJ;ACpQI;IACE;IACA;IACA;IACA;IACA;ADsQN;ACnQI;IACE,cAlBoB;ADuR1B;ACjQE;IACE;ADmQJ;AChQE;IACE;ADkQJ;AChQI;IACE;ADkQN;AChQM;IACE;ADkQR;AC/PM;IACE;ADiQR;AC9PM;IACE,kBAlDW;ADkTnB;AC7PM;IACE,iBAvDU;ADsTlB;;ACzPA;6CAAA;AAEA;;IAEE;AD4PF;ACvPM;;IACE;AD0PR;ACxPM;;IACE;AD2PR;ACtPE;;IACE;ADyPJ;ACtPI;;IACE;IACA;IACA;ADyPN;ACvPM;;IACE,2BA3FW;ADqVnB;ACrPI;;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ICFJ,0BDKwB;ICFxB,sBDEwB;ICnExB,gBDoEqB;IACjB;AD2PN;ACtPE;;IACE;IACA;IACA;IACA;IACA;ADyPJ;ACxPI;;IACE;AD2PN;ACtPE;;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ADyPJ;ACtPE;;IACE;IACA;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;IACA;IACA;IACA;IACA;ADyPJ;ACtPE;;IACE;ADyPJ;ACtPE;;IACE;IACA;ADyPJ;ACtPE;;IACE;IACA;ADyPJ;ACtPE;;IACE;IACA;ADyPJ;ACtPE;;IACE;IACA;ADyPJ;ACrPE;;IACE,2BAhNe;IAiNf;IACA;IACA;ADwPJ;ACvPI;;IACE;IACA;IACA;IACA;AD0PN;ACzPM;;IACE;IACA;IACA;AD4PR;ACvPM;;IACE;AD0PR;ACzPQ;;IACE;AD4PV;ACvPE;;IACE;AD0PJ;;ACtPA;IACE;ADyPF;ACtPI;IACE;ADwPN;;AClPA;6CAAA;AAEA;IACE;IACA;ADqPF;AClPI;IACE;IACA;IACA;IACA;IACA;IACA;ADoPN;ACjPE;IACE;ADmPJ;;AC/OA;6CAAA;AAEA;IACE;ADkPF;;AC/OA;IACE;IACA;ADkPF;AChPE;IACE;IACA;IACA;ADkPJ;AC9OI;IACE;IACA;ADgPN;AC/OM;IACE;ADiPR;AChPQ;IACE;IACA;IACA;IACA;IACA;ADkPV;AChPQ;IACE;IACA;IACA;IACA;IACA;ADkPV;AChPQ;IACE;IACA;IACA;IACA;IACA;ADkPV;AC1OM;IACE;IACA;AD4OR;AC1OM;IACE;IACA;AD4OR;ACtOI;IACE;ADwON;ACnOI;IACE;IACA;ADqON;AClOM;IACE;IACA;IACA;ADoOR;ACnOQ;IACE;ADqOV;AClOQ;IACE;IACA;IACA;IACA;ADoOV;ACjOQ;;;IAEE;IACA;IACA;IACA;IACA;IChUR,kBDiUyB;ADsO3B;ACpOU;;;IACE;ADwOZ;ACpOQ;IACE;ADsOV;ACnOQ;IACE;ADqOV;ACjOU;IACE;ADmOZ;AC3NI;IACE;IACA;AD6NN;AC5NM;IACE;AD8NR;ACxNE;IACE;AD0NJ;ACtNI;IACE;IACA;ADwNN;ACvNM;IACE;IACA;ADyNR;ACvNM;IACE;IACA;ADyNR;ACpNM;IACE;IACA;ADsNR;ACpNM;IACE;ADsNR;ACjNE;IACE;ADmNJ;AC/ME;IACE;ADiNJ;AC/MM;IACE;IACA;IACA;IACA;IACA;IACA;ADiNR;AC/MM;IACE;IACA;ADiNR;;AC3MA;6CAAA;AAGE;IACE;IACA;ICnWF,wCDoWsB;AD+MxB;AC7ME;IACE;IACA;AD+MJ;AC7ME;IACE;AD+MJ;AC5MI;IACE;IACA;IACA;IACA;IACA;IACA;IACA;AD8MN;ACzME;IACE;QACE;ID2MF;AACJ;;ACvMA;6CAAA;AAEA;IACE;IACA;IACA;AD0MF;;ACvMA;6CAAA;AAIE;IACE;IACA;IACA;ADwMJ;ACvMI;IACE;ADyMN;ACtMI;IACE;IACA;IACA;ICjcJ,oBDkcqB;ICjcrB,mBDicqB;IChcrB,iBDgcqB;IC/brB,YD+bqB;IC7brB;IACA;AFyoBF;AC1MI;IACE;IACA;IACA;AD4MN;ACrMI;IAEE;ADsMN;ACnMI;IACE;IACA;IACA;IACA;ADqMN;AClMI;IACE;IACA;IACA;IACA;ADoMN;ACjMI;IACE;IACA;IACA;IACA;ADmMN;AChMI;IACE;IACA;IACA;ADkMN;AC/LI;IACE;IACA;IACA;ADiMN;AC9LI;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IC9gBJ,kBD+gBqB;IC7frB,oBD8fqB;IC7frB,mBD6fqB;IC5frB,iBD4fqB;IC3frB,YD2fqB;ICzfrB;IACA;AF+rBF;;AClMA;IACE;IACA;ADqMF;ACnME;IACE;IACA;IACA;ADqMJ;AClMM;IACE;IACA;ADoMR;AClMQ;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ADoMV;;AC7LA;6CAAA;AAGE;IACE;AD+LJ", "sources": ["webpack:///./src/styles/summernote/font.scss", "webpack:///./src/styles/bs3/summernote-bs3.scss", "webpack:///./src/styles/summernote/common.scss", "webpack:///./src/styles/summernote/elements.scss"], "sourcesContent": ["// Variables\n\n$sni-css-prefix: note-icon !default;\n\n// Path\n\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: auto;\n  src: url(\"./font/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"./font/summernote.woff2\") format(\"woff2\"), url(\"./font/summernote.woff\") format(\"woff\"), url(\"./font/summernote.ttf\") format(\"truetype\");}\n\n// Core\n\n[class^=\"#{$sni-css-prefix}\"]:before,\n[class*=\" #{$sni-css-prefix}\"]:before {\n  display: inline-block;\n  font-family: \"summernote\";\n  font-style: normal;\n  font-size: inherit;\n  text-decoration: inherit;\n  text-rendering: auto;\n  text-transform: none;\n  vertical-align: middle;\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  speak: none;\n}\n\n// Extras\n\n.#{$sni-css-prefix}-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.#{$sni-css-prefix}-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.#{$sni-css-prefix}-pull-left {\n  float: left;\n}\n\n.#{$sni-css-prefix}-pull-right {\n  float: right;\n}\n\n.#{$sni-css-prefix} {\n  &.#{$sni-css-prefix}-pull-left {\n    margin-right: 0.3em;\n  }\n  &.#{$sni-css-prefix}-pull-right {\n    margin-left: 0.3em;\n  }\n}\n\n// Functions\n\n@function char($character-code) {\n  @if function-exists(\"selector-append\") {\n    @return unquote(\"\\\"\\\\#{$character-code}\\\"\");\n  }\n\n  @if \"\\\\#{'x'}\" == \"\\\\x\" {\n    @return str-slice(\"\\x\", 1, 1) + $character-code;\n  }\n  @else {\n    @return #{\"\\\"\\\\\"}#{$character-code + \"\\\"\"};\n  }\n}\n\n// Icons\n\n\n.note-icon-align::before {\n  content: \"\\ea01\";\n}\n\n.note-icon-align-center::before {\n  content: \"\\ea02\";\n}\n\n.note-icon-align-indent::before {\n  content: \"\\ea03\";\n}\n\n.note-icon-align-justify::before {\n  content: \"\\ea04\";\n}\n\n.note-icon-align-left::before {\n  content: \"\\ea05\";\n}\n\n.note-icon-align-outdent::before {\n  content: \"\\ea06\";\n}\n\n.note-icon-align-right::before {\n  content: \"\\ea07\";\n}\n\n.note-icon-arrow-circle-down::before {\n  content: \"\\ea08\";\n}\n\n.note-icon-arrow-circle-left::before {\n  content: \"\\ea09\";\n}\n\n.note-icon-arrow-circle-right::before {\n  content: \"\\ea0a\";\n}\n\n.note-icon-arrow-circle-up::before {\n  content: \"\\ea0b\";\n}\n\n.note-icon-arrows-alt::before {\n  content: \"\\ea0c\";\n}\n\n.note-icon-arrows-h::before {\n  content: \"\\ea0d\";\n}\n\n.note-icon-arrows-v::before {\n  content: \"\\ea0e\";\n}\n\n.note-icon-bold::before {\n  content: \"\\ea0f\";\n}\n\n.note-icon-caret::before {\n  content: \"\\ea10\";\n}\n\n.note-icon-chain-broken::before {\n  content: \"\\ea11\";\n}\n\n.note-icon-circle::before {\n  content: \"\\ea12\";\n}\n\n.note-icon-close::before {\n  content: \"\\ea13\";\n}\n\n.note-icon-code::before {\n  content: \"\\ea14\";\n}\n\n.note-icon-col-after::before {\n  content: \"\\ea15\";\n}\n\n.note-icon-col-before::before {\n  content: \"\\ea16\";\n}\n\n.note-icon-col-remove::before {\n  content: \"\\ea17\";\n}\n\n.note-icon-eraser::before {\n  content: \"\\ea18\";\n}\n\n.note-icon-float-left::before {\n  content: \"\\ea19\";\n}\n\n.note-icon-float-none::before {\n  content: \"\\ea1a\";\n}\n\n.note-icon-float-right::before {\n  content: \"\\ea1b\";\n}\n\n.note-icon-font::before {\n  content: \"\\ea1c\";\n}\n\n.note-icon-frame::before {\n  content: \"\\ea1d\";\n}\n\n.note-icon-italic::before {\n  content: \"\\ea1e\";\n}\n\n.note-icon-link::before {\n  content: \"\\ea1f\";\n}\n\n.note-icon-magic::before {\n  content: \"\\ea20\";\n}\n\n.note-icon-menu-check::before {\n  content: \"\\ea21\";\n}\n\n.note-icon-minus::before {\n  content: \"\\ea22\";\n}\n\n.note-icon-orderedlist::before {\n  content: \"\\ea23\";\n}\n\n.note-icon-pencil::before {\n  content: \"\\ea24\";\n}\n\n.note-icon-picture::before {\n  content: \"\\ea25\";\n}\n\n.note-icon-question::before {\n  content: \"\\ea26\";\n}\n\n.note-icon-redo::before {\n  content: \"\\ea27\";\n}\n\n.note-icon-rollback::before {\n  content: \"\\ea28\";\n}\n\n.note-icon-row-above::before {\n  content: \"\\ea29\";\n}\n\n.note-icon-row-below::before {\n  content: \"\\ea2a\";\n}\n\n.note-icon-row-remove::before {\n  content: \"\\ea2b\";\n}\n\n.note-icon-special-character::before {\n  content: \"\\ea2c\";\n}\n\n.note-icon-square::before {\n  content: \"\\ea2d\";\n}\n\n.note-icon-strikethrough::before {\n  content: \"\\ea2e\";\n}\n\n.note-icon-subscript::before {\n  content: \"\\ea2f\";\n}\n\n.note-icon-summernote::before {\n  content: \"\\ea30\";\n}\n\n.note-icon-superscript::before {\n  content: \"\\ea31\";\n}\n\n.note-icon-table::before {\n  content: \"\\ea32\";\n}\n\n.note-icon-text-height::before {\n  content: \"\\ea33\";\n}\n\n.note-icon-trash::before {\n  content: \"\\ea34\";\n}\n\n.note-icon-underline::before {\n  content: \"\\ea35\";\n}\n\n.note-icon-undo::before {\n  content: \"\\ea36\";\n}\n\n.note-icon-unorderedlist::before {\n  content: \"\\ea37\";\n}\n\n.note-icon-video::before {\n  content: \"\\ea38\";\n}\n\n", "@font-face {\n    font-family: \"summernote\";\n    font-style: normal;\n    font-weight: 400;\n    font-display: auto;\n    src: url(\"./font/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"./font/summernote.woff2\") format(\"woff2\"), url(\"./font/summernote.woff\") format(\"woff\"), url(\"./font/summernote.ttf\") format(\"truetype\");\n}\n[class^=note-icon]:before,\n[class*=\" note-icon\"]:before {\n    display: inline-block;\n    font-family: \"summernote\";\n    font-style: normal;\n    font-size: inherit;\n    text-decoration: inherit;\n    text-rendering: auto;\n    text-transform: none;\n    vertical-align: middle;\n    -moz-osx-font-smoothing: grayscale;\n    -webkit-font-smoothing: antialiased;\n    speak: none;\n}\n\n.note-icon-fw {\n    text-align: center;\n    width: 1.25em;\n}\n\n.note-icon-border {\n    border: solid 0.08em #eee;\n    border-radius: 0.1em;\n    padding: 0.2em 0.25em 0.15em;\n}\n\n.note-icon-pull-left {\n    float: left;\n}\n\n.note-icon-pull-right {\n    float: right;\n}\n\n.note-icon.note-icon-pull-left {\n    margin-right: 0.3em;\n}\n.note-icon.note-icon-pull-right {\n    margin-left: 0.3em;\n}\n\n.note-icon-align::before {\n    content: \"\\ea01\";\n}\n\n.note-icon-align-center::before {\n    content: \"\\ea02\";\n}\n\n.note-icon-align-indent::before {\n    content: \"\\ea03\";\n}\n\n.note-icon-align-justify::before {\n    content: \"\\ea04\";\n}\n\n.note-icon-align-left::before {\n    content: \"\\ea05\";\n}\n\n.note-icon-align-outdent::before {\n    content: \"\\ea06\";\n}\n\n.note-icon-align-right::before {\n    content: \"\\ea07\";\n}\n\n.note-icon-arrow-circle-down::before {\n    content: \"\\ea08\";\n}\n\n.note-icon-arrow-circle-left::before {\n    content: \"\\ea09\";\n}\n\n.note-icon-arrow-circle-right::before {\n    content: \"\\ea0a\";\n}\n\n.note-icon-arrow-circle-up::before {\n    content: \"\\ea0b\";\n}\n\n.note-icon-arrows-alt::before {\n    content: \"\\ea0c\";\n}\n\n.note-icon-arrows-h::before {\n    content: \"\\ea0d\";\n}\n\n.note-icon-arrows-v::before {\n    content: \"\\ea0e\";\n}\n\n.note-icon-bold::before {\n    content: \"\\ea0f\";\n}\n\n.note-icon-caret::before {\n    content: \"\\ea10\";\n}\n\n.note-icon-chain-broken::before {\n    content: \"\\ea11\";\n}\n\n.note-icon-circle::before {\n    content: \"\\ea12\";\n}\n\n.note-icon-close::before {\n    content: \"\\ea13\";\n}\n\n.note-icon-code::before {\n    content: \"\\ea14\";\n}\n\n.note-icon-col-after::before {\n    content: \"\\ea15\";\n}\n\n.note-icon-col-before::before {\n    content: \"\\ea16\";\n}\n\n.note-icon-col-remove::before {\n    content: \"\\ea17\";\n}\n\n.note-icon-eraser::before {\n    content: \"\\ea18\";\n}\n\n.note-icon-float-left::before {\n    content: \"\\ea19\";\n}\n\n.note-icon-float-none::before {\n    content: \"\\ea1a\";\n}\n\n.note-icon-float-right::before {\n    content: \"\\ea1b\";\n}\n\n.note-icon-font::before {\n    content: \"\\ea1c\";\n}\n\n.note-icon-frame::before {\n    content: \"\\ea1d\";\n}\n\n.note-icon-italic::before {\n    content: \"\\ea1e\";\n}\n\n.note-icon-link::before {\n    content: \"\\ea1f\";\n}\n\n.note-icon-magic::before {\n    content: \"\\ea20\";\n}\n\n.note-icon-menu-check::before {\n    content: \"\\ea21\";\n}\n\n.note-icon-minus::before {\n    content: \"\\ea22\";\n}\n\n.note-icon-orderedlist::before {\n    content: \"\\ea23\";\n}\n\n.note-icon-pencil::before {\n    content: \"\\ea24\";\n}\n\n.note-icon-picture::before {\n    content: \"\\ea25\";\n}\n\n.note-icon-question::before {\n    content: \"\\ea26\";\n}\n\n.note-icon-redo::before {\n    content: \"\\ea27\";\n}\n\n.note-icon-rollback::before {\n    content: \"\\ea28\";\n}\n\n.note-icon-row-above::before {\n    content: \"\\ea29\";\n}\n\n.note-icon-row-below::before {\n    content: \"\\ea2a\";\n}\n\n.note-icon-row-remove::before {\n    content: \"\\ea2b\";\n}\n\n.note-icon-special-character::before {\n    content: \"\\ea2c\";\n}\n\n.note-icon-square::before {\n    content: \"\\ea2d\";\n}\n\n.note-icon-strikethrough::before {\n    content: \"\\ea2e\";\n}\n\n.note-icon-subscript::before {\n    content: \"\\ea2f\";\n}\n\n.note-icon-summernote::before {\n    content: \"\\ea30\";\n}\n\n.note-icon-superscript::before {\n    content: \"\\ea31\";\n}\n\n.note-icon-table::before {\n    content: \"\\ea32\";\n}\n\n.note-icon-text-height::before {\n    content: \"\\ea33\";\n}\n\n.note-icon-trash::before {\n    content: \"\\ea34\";\n}\n\n.note-icon-underline::before {\n    content: \"\\ea35\";\n}\n\n.note-icon-undo::before {\n    content: \"\\ea36\";\n}\n\n.note-icon-unorderedlist::before {\n    content: \"\\ea37\";\n}\n\n.note-icon-video::before {\n    content: \"\\ea38\";\n}\n\n/* Theme Variables\n ------------------------------------------ */\n/* Layout\n ------------------------------------------ */\n.note-editor {\n    position: relative;\n}\n.note-editor .note-dropzone {\n    position: absolute;\n    display: none;\n    z-index: 100;\n    color: lightskyblue;\n    background-color: #fff;\n    opacity: 0.95;\n}\n.note-editor .note-dropzone .note-dropzone-message {\n    display: table-cell;\n    vertical-align: middle;\n    text-align: center;\n    font-size: 28px;\n    font-weight: 700;\n}\n.note-editor .note-dropzone.hover {\n    color: #098ddf;\n}\n.note-editor.dragover .note-dropzone {\n    display: table;\n}\n.note-editor .note-editing-area {\n    position: relative;\n}\n.note-editor .note-editing-area .note-editable {\n    outline: none;\n}\n.note-editor .note-editing-area .note-editable sup {\n    vertical-align: super;\n}\n.note-editor .note-editing-area .note-editable sub {\n    vertical-align: sub;\n}\n.note-editor .note-editing-area .note-editable img.note-float-left {\n    margin-right: 10px;\n}\n.note-editor .note-editing-area .note-editable img.note-float-right {\n    margin-left: 10px;\n}\n\n/* Frame mode layout\n ------------------------------------------ */\n.note-editor.note-frame,\n.note-editor.note-airframe {\n    border: 1px solid #00000032;\n}\n.note-editor.note-frame.codeview .note-editing-area .note-editable,\n.note-editor.note-airframe.codeview .note-editing-area .note-editable {\n    display: none;\n}\n.note-editor.note-frame.codeview .note-editing-area .note-codable,\n.note-editor.note-airframe.codeview .note-editing-area .note-codable {\n    display: block;\n}\n.note-editor.note-frame .note-editing-area,\n.note-editor.note-airframe .note-editing-area {\n    overflow: hidden;\n}\n.note-editor.note-frame .note-editing-area .note-editable,\n.note-editor.note-airframe .note-editing-area .note-editable {\n    padding: 10px;\n    overflow: auto;\n    word-wrap: break-word;\n}\n.note-editor.note-frame .note-editing-area .note-editable[contenteditable=false],\n.note-editor.note-airframe .note-editing-area .note-editable[contenteditable=false] {\n    background-color: #8080801d;\n}\n.note-editor.note-frame .note-editing-area .note-codable,\n.note-editor.note-airframe .note-editing-area .note-codable {\n    display: none;\n    width: 100%;\n    padding: 10px;\n    border: none;\n    box-shadow: none;\n    font-family: Menlo, Monaco, monospace, sans-serif;\n    font-size: 14px;\n    color: #ccc;\n    background-color: #222;\n    resize: none;\n    outline: none;\n    -ms-box-sizing: border-box;\n    -moz-box-sizing: border-box;\n    -webkit-box-sizing: border-box;\n    box-sizing: border-box;\n    -webkit-border-radius: 0;\n    -moz-border-radius: 0;\n    border-radius: 0;\n    margin-bottom: 0;\n}\n.note-editor.note-frame.fullscreen,\n.note-editor.note-airframe.fullscreen {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    z-index: 1050;\n}\n.note-editor.note-frame.fullscreen .note-resizebar,\n.note-editor.note-airframe.fullscreen .note-resizebar {\n    display: none;\n}\n.note-editor.note-frame .note-status-output,\n.note-editor.note-airframe .note-status-output {\n    display: block;\n    width: 100%;\n    font-size: 14px;\n    line-height: 1.42857143;\n    height: 20px;\n    margin-bottom: 0;\n    color: #000;\n    border: 0;\n    border-top: 1px solid #e2e2e2;\n}\n.note-editor.note-frame .note-status-output:empty,\n.note-editor.note-airframe .note-status-output:empty {\n    height: 0;\n    border-top: 0 solid transparent;\n}\n.note-editor.note-frame .note-status-output .pull-right,\n.note-editor.note-airframe .note-status-output .pull-right {\n    float: right !important;\n}\n.note-editor.note-frame .note-status-output .text-muted,\n.note-editor.note-airframe .note-status-output .text-muted {\n    color: #777;\n}\n.note-editor.note-frame .note-status-output .text-primary,\n.note-editor.note-airframe .note-status-output .text-primary {\n    color: #286090;\n}\n.note-editor.note-frame .note-status-output .text-success,\n.note-editor.note-airframe .note-status-output .text-success {\n    color: #3c763d;\n}\n.note-editor.note-frame .note-status-output .text-info,\n.note-editor.note-airframe .note-status-output .text-info {\n    color: #31708f;\n}\n.note-editor.note-frame .note-status-output .text-warning,\n.note-editor.note-airframe .note-status-output .text-warning {\n    color: #8a6d3b;\n}\n.note-editor.note-frame .note-status-output .text-danger,\n.note-editor.note-airframe .note-status-output .text-danger {\n    color: #a94442;\n}\n.note-editor.note-frame .note-status-output .alert,\n.note-editor.note-airframe .note-status-output .alert {\n    margin: -7px 0 0 0;\n    padding: 7px 10px 2px 10px;\n    border-radius: 0;\n    color: #000;\n    background-color: #f5f5f5;\n}\n.note-editor.note-frame .note-status-output .alert .note-icon,\n.note-editor.note-airframe .note-status-output .alert .note-icon {\n    margin-right: 5px;\n}\n.note-editor.note-frame .note-status-output .alert-success,\n.note-editor.note-airframe .note-status-output .alert-success {\n    color: #3c763d !important;\n    background-color: #dff0d8 !important;\n}\n.note-editor.note-frame .note-status-output .alert-info,\n.note-editor.note-airframe .note-status-output .alert-info {\n    color: #31708f !important;\n    background-color: #d9edf7 !important;\n}\n.note-editor.note-frame .note-status-output .alert-warning,\n.note-editor.note-airframe .note-status-output .alert-warning {\n    color: #8a6d3b !important;\n    background-color: #fcf8e3 !important;\n}\n.note-editor.note-frame .note-status-output .alert-danger,\n.note-editor.note-airframe .note-status-output .alert-danger {\n    color: #a94442 !important;\n    background-color: #f2dede !important;\n}\n.note-editor.note-frame .note-statusbar,\n.note-editor.note-airframe .note-statusbar {\n    background-color: #8080801d;\n    border-bottom-left-radius: 4px;\n    border-bottom-right-radius: 4px;\n    border-top: 1px solid #00000032;\n}\n.note-editor.note-frame .note-statusbar .note-resizebar,\n.note-editor.note-airframe .note-statusbar .note-resizebar {\n    padding-top: 1px;\n    height: 9px;\n    width: 100%;\n    cursor: ns-resize;\n}\n.note-editor.note-frame .note-statusbar .note-resizebar .note-icon-bar,\n.note-editor.note-airframe .note-statusbar .note-resizebar .note-icon-bar {\n    width: 20px;\n    margin: 1px auto;\n    border-top: 1px solid #00000032;\n}\n.note-editor.note-frame .note-statusbar.locked .note-resizebar,\n.note-editor.note-airframe .note-statusbar.locked .note-resizebar {\n    cursor: default;\n}\n.note-editor.note-frame .note-statusbar.locked .note-resizebar .note-icon-bar,\n.note-editor.note-airframe .note-statusbar.locked .note-resizebar .note-icon-bar {\n    display: none;\n}\n.note-editor.note-frame .note-placeholder,\n.note-editor.note-airframe .note-placeholder {\n    padding: 10px;\n}\n\n.note-editor.note-airframe {\n    border: 0;\n}\n.note-editor.note-airframe .note-editing-area .note-editable {\n    padding: 0;\n}\n\n/* Popover\n ------------------------------------------ */\n.note-popover.popover {\n    display: none;\n    max-width: none;\n}\n.note-popover.popover .popover-content a {\n    display: inline-block;\n    max-width: 200px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    vertical-align: middle;\n}\n.note-popover.popover .arrow {\n    left: 20px !important;\n}\n\n/* Popover and Toolbar (Button container)\n ------------------------------------------ */\n.note-toolbar {\n    position: relative;\n}\n\n.note-popover .popover-content, .note-editor .note-toolbar {\n    margin: 0;\n    padding: 0 0 5px 5px;\n}\n.note-popover .popover-content > .note-btn-group, .note-editor .note-toolbar > .note-btn-group {\n    margin-top: 5px;\n    margin-left: 0;\n    margin-right: 5px;\n}\n.note-popover .popover-content .note-btn-group .note-table, .note-editor .note-toolbar .note-btn-group .note-table {\n    min-width: 0;\n    padding: 5px;\n}\n.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker, .note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker {\n    font-size: 18px;\n}\n.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher, .note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-mousecatcher {\n    position: absolute !important;\n    z-index: 3;\n    width: 10em;\n    height: 10em;\n    cursor: pointer;\n}\n.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted, .note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-unhighlighted {\n    position: relative !important;\n    z-index: 1;\n    width: 5em;\n    height: 5em;\n    background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n}\n.note-popover .popover-content .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted, .note-editor .note-toolbar .note-btn-group .note-table .note-dimension-picker .note-dimension-picker-highlighted {\n    position: absolute !important;\n    z-index: 2;\n    width: 1em;\n    height: 1em;\n    background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n}\n.note-popover .popover-content .note-style .dropdown-style blockquote, .note-popover .popover-content .note-style .dropdown-style pre, .note-editor .note-toolbar .note-style .dropdown-style blockquote, .note-editor .note-toolbar .note-style .dropdown-style pre {\n    margin: 0;\n    padding: 5px 10px;\n}\n.note-popover .popover-content .note-style .dropdown-style h1, .note-popover .popover-content .note-style .dropdown-style h2, .note-popover .popover-content .note-style .dropdown-style h3, .note-popover .popover-content .note-style .dropdown-style h4, .note-popover .popover-content .note-style .dropdown-style h5, .note-popover .popover-content .note-style .dropdown-style h6, .note-popover .popover-content .note-style .dropdown-style p, .note-editor .note-toolbar .note-style .dropdown-style h1, .note-editor .note-toolbar .note-style .dropdown-style h2, .note-editor .note-toolbar .note-style .dropdown-style h3, .note-editor .note-toolbar .note-style .dropdown-style h4, .note-editor .note-toolbar .note-style .dropdown-style h5, .note-editor .note-toolbar .note-style .dropdown-style h6, .note-editor .note-toolbar .note-style .dropdown-style p {\n    margin: 0;\n    padding: 0;\n}\n.note-popover .popover-content .note-color-all .note-dropdown-menu, .note-editor .note-toolbar .note-color-all .note-dropdown-menu {\n    min-width: 337px;\n}\n.note-popover .popover-content .note-color .dropdown-toggle, .note-editor .note-toolbar .note-color .dropdown-toggle {\n    width: 20px;\n    padding-left: 5px;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette {\n    display: inline-block;\n    margin: 0;\n    width: 160px;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette:first-child, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette:first-child {\n    margin: 0 5px;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-palette-title, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-palette-title {\n    font-size: 12px;\n    margin: 2px 7px;\n    text-align: center;\n    border-bottom: 1px solid #eee;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset,\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset,\n.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select {\n    font-size: 11px;\n    margin: 3px;\n    padding: 0 3px;\n    cursor: pointer;\n    width: 100%;\n    -webkit-border-radius: 5px;\n    -moz-border-radius: 5px;\n    border-radius: 5px;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select:hover, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-reset:hover,\n.note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select:hover {\n    background: #eee;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-row, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-row {\n    height: 20px;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-color-select-btn, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-color-select-btn {\n    display: none;\n}\n.note-popover .popover-content .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn, .note-editor .note-toolbar .note-color .note-dropdown-menu .note-palette .note-holder-custom .note-color-btn {\n    border: 1px solid #eee;\n}\n.note-popover .popover-content .note-para .note-dropdown-menu, .note-editor .note-toolbar .note-para .note-dropdown-menu {\n    min-width: 228px;\n    padding: 5px;\n}\n.note-popover .popover-content .note-para .note-dropdown-menu > div + div, .note-editor .note-toolbar .note-para .note-dropdown-menu > div + div {\n    margin-left: 5px;\n}\n.note-popover .popover-content .note-dropdown-menu, .note-editor .note-toolbar .note-dropdown-menu {\n    min-width: 160px;\n}\n.note-popover .popover-content .note-dropdown-menu.right, .note-editor .note-toolbar .note-dropdown-menu.right {\n    right: 0;\n    left: auto;\n}\n.note-popover .popover-content .note-dropdown-menu.right::before, .note-editor .note-toolbar .note-dropdown-menu.right::before {\n    right: 9px;\n    left: auto !important;\n}\n.note-popover .popover-content .note-dropdown-menu.right::after, .note-editor .note-toolbar .note-dropdown-menu.right::after {\n    right: 10px;\n    left: auto !important;\n}\n.note-popover .popover-content .note-dropdown-menu.note-check a i, .note-editor .note-toolbar .note-dropdown-menu.note-check a i {\n    color: deepskyblue;\n    visibility: hidden;\n}\n.note-popover .popover-content .note-dropdown-menu.note-check a.checked i, .note-editor .note-toolbar .note-dropdown-menu.note-check a.checked i {\n    visibility: visible;\n}\n.note-popover .popover-content .note-fontsize-10, .note-editor .note-toolbar .note-fontsize-10 {\n    font-size: 10px;\n}\n.note-popover .popover-content .note-color-palette, .note-editor .note-toolbar .note-color-palette {\n    line-height: 1;\n}\n.note-popover .popover-content .note-color-palette div .note-color-btn, .note-editor .note-toolbar .note-color-palette div .note-color-btn {\n    width: 20px;\n    height: 20px;\n    padding: 0;\n    margin: 0;\n    border: 0;\n    border-radius: 0;\n}\n.note-popover .popover-content .note-color-palette div .note-color-btn:hover, .note-editor .note-toolbar .note-color-palette div .note-color-btn:hover {\n    transform: scale(1.2);\n    transition: all 0.2s;\n}\n\n/* Dialog\n ------------------------------------------ */\n.note-modal .modal-dialog {\n    outline: 0;\n    border-radius: 5px;\n    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n    -moz-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n}\n.note-modal .form-group {\n    margin-left: 0;\n    margin-right: 0;\n}\n.note-modal .note-modal-form {\n    margin: 0;\n}\n.note-modal .note-image-dialog .note-dropzone {\n    min-height: 100px;\n    font-size: 30px;\n    line-height: 4;\n    color: lightgray;\n    text-align: center;\n    border: 4px dashed lightgray;\n    margin-bottom: 10px;\n}\n@-moz-document url-prefix() {\n    .note-modal .note-image-input {\n        height: auto;\n    }\n}\n\n/* Placeholder\n ------------------------------------------ */\n.note-placeholder {\n    position: absolute;\n    display: none;\n    color: gray;\n}\n\n/* Handle\n ------------------------------------------ */\n.note-handle .note-control-selection {\n    position: absolute;\n    display: none;\n    border: 1px solid #000;\n}\n.note-handle .note-control-selection > div {\n    position: absolute;\n}\n.note-handle .note-control-selection .note-control-selection-bg {\n    width: 100%;\n    height: 100%;\n    background-color: #000;\n    -webkit-opacity: 0.3;\n    -khtml-opacity: 0.3;\n    -moz-opacity: 0.3;\n    opacity: 0.3;\n    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=30);\n    filter: alpha(opacity=30);\n}\n.note-handle .note-control-selection .note-control-handle, .note-handle .note-control-selection .note-control-sizing, .note-handle .note-control-selection .note-control-holder {\n    width: 7px;\n    height: 7px;\n    border: 1px solid #000;\n}\n.note-handle .note-control-selection .note-control-sizing {\n    background-color: #000;\n}\n.note-handle .note-control-selection .note-control-nw {\n    top: -5px;\n    left: -5px;\n    border-right: none;\n    border-bottom: none;\n}\n.note-handle .note-control-selection .note-control-ne {\n    top: -5px;\n    right: -5px;\n    border-bottom: none;\n    border-left: none;\n}\n.note-handle .note-control-selection .note-control-sw {\n    bottom: -5px;\n    left: -5px;\n    border-top: none;\n    border-right: none;\n}\n.note-handle .note-control-selection .note-control-se {\n    right: -5px;\n    bottom: -5px;\n    cursor: se-resize;\n}\n.note-handle .note-control-selection .note-control-se.note-control-holder {\n    cursor: default;\n    border-top: none;\n    border-left: none;\n}\n.note-handle .note-control-selection .note-control-selection-info {\n    right: 0;\n    bottom: 0;\n    padding: 5px;\n    margin: 5px;\n    color: #fff;\n    background-color: #000;\n    font-size: 12px;\n    -webkit-border-radius: 5px;\n    -moz-border-radius: 5px;\n    border-radius: 5px;\n    -webkit-opacity: 0.7;\n    -khtml-opacity: 0.7;\n    -moz-opacity: 0.7;\n    opacity: 0.7;\n    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=70);\n    filter: alpha(opacity=70);\n}\n\n.note-hint-popover {\n    min-width: 100px;\n    padding: 2px;\n}\n.note-hint-popover .popover-content {\n    padding: 3px;\n    max-height: 150px;\n    overflow: auto;\n}\n.note-hint-popover .popover-content .note-hint-group .note-hint-item {\n    display: block !important;\n    padding: 3px;\n}\n.note-hint-popover .popover-content .note-hint-group .note-hint-item.active, .note-hint-popover .popover-content .note-hint-group .note-hint-item:hover {\n    display: block;\n    clear: both;\n    font-weight: 400;\n    line-height: 1.4;\n    color: white;\n    white-space: nowrap;\n    text-decoration: none;\n    background-color: #428bca;\n    outline: 0;\n    cursor: pointer;\n}\n\n/* Handle\n ------------------------------------------ */\nhtml .note-fullscreen-body, body .note-fullscreen-body {\n    overflow: hidden !important;\n}", "@import \"elements.scss\";\n\n/* Theme Variables\n ------------------------------------------ */\n$border-color: #00000032;\n$background-color: #8080801d;\n\n$img-margin-left: 10px;\n$img-margin-right: 10px;\n\n/* Layout\n ------------------------------------------ */\n.note-editor {\n  position: relative;\n\n  // dropzone\n  $dropzone-color: lightskyblue;\n  $dropzone-active-color: darken($dropzone-color, 30);\n  .note-dropzone {\n    position: absolute;\n    display: none;\n    z-index: 100;\n    color: $dropzone-color;\n    background-color: #fff;\n    opacity: 0.95;\n\n    .note-dropzone-message {\n      display: table-cell;\n      vertical-align: middle;\n      text-align: center;\n      font-size: 28px;\n      font-weight: 700;\n    }\n\n    &.hover {\n      color: $dropzone-active-color;\n    }\n  }\n\n  &.dragover .note-dropzone {\n    display: table;\n  }\n\n  .note-editing-area {\n    position: relative;\n\n    .note-editable {\n      outline: none;\n\n      sup {\n        vertical-align: super;\n      }\n\n      sub {\n        vertical-align: sub;\n      }\n\n      img.note-float-left {\n        margin-right: $img-margin-right;\n      }\n\n      img.note-float-right {\n        margin-left: $img-margin-left;\n      }\n    }\n  }\n}\n\n/* Frame mode layout\n ------------------------------------------ */\n.note-editor.note-frame,\n.note-editor.note-airframe {\n  border: 1px solid $border-color;\n\n  // codeview mode\n  &.codeview {\n    .note-editing-area {\n      .note-editable {\n        display: none;\n      }\n      .note-codable {\n        display: block;\n      }\n    }\n  }\n\n  .note-editing-area {\n    overflow: hidden;\n\n    // editable\n    .note-editable {\n      padding: 10px;\n      overflow: auto;\n      word-wrap: break-word;\n\n      &[contenteditable=\"false\"] {\n        background-color: $background-color;\n      }\n    }\n\n    // codeable\n    .note-codable {\n      display: none;\n      width: 100%;\n      padding: 10px;\n      border: none;\n      box-shadow: none;\n      font-family: Menlo, Monaco, monospace, sans-serif;\n      font-size: 14px;\n      color: #ccc;\n      background-color: #222;\n      resize: none;\n      outline: none;\n\n      // override BS2 default style\n      @include box-sizing(border-box);\n      @include rounded(0);\n      margin-bottom: 0;\n    }\n  }\n\n  // fullscreen mode\n  &.fullscreen {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    z-index: 1050; // bs3 modal-backdrop: 1030, bs2: 1040\n    .note-resizebar {\n      display: none;\n    }\n  }\n\n  // Notifications\n  .note-status-output {\n    display: block;\n    width: 100%;\n    font-size: 14px;\n    line-height: 1.42857143;\n    height: 20px;\n    margin-bottom: 0;\n    color: #000;\n    border: 0;\n    border-top: 1px solid #e2e2e2;\n  }\n\n  .note-status-output:empty {\n    height: 0;\n    border-top: 0 solid transparent;\n  }\n\n  .note-status-output .pull-right {\n    float: right !important;\n  }\n\n  .note-status-output .text-muted {\n    color: #777;\n  }\n\n  .note-status-output .text-primary {\n    color: #286090;\n  }\n\n  .note-status-output .text-success {\n    color: #3c763d;\n  }\n\n  .note-status-output .text-info {\n    color: #31708f;\n  }\n\n  .note-status-output .text-warning {\n    color: #8a6d3b;\n  }\n\n  .note-status-output .text-danger {\n    color: #a94442;\n  }\n\n  .note-status-output .alert {\n    margin: -7px 0 0 0;\n    padding: 7px 10px 2px 10px;\n    border-radius: 0;\n    color: #000;\n    background-color: #f5f5f5;\n  }\n\n  .note-status-output .alert .note-icon {\n    margin-right: 5px;\n  }\n\n  .note-status-output .alert-success {\n    color: #3c763d !important;\n    background-color: #dff0d8 !important;\n  }\n\n  .note-status-output .alert-info {\n    color: #31708f !important;\n    background-color: #d9edf7 !important;\n  }\n\n  .note-status-output .alert-warning {\n    color: #8a6d3b !important;\n    background-color: #fcf8e3 !important;\n  }\n\n  .note-status-output .alert-danger {\n    color: #a94442 !important;\n    background-color: #f2dede !important;\n  }\n\n  // statusbar\n  .note-statusbar {\n    background-color: $background-color;\n    border-bottom-left-radius: 4px;\n    border-bottom-right-radius: 4px;\n    border-top: 1px solid $border-color;\n    .note-resizebar {\n      padding-top: 1px;\n      height: 9px;\n      width: 100%;\n      cursor: ns-resize;\n      .note-icon-bar {\n        width: 20px;\n        margin: 1px auto;\n        border-top: 1px solid $border-color;\n      }\n    }\n\n    &.locked {\n      .note-resizebar {\n        cursor: default;\n        .note-icon-bar {\n          display: none;\n        }\n      }\n    }\n  }\n  .note-placeholder {\n    padding: 10px;\n  }\n}\n\n.note-editor.note-airframe {\n  border: 0;\n\n  .note-editing-area {\n    .note-editable {\n      padding: 0;\n    }\n  }\n}\n\n\n/* Popover\n ------------------------------------------ */\n.note-popover.popover {\n  display: none;\n  max-width: none;\n\n  .popover-content {\n    a {\n      display: inline-block;\n      max-width: 200px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap; // for FF\n      vertical-align: middle; // for FF\n    }\n  }\n  .arrow {\n    left: 20px !important;\n  }\n}\n\n/* Popover and Toolbar (Button container)\n ------------------------------------------ */\n.note-toolbar {\n  position: relative;\n}\n\n.note-popover .popover-content, .note-editor .note-toolbar {\n  margin: 0;\n  padding: 0 0 5px 5px;\n\n  & > .note-btn-group {\n    margin-top: 5px;\n    margin-left: 0;\n    margin-right: 5px;\n  }\n\n  .note-btn-group {\n    .note-table {\n      min-width: 0;\n      padding: 5px;\n      .note-dimension-picker {\n        font-size: 18px;\n        .note-dimension-picker-mousecatcher {\n          position: absolute !important;\n          z-index: 3;\n          width: 10em;\n          height: 10em;\n          cursor: pointer;\n        }\n        .note-dimension-picker-unhighlighted {\n          position: relative !important;\n          z-index: 1;\n          width: 5em;\n          height: 5em;\n          background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n        }\n        .note-dimension-picker-highlighted {\n          position: absolute !important;\n          z-index: 2;\n          width: 1em;\n          height: 1em;\n          background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n        }\n      }\n    }\n  }\n\n  .note-style {\n    .dropdown-style {\n      blockquote, pre {\n        margin: 0;\n        padding: 5px 10px;\n      }\n      h1, h2, h3, h4, h5, h6, p {\n        margin: 0;\n        padding: 0;\n      }\n    }\n  }\n\n  .note-color-all {\n    .note-dropdown-menu {\n      min-width: 337px;\n    }\n  }\n\n  .note-color {\n    .dropdown-toggle {\n      width: 20px;\n      padding-left: 5px;\n    }\n    .note-dropdown-menu {\n      .note-palette {\n        display: inline-block;\n        margin: 0;\n        width: 160px;\n        &:first-child {\n          margin: 0 5px;\n        }\n\n        .note-palette-title {\n          font-size: 12px;\n          margin: 2px 7px;\n          text-align: center;\n          border-bottom: 1px solid #eee;\n        }\n\n        .note-color-reset,\n        .note-color-select {\n          font-size: 11px;\n          margin: 3px;\n          padding: 0 3px;\n          cursor: pointer;\n          width: 100%;\n          @include rounded(5px);\n\n          &:hover {\n            background: #eee;\n          }\n        }\n\n        .note-color-row {\n          height: 20px;\n        }\n\n        .note-color-select-btn {\n          display: none;\n        }\n\n        .note-holder-custom {\n          .note-color-btn {\n            border: 1px solid #eee;\n          }\n        }\n      }\n    }\n  }\n\n  .note-para {\n    .note-dropdown-menu {\n      min-width: 228px;\n      padding: 5px;\n      & > div + div {\n        margin-left: 5px;\n      }\n    }\n  }\n\n  // dropdown-menu for toolbar and popover\n  .note-dropdown-menu {\n    min-width: 160px;\n\n    // dropdown-menu right position\n    // http://forrst.com/posts/Bootstrap_right_positioned_dropdown-2KB\n    &.right {\n      right: 0;\n      left: auto;\n      &::before {\n        right: 9px;\n        left: auto !important;\n      }\n      &::after {\n        right: 10px;\n        left: auto !important;\n      }\n    }\n    // dropdown-menu for selectbox\n    &.note-check {\n      a i {\n        color: deepskyblue;\n        visibility: hidden;\n      }\n      a.checked i {\n        visibility: visible;\n      }\n    }\n  }\n\n  .note-fontsize-10 {\n    font-size: 10px;\n  }\n\n  // color palette for toolbar and popover\n  .note-color-palette {\n    line-height: 1;\n    div {\n      .note-color-btn {\n        width: 20px;\n        height: 20px;\n        padding: 0;\n        margin: 0;\n        border: 0;\n        border-radius: 0;\n      }\n      .note-color-btn:hover {\n        transform: scale(1.2);\n        transition: all 0.2s;\n      }\n    }\n  }\n}\n\n/* Dialog\n ------------------------------------------ */\n.note-modal {\n  .modal-dialog {\n    outline: 0;\n    border-radius: 5px;\n    @include box-shadow(0 3px 9px rgba(0,0,0,.5));\n  }\n  .form-group { // overwrite BS's form-horizontal minus margins\n    margin-left: 0;\n    margin-right: 0;\n  }\n  .note-modal-form {\n    margin: 0; // overwrite BS2's form margin bottom\n  }\n  .note-image-dialog {\n    .note-dropzone {\n      min-height: 100px;\n      font-size: 30px;\n      line-height: 4; // vertical-align\n      color: lightgray;\n      text-align: center;\n      border: 4px dashed lightgray;\n      margin-bottom: 10px;\n    }\n  }\n\n  // [workaround] firefox fileinput\n  @-moz-document url-prefix() {\n    .note-image-input {\n      height: auto;\n    }\n  }\n}\n\n/* Placeholder\n ------------------------------------------ */\n.note-placeholder {\n  position: absolute;\n  display: none;\n  color: gray;\n}\n\n/* Handle\n ------------------------------------------ */\n.note-handle {\n  // control selection\n  .note-control-selection {\n    position: absolute;\n    display: none;\n    border: 1px solid #000;\n    & > div {\n      position: absolute;\n    }\n\n    .note-control-selection-bg {\n      width: 100%;\n      height: 100%;\n      background-color: #000;\n      @include opacity(0.3);\n    }\n\n    .note-control-handle {\n      width: 7px;\n      height: 7px;\n      border: 1px solid #000;\n    }\n\n    .note-control-holder {\n      @extend .note-control-handle;\n    }\n\n    .note-control-sizing {\n      @extend .note-control-handle;\n      background-color: #000;\n    }\n\n    .note-control-nw {\n      top: -5px;\n      left: -5px;\n      border-right: none;\n      border-bottom: none;\n    }\n\n    .note-control-ne {\n      top: -5px;\n      right: -5px;\n      border-bottom: none;\n      border-left: none;\n    }\n\n    .note-control-sw {\n      bottom: -5px;\n      left: -5px;\n      border-top: none;\n      border-right: none;\n    }\n\n    .note-control-se {\n      right: -5px;\n      bottom: -5px;\n      cursor: se-resize;\n    }\n\n    .note-control-se.note-control-holder {\n      cursor: default;\n      border-top: none;\n      border-left: none;\n    }\n\n    .note-control-selection-info {\n      right: 0;\n      bottom: 0;\n      padding: 5px;\n      margin: 5px;\n      color: #fff;\n      background-color: #000;\n      font-size: 12px;\n      @include rounded(5px);\n      @include opacity(0.7);\n    }\n  }\n}\n\n.note-hint-popover {\n  min-width: 100px;\n  padding: 2px;\n\n  .popover-content {\n    padding: 3px;\n    max-height: 150px;\n    overflow: auto;\n\n    .note-hint-group {\n      .note-hint-item {\n        display: block !important;\n        padding: 3px;\n\n        &.active, &:hover {\n          display: block;\n          clear: both;\n          font-weight: 400;\n          line-height: 1.4;\n          color: white;\n          white-space: nowrap;\n          text-decoration: none;\n          background-color: #428bca;\n          outline: 0;\n          cursor: pointer;\n        }\n      }\n    }\n  }\n}\n\n/* Handle\n ------------------------------------------ */\nhtml, body {\n  .note-fullscreen-body {\n    overflow: hidden !important;\n  }\n}\n", "@mixin gradient($color: #F5F5F5, $start: #EEE, $stop: #FFF) {\n  background: $color;\n  background: -webkit-gradient(linear,\n                               left bottom,\n                               left top,\n                               color-stop(0, $start),\n                               color-stop(1, $stop));\n  background: -ms-linear-gradient(bottom,\n                                  $start,\n                                  $stop);\n  background: -moz-linear-gradient(center bottom,\n                                   $start 0%,\n                                   $stop 100%);\n  background: -o-linear-gradient($stop,\n                                 $start);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($stop)}', endColorstr='#{ie-hex-str($start)}', GradientType=0);\n}\n@mixin bw-gradient($color: #F5F5F5, $start: 0, $stop: 255) {\n  background: $color;\n  background: -webkit-gradient(linear,\n                               left bottom,\n                               left top,\n                               color-stop(0, rgb($start,$start,$start)),\n                               color-stop(1, rgb($stop,$stop,$stop)));\n  background: -ms-linear-gradient(bottom,\n                                  rgb($start,$start,$start) 0%,\n                                  rgb($stop,$stop,$stop) 100%);\n  background: -moz-linear-gradient(center bottom,\n                                   rgb($start,$start,$start) 0%,\n                                   rgb($stop,$stop,$stop) 100%);\n  background: -o-linear-gradient(rgb($stop,$stop,$stop),\n                                 rgb($start,$start,$start));\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str(rgb($stop,$stop,$stop))}', endColorstr='#{ie-hex-str(rgb($start,$start,$start))}', GradientType=0);\n}\n@mixin bordered($top-color: #EEE, $right-color: #EEE, $bottom-color: #EEE, $left-color: #EEE) {\n  border-top: solid 1px $top-color;\n  border-left: solid 1px $left-color;\n  border-right: solid 1px $right-color;\n  border-bottom: solid 1px $bottom-color;\n}\n@mixin drop-shadow($x-axis: 0, $y-axis: 1px, $blur: 2px, $alpha: 0.1) {\n  -webkit-box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n  -moz-box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n  box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n}\n@mixin rounded($radius: 2px) {\n  -webkit-border-radius: $radius;\n  -moz-border-radius: $radius;\n  border-radius: $radius;\n}\n@mixin border-radius($topright: 0, $bottomright: 0, $bottomleft: 0, $topleft: 0) {\n  -webkit-border-top-right-radius: $topright;\n  -webkit-border-bottom-right-radius: $bottomright;\n  -webkit-border-bottom-left-radius: $bottomleft;\n  -webkit-border-top-left-radius: $topleft;\n  -moz-border-radius-topright: $topright;\n  -moz-border-radius-bottomright: $bottomright;\n  -moz-border-radius-bottomleft: $bottomleft;\n  -moz-border-radius-topleft: $topleft;\n  border-top-right-radius: $topright;\n  border-bottom-right-radius: $bottomright;\n  border-bottom-left-radius: $bottomleft;\n  border-top-left-radius: $topleft;\n  @include background-clip(padding-box);\n}\n@mixin opacity($opacity: 0.5) {\n  -webkit-opacity: $opacity;\n  -khtml-opacity: $opacity;\n  -moz-opacity: $opacity;\n  opacity: $opacity;\n  $opperc: $opacity * 100;\n  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=#{$opperc});\n  filter: alpha(opacity=$opperc);\n}\n@mixin transition-duration($duration: 0.2s) {\n  -moz-transition-duration: $duration;\n  -webkit-transition-duration: $duration;\n  -o-transition-duration: $duration;\n  transition-duration: $duration;\n}\n@mixin transform($arguments...) {\n  -webkit-transform: $arguments;\n  -moz-transform: $arguments;\n  -o-transform: $arguments;\n  -ms-transform: $arguments;\n  transform: $arguments;\n}\n@mixin rotation($deg:5deg) {\n  @include transform(rotate($deg));\n}\n@mixin scale($ratio:1.5) {\n  @include transform(scale($ratio));\n}\n@mixin transition($duration:0.2s, $ease:ease-out) {\n  -webkit-transition: all $duration $ease;\n  -moz-transition: all $duration $ease;\n  -o-transition: all $duration $ease;\n  transition: all $duration $ease;\n}\n@mixin inner-shadow($horizontal:0, $vertical:1px, $blur:2px, $alpha: 0.4) {\n  -webkit-box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n  -moz-box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n  box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n}\n@mixin box-shadow($arguments) {\n  -webkit-box-shadow: $arguments;\n  -moz-box-shadow: $arguments;\n  box-shadow: $arguments;\n}\n@mixin box-sizing($sizing: border-box) {\n  -ms-box-sizing: $sizing;\n  -moz-box-sizing: $sizing;\n  -webkit-box-sizing: $sizing;\n  box-sizing: $sizing;\n}\n@mixin user-select($argument: none) {\n  -webkit-user-select: $argument;\n  -moz-user-select: $argument;\n  -ms-user-select: $argument;\n  user-select: $argument;\n}\n@mixin columns($colwidth: 250px, $colcount: 0, $colgap: 50px, $columnRuleColor: #EEE, $columnRuleStyle: solid, $columnRuleWidth: 1px) {\n  -moz-column-width: $colwidth;\n  -moz-column-count: $colcount;\n  -moz-column-gap: $colgap;\n  -moz-column-rule-color: $columnRuleColor;\n  -moz-column-rule-style: $columnRuleStyle;\n  -moz-column-rule-width: $columnRuleWidth;\n  -webkit-column-width: $colwidth;\n  -webkit-column-count: $colcount;\n  -webkit-column-gap: $colgap;\n  -webkit-column-rule-color: $columnRuleColor;\n  -webkit-column-rule-style: $columnRuleStyle;\n  -webkit-column-rule-width: $columnRuleWidth;\n  column-width: $colwidth;\n  column-count: $colcount;\n  column-gap: $colgap;\n  column-rule-color: $columnRuleColor;\n  column-rule-style: $columnRuleStyle;\n  column-rule-width: $columnRuleWidth;\n}\n@mixin translate($x:0, $y:0) {\n  @include transform(translate($x, $y));\n}\n@mixin background-clip($argument: padding-box) {\n  -moz-background-clip: $argument;\n  -webkit-background-clip: $argument;\n  background-clip: $argument;\n}\n"], "names": [], "sourceRoot": ""}