{"version": 3, "file": "lang/summernote-da-DK.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,OALJ;AAMJC,QAAAA,IAAI,EAAE,YANF;AAOJC,QAAAA,aAAa,EAAE,eAPX;AAQJC,QAAAA,SAAS,EAAE,eARP;AASJC,QAAAA,WAAW,EAAE,cATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,oBAHP;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,aAAa,EAAE,iBALV;AAMLC,QAAAA,SAAS,EAAE,gBANN;AAOLC,QAAAA,UAAU,EAAE,cAPP;AAQLC,QAAAA,SAAS,EAAE,mBARN;AASLC,QAAAA,YAAY,EAAE,oBATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,iBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,qBAbV;AAcLC,QAAAA,SAAS,EAAE,cAdN;AAeLC,QAAAA,eAAe,EAAE,iBAfZ;AAgBLC,QAAAA,eAAe,EAAE,oBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,kDAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,eAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,aAFJ;AAGJuB,QAAAA,MAAM,EAAE,YAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,4BAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,mBAFR;AAGLC,QAAAA,WAAW,EAAE,oBAHR;AAILC,QAAAA,UAAU,EAAE,wBAJP;AAKLC,QAAAA,WAAW,EAAE,sBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,cAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,cALC;AAMLC,QAAAA,EAAE,EAAE,cANC;AAOLC,QAAAA,EAAE,EAAE,cAPC;AAQLC,QAAAA,EAAE,EAAE,cARC;AASLC,QAAAA,EAAE,EAAE,cATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,sBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,YAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,QADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,cAHC;AAITC,QAAAA,IAAI,EAAE,gBAJG;AAKTC,QAAAA,MAAM,EAAE,WALC;AAMTC,QAAAA,KAAK,EAAE,cANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,oBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,iBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,SADH;AAERC,QAAAA,KAAK,EAAE,KAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,cANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,iBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-da-DK.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'da-DK': {\n      font: {\n        bold: 'Fed',\n        italic: 'Kursiv',\n        underline: 'Understreget',\n        clear: 'Fjern formatering',\n        height: '<PERSON><PERSON><PERSON><PERSON>',\n        name: 'Skrifttype',\n        strikethrough: '<PERSON><PERSON>mstreget',\n        subscript: 'Sænket skrift',\n        superscript: 'Hævet skrift',\n        size: 'Skriftstørrelse',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Indsæt billede',\n        resizeFull: 'Original størrelse',\n        resizeHalf: 'Halv størrelse',\n        resizeQuarter: '<PERSON>vart størrelse',\n        floatLeft: 'Venstrestillet',\n        floatRight: 'Højrestillet',\n        floatNone: 'Fjern formatering',\n        shapeRounded: 'Form: Runde kanter',\n        shapeCircle: 'Form: Cirkel',\n        shapeThumbnail: 'Form: Miniature',\n        shapeNone: 'Form: Ingen',\n        dragImageHere: 'Træk billede hertil',\n        dropImage: 'Slip billede',\n        selectFromFiles: 'Vælg billed-fil',\n        maximumFileSize: 'Maks fil størrelse',\n        maximumFileSizeError: 'Filen er større end maks tilladte fil størrelse!',\n        url: 'Billede URL',\n        remove: 'Fjern billede',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video Link',\n        insert: 'Indsæt Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Indsæt link',\n        unlink: 'Fjern link',\n        edit: 'Rediger',\n        textToDisplay: 'Visningstekst',\n        url: 'Hvor skal linket pege hen?',\n        openInNewWindow: 'Åbn i nyt vindue',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Tilføj række over',\n        addRowBelow: 'Tilføj række under',\n        addColLeft: 'Tilføj venstre kolonne',\n        addColRight: 'Tilføj højre kolonne',\n        delRow: 'Slet række',\n        delCol: 'Slet kolonne',\n        delTable: 'Slet tabel',\n      },\n      hr: {\n        insert: 'Indsæt horisontal linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Kode',\n        h1: 'Overskrift 1',\n        h2: 'Overskrift 2',\n        h3: 'Overskrift 3',\n        h4: 'Overskrift 4',\n        h5: 'Overskrift 5',\n        h6: 'Overskrift 6',\n      },\n      lists: {\n        unordered: 'Punktopstillet liste',\n        ordered: 'Nummereret liste',\n      },\n      options: {\n        help: 'Hjælp',\n        fullscreen: 'Fuld skærm',\n        codeview: 'HTML-Visning',\n      },\n      paragraph: {\n        paragraph: 'Afsnit',\n        outdent: 'Formindsk indryk',\n        indent: 'Forøg indryk',\n        left: 'Venstrestillet',\n        center: 'Centreret',\n        right: 'Højrestillet',\n        justify: 'Blokjuster',\n      },\n      color: {\n        recent: 'Nyligt valgt farve',\n        more: 'Flere farver',\n        background: 'Baggrund',\n        foreground: 'Forgrund',\n        transparent: 'Transparent',\n        setTransparent: 'Sæt transparent',\n        reset: 'Nulstil',\n        resetToDefault: 'Gendan standardindstillinger',\n      },\n      shortcut: {\n        shortcuts: 'Genveje',\n        close: 'Luk',\n        textFormatting: 'Tekstformatering',\n        action: 'Handling',\n        paragraphFormatting: 'Afsnitsformatering',\n        documentStyle: 'Dokumentstil',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Indsæt paragraf',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Vis Link Dialog',\n      },\n      history: {\n        undo: 'Fortryd',\n        redo: 'Annuller fortryd',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Vælg special karakterer',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}