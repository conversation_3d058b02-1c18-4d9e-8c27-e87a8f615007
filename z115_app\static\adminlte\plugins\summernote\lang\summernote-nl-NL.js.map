{"version": 3, "file": "lang/summernote-nl-NL.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,aALJ;AAMJC,QAAAA,IAAI,EAAE,YANF;AAOJC,QAAAA,aAAa,EAAE,WAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,YADF;AAELC,QAAAA,MAAM,EAAE,qBAFH;AAGLC,QAAAA,UAAU,EAAE,mBAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,aAAa,EAAE,eALV;AAMLC,QAAAA,SAAS,EAAE,iBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,iBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,oCAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,uBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,uBAlBA;AAmBLC,QAAAA,MAAM,EAAE,sBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,kBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,kBAHJ;AAIJC,QAAAA,IAAI,EAAE,UAJF;AAKJC,QAAAA,aAAa,EAAE,gBALX;AAMJT,QAAAA,GAAG,EAAE,0CAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,wBAFR;AAGLC,QAAAA,WAAW,EAAE,wBAHR;AAILC,QAAAA,UAAU,EAAE,uBAJP;AAKLC,QAAAA,WAAW,EAAE,wBALR;AAMLC,QAAAA,MAAM,EAAE,eANH;AAOLC,QAAAA,MAAM,EAAE,iBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,SAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,OALC;AAMLC,QAAAA,EAAE,EAAE,OANC;AAOLC,QAAAA,EAAE,EAAE,OAPC;AAQLC,QAAAA,EAAE,EAAE,OARC;AASLC,QAAAA,EAAE,EAAE,OATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,mBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,iBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,uBAFA;AAGTC,QAAAA,MAAM,EAAE,sBAHC;AAITC,QAAAA,IAAI,EAAE,iBAJG;AAKTC,QAAAA,MAAM,EAAE,WALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,eADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,mBAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,aANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,cAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,kBALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,iBADf;AAEJ,gBAAQ,kCAFJ;AAGJ,gBAAQ,qCAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,aALL;AAMJ,gBAAQ,uBANJ;AAOJ,kBAAU,2BAPN;AAQJ,qBAAa,gCART;AASJ,yBAAiB,iCATb;AAUJ,wBAAgB,iBAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,+BAdX;AAeJ,+BAAuB,mCAfnB;AAgBJ,6BAAqB,iCAhBjB;AAiBJ,mBAAW,sCAjBP;AAkBJ,kBAAU,8BAlBN;AAmBJ,sBAAc,kDAnBV;AAoBJ,oBAAY,+BApBR;AAqBJ,oBAAY,+BArBR;AAsBJ,oBAAY,+BAtBR;AAuBJ,oBAAY,+BAvBR;AAwBJ,oBAAY,+BAxBR;AAyBJ,oBAAY,+BAzBR;AA0BJ,gCAAwB,2BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,gBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,iBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-nl-NL.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'nl-NL': {\n      font: {\n        bold: 'Vet',\n        italic: 'Cursief',\n        underline: 'Onderstrepen',\n        clear: 'Stijl verwijderen',\n        height: '<PERSON><PERSON><PERSON>og<PERSON>',\n        name: 'Lettertype',\n        strikethrough: '<PERSON>hale<PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Tekstgrootte',\n      },\n      image: {\n        image: 'Afbeelding',\n        insert: 'Afbeelding invoegen',\n        resizeFull: 'Volledige breedte',\n        resizeHalf: 'Halve breedte',\n        resizeQuarter: 'Kwart breedte',\n        floatLeft: '<PERSON>s uitlijnen',\n        floatRight: 'Rechts uitlijnen',\n        floatNone: 'Geen uitlijning',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Sleep hier een afbeelding naar toe',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Selecteer een bestand',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL van de afbeelding',\n        remove: 'Verwijder afbeelding',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video link',\n        insert: 'Video invoegen',\n        url: 'URL van de video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion of Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link invoegen',\n        unlink: 'Link verwijderen',\n        edit: 'Wijzigen',\n        textToDisplay: 'Tekst van link',\n        url: 'Naar welke URL moet deze link verwijzen?',\n        openInNewWindow: 'Open in nieuw venster',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Rij hierboven invoegen',\n        addRowBelow: 'Rij hieronder invoegen',\n        addColLeft: 'Kolom links toevoegen',\n        addColRight: 'Kolom rechts toevoegen',\n        delRow: 'Verwijder rij',\n        delCol: 'Verwijder kolom',\n        delTable: 'Verwijder tabel',\n      },\n      hr: {\n        insert: 'Horizontale lijn invoegen',\n      },\n      style: {\n        style: 'Stijl',\n        p: 'Normaal',\n        blockquote: 'Quote',\n        pre: 'Code',\n        h1: 'Kop 1',\n        h2: 'Kop 2',\n        h3: 'Kop 3',\n        h4: 'Kop 4',\n        h5: 'Kop 5',\n        h6: 'Kop 6',\n      },\n      lists: {\n        unordered: 'Ongeordende lijst',\n        ordered: 'Geordende lijst',\n      },\n      options: {\n        help: 'Help',\n        fullscreen: 'Volledig scherm',\n        codeview: 'Bekijk Code',\n      },\n      paragraph: {\n        paragraph: 'Paragraaf',\n        outdent: 'Inspringen verkleinen',\n        indent: 'Inspringen vergroten',\n        left: 'Links uitlijnen',\n        center: 'Centreren',\n        right: 'Rechts uitlijnen',\n        justify: 'Uitvullen',\n      },\n      color: {\n        recent: 'Recente kleur',\n        more: 'Meer kleuren',\n        background: 'Achtergrond kleur',\n        foreground: 'Tekst kleur',\n        transparent: 'Transparant',\n        setTransparent: 'Transparant',\n        reset: 'Standaard',\n        resetToDefault: 'Standaard kleur',\n      },\n      shortcut: {\n        shortcuts: 'Toetsencombinaties',\n        close: 'sluiten',\n        textFormatting: 'Tekststijlen',\n        action: 'Acties',\n        paragraphFormatting: 'Paragraafstijlen',\n        documentStyle: 'Documentstijlen',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Alinea invoegen',\n        'undo': 'Laatste handeling ongedaan maken',\n        'redo': 'Laatste handeling opnieuw uitvoeren',\n        'tab': 'Tab',\n        'untab': 'Herstel tab',\n        'bold': 'Stel stijl in als vet',\n        'italic': 'Stel stijl in als cursief',\n        'underline': 'Stel stijl in als onderstreept',\n        'strikethrough': 'Stel stijl in als doorgestreept',\n        'removeFormat': 'Verwijder stijl',\n        'justifyLeft': 'Lijn links uit',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Lijn rechts uit',\n        'justifyFull': 'Lijn uit op volledige breedte',\n        'insertUnorderedList': 'Zet ongeordende lijstweergave aan',\n        'insertOrderedList': 'Zet geordende lijstweergave aan',\n        'outdent': 'Verwijder inspringing huidige alinea',\n        'indent': 'Inspringen op huidige alinea',\n        'formatPara': 'Wijzig formattering huidig blok in alinea(P tag)',\n        'formatH1': 'Formatteer huidig blok als H1',\n        'formatH2': 'Formatteer huidig blok als H2',\n        'formatH3': 'Formatteer huidig blok als H3',\n        'formatH4': 'Formatteer huidig blok als H4',\n        'formatH5': 'Formatteer huidig blok als H5',\n        'formatH6': 'Formatteer huidig blok als H6',\n        'insertHorizontalRule': 'Invoegen horizontale lijn',\n        'linkDialog.show': 'Toon Link Dialoogvenster',\n      },\n      history: {\n        undo: 'Ongedaan maken',\n        redo: 'Opnieuw doorvoeren',\n      },\n      specialChar: {\n        specialChar: 'SPECIALE TEKENS',\n        select: 'Selecteer Speciale Tekens',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}