{"version": 3, "file": "lang/summernote-pt-BR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,yBAJH;AAKJC,QAAAA,MAAM,EAAE,iBALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,6BAHP;AAILC,QAAAA,UAAU,EAAE,2BAJP;AAKLC,QAAAA,aAAa,EAAE,2BALV;AAMLC,QAAAA,SAAS,EAAE,uBANN;AAOLC,QAAAA,UAAU,EAAE,sBAPP;AAQLC,QAAAA,SAAS,EAAE,aARN;AASLC,QAAAA,YAAY,EAAE,oBATT;AAULC,QAAAA,WAAW,EAAE,gBAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,eAZN;AAaLC,QAAAA,aAAa,EAAE,iCAbV;AAcLC,QAAAA,SAAS,EAAE,uBAdN;AAeLC,QAAAA,eAAe,EAAE,iCAfZ;AAgBLC,QAAAA,eAAe,EAAE,2BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,qCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,eAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,iBAFN;AAGLpB,QAAAA,MAAM,EAAE,eAHH;AAILgB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,cAFJ;AAGJuB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,+BAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,uBAFR;AAGLC,QAAAA,WAAW,EAAE,wBAHR;AAILC,QAAAA,UAAU,EAAE,6BAJP;AAKLC,QAAAA,WAAW,EAAE,4BALR;AAMLC,QAAAA,MAAM,EAAE,eANH;AAOLC,QAAAA,MAAM,EAAE,gBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,QAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,sBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,YAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,YAFD;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,UAAU,EAAE,OAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE,kBARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA9FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,MAJA;AAKRC,QAAAA,mBAAmB,EAAE,yBALb;AAMRC,QAAAA,aAAa,EAAE,qBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP3B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,mBADf;AAEJ,gBAAQ,2BAFJ;AAGJ,gBAAQ,0BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,cALL;AAMJ,gBAAQ,oBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,YART;AASJ,yBAAiB,SATb;AAUJ,wBAAgB,gBAVZ;AAWJ,uBAAe,oBAXX;AAYJ,yBAAiB,aAZb;AAaJ,wBAAgB,oBAbZ;AAcJ,uBAAe,YAdX;AAeJ,+BAAuB,oBAfnB;AAgBJ,6BAAqB,gBAhBjB;AAiBJ,mBAAW,wBAjBP;AAkBJ,kBAAU,yBAlBN;AAmBJ,sBAAc,gDAnBV;AAoBJ,oBAAY,kCApBR;AAqBJ,oBAAY,kCArBR;AAsBJ,oBAAY,kCAtBR;AAuBJ,oBAAY,kCAvBR;AAwBJ,oBAAY,kCAxBR;AAyBJ,oBAAY,kCAzBR;AA0BJ,gCAAwB,0BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,sBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pt-BR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'pt-BR': {\n      font: {\n        bold: 'Negrito',\n        italic: 'Itálico',\n        underline: 'Sublinhado',\n        clear: 'Remover estilo da fonte',\n        height: '<PERSON><PERSON> da linha',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        subscript: 'Subscrito',\n        superscript: 'Sobrescrito',\n        size: 'Tamanho da fonte',\n      },\n      image: {\n        image: 'Imagem',\n        insert: 'Inserir imagem',\n        resizeFull: 'Redimensionar Completamente',\n        resizeHalf: 'Redimensionar pela Metade',\n        resizeQuarter: 'Redimensionar a um Quarto',\n        floatLeft: 'Flutuar para Esquerda',\n        floatRight: 'Flutuar para Direita',\n        floatNone: 'Não Flutuar',\n        shapeRounded: 'Forma: Arredondado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Miniatura',\n        shapeNone: 'Forma: Nenhum',\n        dragImageHere: 'Arraste Imagem ou Texto para cá',\n        dropImage: 'Solte Imagem ou Texto',\n        selectFromFiles: 'Selecione a partir dos arquivos',\n        maximumFileSize: 'Tamanho máximo do arquivo',\n        maximumFileSizeError: 'Tamanho máximo do arquivo excedido.',\n        url: 'URL da imagem',\n        remove: 'Remover Imagem',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Link para vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserir link',\n        unlink: 'Remover link',\n        edit: 'Editar',\n        textToDisplay: 'Texto para exibir',\n        url: 'Para qual URL este link leva?',\n        openInNewWindow: 'Abrir em uma nova janela',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Adicionar linha acima',\n        addRowBelow: 'Adicionar linha abaixo',\n        addColLeft: 'Adicionar coluna à esquerda',\n        addColRight: 'Adicionar coluna à direita',\n        delRow: 'Excluir linha',\n        delCol: 'Excluir coluna',\n        delTable: 'Excluir tabela',\n      },\n      hr: {\n        insert: 'Linha horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Citação',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista com marcadores',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ajuda',\n        fullscreen: 'Tela cheia',\n        codeview: 'Ver código-fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menor tabulação',\n        indent: 'Maior tabulação',\n        left: 'Alinhar à esquerda',\n        center: 'Alinhar ao centro',\n        right: 'Alinha à direita',\n        justify: 'Justificado',\n      },\n      color: {\n        recent: 'Cor recente',\n        more: 'Mais cores',\n        background: 'Fundo',\n        foreground: 'Fonte',\n        transparent: 'Transparente',\n        setTransparent: 'Fundo transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar padrão',\n        cpSelect: 'Selecionar',\n      },\n      shortcut: {\n        shortcuts: 'Atalhos do teclado',\n        close: 'Fechar',\n        textFormatting: 'Formatação de texto',\n        action: 'Ação',\n        paragraphFormatting: 'Formatação de parágrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Inserir Parágrafo',\n        'undo': 'Desfazer o último comando',\n        'redo': 'Refazer o último comando',\n        'tab': 'Tab',\n        'untab': 'Desfazer tab',\n        'bold': 'Colocar em negrito',\n        'italic': 'Colocar em itálico',\n        'underline': 'Sublinhado',\n        'strikethrough': 'Tachado',\n        'removeFormat': 'Remover estilo',\n        'justifyLeft': 'Alinhar à esquerda',\n        'justifyCenter': 'Centralizar',\n        'justifyRight': 'Alinhar à esquerda',\n        'justifyFull': 'Justificar',\n        'insertUnorderedList': 'Lista não ordenada',\n        'insertOrderedList': 'Lista ordenada',\n        'outdent': 'Recuar parágrafo atual',\n        'indent': 'Avançar parágrafo atual',\n        'formatPara': 'Alterar formato do bloco para parágrafo(tag P)',\n        'formatH1': 'Alterar formato do bloco para H1',\n        'formatH2': 'Alterar formato do bloco para H2',\n        'formatH3': 'Alterar formato do bloco para H3',\n        'formatH4': 'Alterar formato do bloco para H4',\n        'formatH5': 'Alterar formato do bloco para H5',\n        'formatH6': 'Alterar formato do bloco para H6',\n        'insertHorizontalRule': 'Inserir Régua horizontal',\n        'linkDialog.show': 'Inserir um Hiperlink',\n      },\n      history: {\n        undo: 'Desfazer',\n        redo: 'Refazer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIAIS',\n        select: 'Selecionar Caracteres Especiais',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}