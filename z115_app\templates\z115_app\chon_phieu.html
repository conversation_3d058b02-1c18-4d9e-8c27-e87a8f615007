{% extends 'z115_app/base.html' %}
{% load static %}

{% block sub_content %}
    <div class="container mt-4">
        <h2 style="font-family: 'Times New Roman', Times, serif; font-size: 20px; font-weight: bold; color: #0000FF;">CHỌN PHIẾU ĐỂ IN</h2>
        <select class="form-select mb-3" id="phieuSelect" onchange="window.location.href='{% url 'in_phieu' phieu_id=1 %}'.replace('1', this.value)">
            {% for phieu in phieus %}
                <option value="{{ phieu.id }}">{{ phieu.so_phieu }} - {{ phieu.don_vi_nhan }}</option>
            {% empty %}
                <option value="">Không có phiếu nào</option>
            {% endfor %}
        </select>
    </div>
{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'z115_app/css/bootstrap.min.css' %}">
{% endblock %}

{% block extra_js %}
    <script src="{% static 'z115_app/js/bootstrap.min.js' %}"></script>
{% endblock %}