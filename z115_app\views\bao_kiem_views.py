# views/bao_kiem_views.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from ..models import BaoKiemVatTu, BaoKiemBackup
import logging
from datetime import datetime
import pandas as pd
import io
import os

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def bao_kiem_vat_tu_kiem_tra(request):
    if request.user.username != 'PHONGB12':
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")

    # Khởi tạo session cho dữ liệu tạm
    temp_data = request.session.get('temp_baokiem_data', {})

    if request.method == "POST" and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if 'update_temp' in request.POST:
            for item_id in request.POST.getlist('item_id'):
                so_hop_cach = request.POST.get(f"so_hop_cach_{item_id}")
                ket_qua = request.POST.get(f"ket_qua_kiem_tra_{item_id}")
                ngay_tra = request.POST.get(f"ngay_tra_ket_qua_{item_id}")
                if so_hop_cach or ket_qua or ngay_tra:  # Chỉ cập nhật nếu có dữ liệu
                    temp_data[item_id] = {
                        'so_hop_cach': so_hop_cach,
                        'ket_qua_kiem_tra': ket_qua,
                        'ngay_tra_ket_qua': ngay_tra
                    }
            request.session['temp_baokiem_data'] = temp_data
            logger.debug(f"Temporary data updated: {temp_data}")
            return JsonResponse({'status': 'success', 'message': 'Dữ liệu đã được lưu tạm!'})
        elif 'save_all' in request.POST:
            saved_items = False
            error_items = []
            for item_id, data in temp_data.items():
                try:
                    item = BaoKiemVatTu.objects.get(id=item_id)
                    so_hop_cach = data.get('so_hop_cach')
                    ket_qua = data.get('ket_qua_kiem_tra')
                    ngay_tra = data.get('ngay_tra_ket_qua')
                    if so_hop_cach:
                        item.so_hop_cach = int(float(so_hop_cach))
                    if ket_qua:
                        item.ket_qua_kiem_tra = ket_qua
                    if ngay_tra:
                        item.ngay_tra_ket_qua = datetime.strptime(ngay_tra, '%Y-%m-%d').date()
                    item.save()
                    saved_items = True
                    logger.debug(f"Item {item_id} saved successfully with available data.")
                except ValueError as ve:
                    logger.error(f"Error saving item {item_id}: {ve}")
                    error_items.append(f"Lỗi khi lưu mục {item.ten_quy_cach}: Giá trị không hợp lệ - {str(ve)}.")
                except Exception as e:
                    logger.error(f"Unexpected error saving item {item_id}: {e}")
                    error_items.append(f"Lỗi không mong muốn khi lưu mục {item.ten_quy_cach}: {str(e)}.")
            
            # Xóa session sau khi lưu
            if saved_items:
                del request.session['temp_baokiem_data']
                return JsonResponse({'status': 'success', 'message': 'Cập nhật thành công tất cả các mục!', 'redirect': '/bao-kiem-vat-tu/'})
            if error_items:
                return JsonResponse({'status': 'error', 'message': '\n'.join(error_items)})
            return JsonResponse({'status': 'error', 'message': 'Không có dữ liệu để lưu!'})

    baokiem_list = BaoKiemVatTu.objects.filter(so_hop_cach__isnull=True).order_by('-ngay')
    if not baokiem_list.exists():
        baokiem_list = BaoKiemVatTu.objects.filter(so_hop_cach=0).order_by('-ngay')
    paginator = Paginator(baokiem_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'z115_app/bao_kiem_vat_tu_kiem_tra.html', {'page_obj': page_obj})

@login_required
def bao_kiem_vat_tu_da_kiem(request):
    if request.user.username != 'PHONGB12':
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")
    if request.method == "POST":
        if 'edit_item' in request.POST:
            pass
        elif 'save_item' in request.POST:
            item_id = request.POST.get('save_item')
            item = BaoKiemVatTu.objects.get(id=item_id)
            so_hop_cach = request.POST.get(f"so_hop_cach_{item_id}")
            ket_qua = request.POST.get(f"ket_qua_kiem_tra_{item_id}")
            ngay_tra = request.POST.get(f"ngay_tra_ket_qua_{item_id}")
            try:
                if so_hop_cach:
                    item.so_hop_cach = int(float(so_hop_cach))
                else:
                    item.so_hop_cach = 0
                item.ket_qua_kiem_tra = ket_qua if ket_qua else ''
                item.ngay_tra_ket_qua = datetime.strptime(ngay_tra, '%Y-%m-%d').date() if ngay_tra else None
                item.save()
                if item.so_hop_cach == 0 and not item.ket_qua_kiem_tra and item.ngay_tra_ket_qua is None:
                    messages.success(request, f"Cập nhật thành công! Dữ liệu của '{item.ten_quy_cach}' đã chuyển về VẬT TƯ CHỜ KIỂM.")
                    return redirect('bao_kiem_vat_tu_kiem_tra')
                else:
                    messages.success(request, f"Cập nhật thành công! Dữ liệu của '{item.ten_quy_cach}' vẫn ở VẬT TƯ ĐÃ KIỂM.")
                    return redirect('bao_kiem_vat_tu_da_kiem')
            except ValueError as ve:
                logger.error(f"Error saving item {item_id}: {ve}")
                messages.error(request, f"Lỗi khi lưu mục {item.ten_quy_cach}: Giá trị không hợp lệ - {str(ve)}.")
            except Exception as e:
                logger.error(f"Unexpected error saving item {item_id}: {e}")
                messages.error(request, f"Lỗi không mong muốn khi lưu mục {item.ten_quy_cach}: {str(e)}.")
            return redirect('bao_kiem_vat_tu_da_kiem')
    baokiem_list = BaoKiemVatTu.objects.exclude(so_hop_cach__isnull=True).exclude(so_hop_cach=0).order_by('-ngay')
    paginator = Paginator(baokiem_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'z115_app/bao_kiem_vat_tu_da_kiem.html', {'page_obj': page_obj})

@login_required
def bao_kiem_vat_tu(request):
    if request.method == "POST" and request.user.username == 'PHUONGB3':
        logger.debug(f"POST request received from {request.user.username}")
        phuongb3_password = request.POST.get('phuongb3_password', '')
        if phuongb3_password == '123456':
            if request.FILES.get('excel_file'):
                excel_file = request.FILES['excel_file']
                try:
                    df = pd.read_excel(excel_file)
                    logger.debug(f"Columns in Excel file: {df.columns.tolist()}")
                    required_columns = ['Ngày', 'Mã VLSPP', 'Mã kho', 'Tên quy cách vật tư', 'Dùng vào việc', 'Đvt', 'SL', 'Người báo kiểm', 'Nơi để vật tư chờ kiểm', 'Phòng B12 ký', 'Phòng B3 ký']
                    if not all(col in df.columns for col in required_columns):
                        raise ValueError(f"Thiếu cột cần thiết. Các cột yêu cầu: {required_columns}")

                    existing_records = BaoKiemVatTu.objects.values_list('ngay', 'ten_quy_cach', 'nguoi_bao_kiem')
                    new_records = 0
                    for index, row in df.iterrows():
                        try:
                            ngay = pd.to_datetime(row['Ngày'], format='%d/%m/%y').date()
                            ma_vlspp = str(row['Mã VLSPP']) if pd.notna(row['Mã VLSPP']) else ''
                            ma_kho = str(row['Mã kho']) if pd.notna(row['Mã kho']) else ''
                            ten_quy_cach = str(row['Tên quy cách vật tư'])
                            dung_vao_viec = str(row['Dùng vào việc'])
                            don_vi_tinh = str(row['Đvt'])
                            so_luong = float(row['SL']) if pd.notna(row['SL']) else 0.0
                            nguoi_bao_kiem = str(row['Người báo kiểm'])
                            noi_de_vat_tu = str(row['Nơi để vật tư chờ kiểm'])
                            phong_b12_ky = str(row['Phòng B12 ký']) if pd.notna(row['Phòng B12 ký']) else ''
                            phong_b3_ky = str(row['Phòng B3 ký']) if pd.notna(row['Phòng B3 ký']) else ''
                            if (ngay, ten_quy_cach, nguoi_bao_kiem) not in existing_records:
                                BaoKiemVatTu.objects.create(
                                    ngay=ngay, ma_vlspp=ma_vlspp, ma_kho=ma_kho, ten_quy_cach=ten_quy_cach,
                                    dung_vao_viec=dung_vao_viec, don_vi_tinh=don_vi_tinh, so_luong=so_luong,
                                    nguoi_bao_kiem=nguoi_bao_kiem, noi_de_vat_tu=noi_de_vat_tu,
                                    phong_b12_ky=phong_b12_ky, phong_b3_ky=phong_b3_ky
                                )
                                new_records += 1
                                logger.debug(f"Added new record: ngay={ngay}, ten_quy_cach={ten_quy_cach}, nguoi_bao_kiem={nguoi_bao_kiem}")
                        except ValueError as ve:
                            logger.error(f"Error at row {index + 2}: Invalid value - {ve}")
                            messages.error(request, f"Lỗi tại dòng {index + 2}: Giá trị không hợp lệ, vui lòng kiểm tra!")
                            continue
                        except Exception as e:
                            logger.error(f"Error at row {index + 2}: {e}")
                            messages.error(request, f"Lỗi tại dòng {index + 2}: {str(e)}")
                            continue
                    if new_records > 0:
                        messages.success(request, f"Đã tải và bổ sung {new_records} bản ghi mới thành công!")
                    else:
                        messages.info(request, "Không có dữ liệu mới để bổ sung (có thể đã tồn tại).")
                except Exception as e:
                    logger.error(f"Error processing Excel file: {e}")
                    messages.error(request, f"Lỗi khi xử lý file Excel: {str(e)}, vui lòng kiểm tra định dạng!")
            elif request.POST.get('reset_data'):
                count_before = BaoKiemVatTu.objects.count()
                BaoKiemVatTu.objects.all().delete()
                count_after = BaoKiemVatTu.objects.count()
                logger.debug(f"Reset: Before count={count_before}, After count={count_after}")
                messages.success(request, "ĐÃ RESET DỮ LIỆU THÀNH CÔNG!")
                return redirect('bao_kiem_vat_tu')
            elif request.POST.get('backup_data'):
                data = BaoKiemVatTu.objects.all()
                backup_date = datetime.now()
                for item in data:
                    BaoKiemBackup.objects.create(
                        ngay=item.ngay,
                        ma_vlspp=item.ma_vlspp,
                        ma_kho=item.ma_kho,
                        ten_quy_cach=item.ten_quy_cach,
                        dung_vao_viec=item.dung_vao_viec,
                        don_vi_tinh=item.don_vi_tinh,
                        so_luong=item.so_luong,
                        nguoi_bao_kiem=item.nguoi_bao_kiem,
                        noi_de_vat_tu=item.noi_de_vat_tu,
                        so_hop_cach=item.so_hop_cach,
                        ket_qua_kiem_tra=item.ket_qua_kiem_tra,
                        ngay_tra_ket_qua=item.ngay_tra_ket_qua,
                        phong_b12_ky=item.phong_b12_ky,
                        phong_b3_ky=item.phong_b3_ky,
                        backup_date=backup_date
                    )
                output = io.BytesIO()
                df = pd.DataFrame(list(data.values(
                    'ngay', 'ma_vlspp', 'ma_kho', 'ten_quy_cach', 'dung_vao_viec', 'don_vi_tinh',
                    'so_luong', 'nguoi_bao_kiem', 'noi_de_vat_tu', 'so_hop_cach', 'ket_qua_kiem_tra',
                    'ngay_tra_ket_qua', 'phong_b12_ky', 'phong_b3_ky'
                )))
                df['ngay'] = df['ngay'].astype(str)
                df['ngay_tra_ket_qua'] = df['ngay_tra_ket_qua'].astype(str)
                df.to_excel(output, index=False)
                output.seek(0)
                backup_folder = r"C:\Users\<USER>\z115_project\saoluu"
                os.makedirs(backup_folder, exist_ok=True)
                backup_filename = f'bao_kiem_vat_tu_backup_{backup_date.strftime("%Y%m%d_%H%M%S")}.xlsx'
                backup_file_path = os.path.join(backup_folder, backup_filename)
                try:
                    with open(backup_file_path, 'wb') as f:
                        f.write(output.getvalue())
                    logger.debug(f"Backup file saved to {backup_file_path}")
                    response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                    response['Content-Disposition'] = f'attachment; filename="{backup_filename}"'
                    messages.success(request, f"ĐÃ SAO LƯU DỮ LIỆU THÀNH CÔNG - FILE ĐÃ TẢI VỀ VÀ LƯU TẠI {backup_file_path}")
                    return response
                except Exception as e:
                    logger.error(f"Failed to save backup file: {e}")
                    messages.error(request, f"LỖI KHI LƯU FILE SAO LƯU: {str(e)}")
                    return redirect('bao_kiem_vat_tu')
            elif request.POST.get('restore_data'):
                backup_date_str = request.POST.get('backup_date')
                if backup_date_str:
                    backup_date = datetime.strptime(backup_date_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=None).date()
                    BaoKiemVatTu.objects.all().delete()
                    backups = BaoKiemBackup.objects.filter(backup_date__date=backup_date)
                    for backup in backups:
                        BaoKiemVatTu.objects.create(
                            ngay=backup.ngay,
                            ma_vlspp=backup.ma_vlspp,
                            ma_kho=backup.ma_kho,
                            ten_quy_cach=backup.ten_quy_cach,
                            dung_vao_viec=backup.dung_vao_viec,
                            don_vi_tinh=backup.don_vi_tinh,
                            so_luong=backup.so_luong,
                            nguoi_bao_kiem=backup.nguoi_bao_kiem,
                            noi_de_vat_tu=backup.noi_de_vat_tu,
                            so_hop_cach=backup.so_hop_cach,
                            ket_qua_kiem_tra=backup.ket_qua_kiem_tra,
                            ngay_tra_ket_qua=backup.ngay_tra_ket_qua,
                            phong_b12_ky=backup.phong_b12_ky,
                            phong_b3_ky=backup.phong_b3_ky
                        )
                    logger.debug(f"Restored data from backup date {backup_date}")
                    messages.success(request, f"ĐÃ KHÔI PHỤC DỮ LIỆU TỪ NGÀY SAO LƯU {backup_date}")
                else:
                    messages.error(request, "VUI LÒNG CHỌN NGÀY SAO LƯU!")
                return redirect('bao_kiem_vat_tu')
        else:
            logger.debug("Password authentication failed")
            messages.error(request, "MẬT KHẨU PHUONGB3 KHÔNG ĐÚNG!")
            return redirect('bao_kiem_vat_tu')

    baokiem_list = BaoKiemVatTu.objects.all().order_by('-ngay')
    logger.debug(f"Initial baokiem_list count: {baokiem_list.count()}")
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    search_name = request.GET.get('search_name', '').strip()
    search_kho = request.GET.get('search_kho', '').strip()
    filter_type = request.GET.get('filter_type', 'all')

    if from_date and to_date and from_date.strip() and to_date.strip():
        try:
            from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
            to_date = datetime.strptime(to_date, '%Y-%m-%d').date()
            baokiem_list = baokiem_list.filter(ngay__range=[from_date, to_date])
            logger.debug(f"After date filter ({from_date} to {to_date}): {baokiem_list.count()}")
        except ValueError:
            messages.error(request, "Định dạng ngày không hợp lệ!")
            from_date = None
            to_date = None
    if search_name:
        baokiem_list = baokiem_list.filter(ten_quy_cach__icontains=search_name)
        logger.debug(f"After search_name filter ({search_name}): {baokiem_list.count()}")
    if search_kho:
        baokiem_list = baokiem_list.filter(ma_kho__icontains=search_kho)
        logger.debug(f"After search_kho filter ({search_kho}): {baokiem_list.count()}")
    if filter_type == 'chua_kiem':
        baokiem_list = baokiem_list.filter(so_hop_cach__isnull=True) | baokiem_list.filter(so_hop_cach=0)
        logger.debug(f"After chua_kiem filter: {baokiem_list.count()}")
    elif filter_type == 'da_kiem':
        baokiem_list = baokiem_list.exclude(so_hop_cach__isnull=True).exclude(so_hop_cach=0)
        logger.debug(f"After da_kiem filter: {baokiem_list.count()}")

    if request.GET.get('clear_date'):
        from_date = None
        to_date = None

    paginator = Paginator(baokiem_list, 10)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    backup_dates = BaoKiemBackup.objects.values_list('backup_date', flat=True).distinct().order_by('-backup_date')

    if request.GET.get('export_excel'):
        output = io.BytesIO()
        df = pd.DataFrame(list(baokiem_list.values(
            'ngay', 'ma_vlspp', 'ma_kho', 'ten_quy_cach', 'dung_vao_viec', 'don_vi_tinh',
            'so_luong', 'nguoi_bao_kiem', 'noi_de_vat_tu', 'so_hop_cach', 'ket_qua_kiem_tra',
            'ngay_tra_ket_qua', 'phong_b12_ky', 'phong_b3_ky'
        )))
        df['ngay'] = df['ngay'].astype(str)
        df['ngay_tra_ket_qua'] = df['ngay_tra_ket_qua'].astype(str)
        df.to_excel(output, index=False)
        output.seek(0)
        response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="bao_kiem_vat_tu.xlsx"'
        return response

    return render(request, 'z115_app/bao_kiem_vat_tu.html', {
        'page_obj': page_obj,
        'from_date': from_date,
        'to_date': to_date,
        'search_name': search_name,
        'search_kho': search_kho,
        'filter_type': filter_type,
        'backup_dates': backup_dates if request.user.username == 'PHUONGB3' else None
    })