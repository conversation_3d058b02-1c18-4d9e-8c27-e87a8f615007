<!-- BẢN ĐÃ SỬA - danh_sach_cho_ch_duyet.html -->
{% extends 'z115_app/cap_vat_tu_khu_a.html' %}
{% load static %}

{% block sub_content %}
<div class="container mt-4">
<h2 class="text-center" style="font-weight: bold; color: #008000;">DANH SÁCH CHỜ CHỈ HUY DUYỆT</h2>
    <a href="{% url 'cap_vat_tu_khu_a' %}" class="btn btn-secondary mb-3" style="font-family: 'Times New Roman'; font-size: 18px;">Quay lại</a>
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Toast thông báo -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" style="font-weight: bold; color: #007bff;">
                BẠN ĐÃ DUYỆT PHIẾU THÀNH CÔNG!
            </div>
        </div>
        <div id="cancelToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-warning">
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" style="font-weight: bold; color: #dc3545;">
                BẠN ĐÃ HUỶ PHIẾU THÀNH CÔNG!
            </div>
        </div>
    </div>

    <div id="phieu-list" class="list-group scrollable-phieu-list">
        {% if phieus %}
            {% for phieu in phieus %}
                <a href="#" class="list-group-item list-group-item-action phieu-item flashing-background" data-phieu-id="{{ phieu.id }}" onclick="showPhieuDetails({{ phieu.id }})">
                    Phiếu số: {{ phieu.so_phieu }} | Đơn vị: {{ phieu.don_vi_nhan }} | Kho: {{ phieu.kho }} | Tài khoản tạo: {{ phieu.tai_khoan_tao }} | Ngày tạo: {{ phieu.ngay_tao|date:"d/m/Y" }} | Trạng thái: {{ phieu.get_trang_thai_display }}
                </a>
            {% endfor %}
        {% else %}
            <p class="text-center">Không có phiếu nào đang chờ duyệt.</p>
        {% endif %}
    </div>

    <!-- Modal hiển thị chi tiết và duyệt -->
    <div class="modal fade" id="phieuDetailsModal" tabindex="-1" aria-labelledby="phieuDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="phieuDetailsModalLabel">Chi tiết phiếu</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="duyet-form">{% csrf_token %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>STT</th>
                                        <th>Mục đích sử dụng</th>
                                        <th>Tên vật tư và quy cách</th>
                                        <th>ĐVT</th>
                                        <th>Số lượng yêu cầu</th>
                                        <th>Số lượng ĐMV duyệt</th>
                                    </tr>
                                </thead>
                                <tbody id="phieu-body"></tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-success" id="save-btn">Lưu và Duyệt Phiếu</button>
                    <button class="btn btn-danger" id="cancel-btn">Làm lại Phiếu</button>
                    <button type="button" class="btn btn-secondary" id="close-btn">Đóng</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let phieuId = null;

    function showPhieuDetails(id) {
        phieuId = id;
        fetch(`/get_phieu_details_ch/${id}/`)
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('phieu-body');
                tbody.innerHTML = '';
                data.vat_tus.forEach((vattu, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${vattu.muc_dich_su_dung}</td>
                        <td>${vattu.ten_vat_tu}</td>
                        <td>${vattu.don_vi_tinh}</td>
                        <td>${vattu.so_luong_yeu_cau}</td>
                        <td>${vattu.so_luong_duyet}</td>
                    `;
                    tbody.appendChild(row);
                });
                document.getElementById('duyet-form').classList.remove('submitted');
                new bootstrap.Modal(document.getElementById('phieuDetailsModal')).show();
            });
    }

    // ✅ Nút Lưu và Duyệt: gọi API save_duyet_ch_ajax/
    document.getElementById('save-btn').addEventListener('click', () => {
        const form = document.getElementById('duyet-form');
        const formData = new FormData(form);
        formData.append('phieu_id', phieuId);

        fetch(`/save_duyet_ch_ajax/`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                form.classList.add('submitted');
                const toast = new bootstrap.Toast(document.getElementById('successToast'));
                toast.show();
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('phieuDetailsModal'));
                    modal.hide();
                    const item = document.querySelector(`.phieu-item[data-phieu-id="${phieuId}"]`);
                    if (item) item.remove();
                }, 3000);
            } else {
                alert(data.error || "Có lỗi xảy ra khi duyệt phiếu!");
            }
        })
        .catch(error => {
            console.error("Lỗi khi gửi yêu cầu duyệt phiếu:", error);
            alert("Không thể kết nối đến máy chủ!");
        });
    });

    // Nút Huỷ phiếu
    document.getElementById('cancel-btn').addEventListener('click', () => {
        fetch(`/huy_phieu_ch/${phieuId}/`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const toast = new bootstrap.Toast(document.getElementById('cancelToast'));
                toast.show();
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('phieuDetailsModal'));
                    modal.hide();
                    const item = document.querySelector(`.phieu-item[data-phieu-id="${phieuId}"]`);
                    if (item) item.remove();
                }, 3000);
            }
        });
    });

    document.getElementById('close-btn').addEventListener('click', () => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('phieuDetailsModal'));
        modal.hide();
        const form = document.getElementById('duyet-form');
        if (form.classList.contains('submitted')) {
            const item = document.querySelector(`.phieu-item[data-phieu-id="${phieuId}"]`);
            if (item) item.remove();
        }
    });
</script>

<style>
    .number-input { width: 100px; }
    .toast-body { font-weight: bold; color: #007bff; }
</style>
<style>
    .scrollable-phieu-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ccc;
        padding-right: 5px;
    }

    .flashing-background {
        animation: flash-bg 3s ease-in-out infinite;
    }

    @keyframes flash-bg {
        0%   { background-color: #ffe5e5; }
        50%  { background-color: #ffb3b3; }
        100% { background-color: #ffe5e5; }
    }

    .phieu-item {
        font-family: 'Times New Roman', Times, serif;
        font-size: 16px;
        padding: 10px;
    }

    .phieu-item:hover {
        background-color: #f1f1f1;
    }
</style>

{% endblock %}
