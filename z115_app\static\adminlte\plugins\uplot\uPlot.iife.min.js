/*! https://github.com/leeoniya/uPlot (v1.6.18) */
var uPlot=function(){"use strict";function e(e,t,l,n){let i;l=l||0;let o=2147483647>=(n=n||t.length-1);for(;n-l>1;)i=o?l+n>>1:g((l+n)/2),e>t[i]?l=i:n=i;return e-t[l]>t[n]-e?n:l}function t(e,t,l,n){for(let i=1==n?t:l;i>=t&&l>=i;i+=n)if(null!=e[i])return i;return-1}const l=[0,0];function n(e,t,n,i){return l[0]=0>n?L(e,-n):e,l[1]=0>i?L(t,-i):t,l}function i(e,t,l,i){let o,s,r,u=v(e),a=10==l?y:M;return e==t&&(-1==u?(e*=l,t/=l):(e/=l,t*=l)),i?(o=g(a(e)),s=w(a(t)),r=n(k(l,o),k(l,s),o,s),e=r[0],t=r[1]):(o=g(a(m(e))),s=g(a(m(t))),r=n(k(l,o),k(l,s),o,s),e=R(e,r[0]),t=H(t,r[1])),[e,t]}function o(e,t,l,n){let o=i(e,t,l,n);return 0==e&&(o[0]=0),0==t&&(o[1]=0),o}const s={mode:3,pad:.1},r={pad:0,soft:null,mode:0},u={min:r,max:r};function a(e,t,l,n){return J(l)?f(e,t,l):(r.pad=l,r.soft=n?0:null,r.mode=n?3:0,f(e,t,u))}function c(e,t){return null==e?t:e}function f(e,t,l){let n=l.min,i=l.max,o=c(n.pad,0),s=c(i.pad,0),r=c(n.hard,-E),u=c(i.hard,E),a=c(n.soft,E),f=c(i.soft,-E),h=c(n.mode,0),d=c(i.mode,0),p=t-e;1e-9>p&&(p=0,0!=e&&0!=t||(p=1e-9,2==h&&a!=E&&(o=0),2==d&&f!=-E&&(s=0)));let x=p||m(t)||1e3,w=y(x),v=k(10,g(w)),M=L(R(e-x*(0==p?0==e?.1:1:o),v/10),9),S=a>e||1!=h&&(3!=h||M>a)&&(2!=h||a>M)?E:a,D=b(r,S>M&&e>=S?S:_(S,M)),T=L(H(t+x*(0==p?0==t?.1:1:s),v/10),9),z=t>f||1!=d&&(3!=d||f>T)&&(2!=d||T>f)?-E:f,P=_(u,T>z&&z>=t?z:b(z,T));return D==P&&0==D&&(P=100),[D,P]}const h=new Intl.NumberFormat(navigator.language).format,d=Math,p=d.PI,m=d.abs,g=d.floor,x=d.round,w=d.ceil,_=d.min,b=d.max,k=d.pow,v=d.sign,y=d.log10,M=d.log2,S=(e,t=1)=>d.asinh(e/t),E=1/0;function D(e){return 1+(0|y((e^e>>31)-(e>>31)))}function T(e,t){return x(e/t)*t}function z(e,t,l){return _(b(e,t),l)}function P(e){return"function"==typeof e?e:()=>e}const A=e=>e,W=(e,t)=>t,Y=()=>null,C=()=>!0,F=(e,t)=>e==t;function H(e,t){return w(e/t)*t}function R(e,t){return g(e/t)*t}function L(e,t){return x(e*(t=10**t))/t}const I=new Map;function G(e){return((""+e).split(".")[1]||"").length}function O(e,t,l,n){let i=[],o=n.map(G);for(let s=t;l>s;s++){let t=m(s),l=L(k(e,s),t);for(let e=0;n.length>e;e++){let r=n[e]*l,u=(0>r||0>s?t:0)+(o[e]>s?o[e]:0),a=L(r,u);i.push(a),I.set(a,u)}}return i}const N={},j=[],B=[null,null],V=Array.isArray;function U(e){return"string"==typeof e}function J(e){let t=!1;if(null!=e){let l=e.constructor;t=null==l||l==Object}return t}function q(e){return null!=e&&"object"==typeof e}function K(e,t=J){let l;if(V(e)){let n=e.find((e=>null!=e));if(V(n)||t(n)){l=Array(e.length);for(let n=0;e.length>n;n++)l[n]=K(e[n],t)}else l=e.slice()}else if(t(e)){l={};for(let n in e)l[n]=K(e[n],t)}else l=e;return l}function Z(e){let t=arguments;for(let l=1;t.length>l;l++){let n=t[l];for(let t in n)J(e[t])?Z(e[t],K(n[t])):e[t]=K(n[t])}return e}function $(e,t,l){for(let n,i=0,o=-1;t.length>i;i++){let s=t[i];if(s>o){for(n=s-1;n>=0&&null==e[n];)e[n--]=null;for(n=s+1;l>n&&null==e[n];)e[o=n++]=null}}}const X="undefined"==typeof queueMicrotask?e=>Promise.resolve().then(e):queueMicrotask,Q="width",ee="height",te="top",le="bottom",ne="left",ie="right",oe="#000",se="mousemove",re="mousedown",ue="mouseup",ae="mouseenter",ce="mouseleave",fe="dblclick",he="change",de="dppxchange",pe="u-off",me="u-label",ge=document,xe=window;let we,_e;function be(e,t){if(null!=t){let l=e.classList;!l.contains(t)&&l.add(t)}}function ke(e,t){let l=e.classList;l.contains(t)&&l.remove(t)}function ve(e,t,l){e.style[t]=l+"px"}function ye(e,t,l,n){let i=ge.createElement(e);return null!=t&&be(i,t),null!=l&&l.insertBefore(i,n),i}function Me(e,t){return ye("div",e,t)}const Se=new WeakMap;function Ee(e,t,l,n,i){let o="translate("+t+"px,"+l+"px)";o!=Se.get(e)&&(e.style.transform=o,Se.set(e,o),0>t||0>l||t>n||l>i?be(e,pe):ke(e,pe))}const De=new WeakMap;function Te(e,t,l){let n=t+l;n!=De.get(e)&&(De.set(e,n),e.style.background=t,e.style.borderColor=l)}const ze=new WeakMap;function Pe(e,t,l,n){let i=t+""+l;i!=ze.get(e)&&(ze.set(e,i),e.style.height=l+"px",e.style.width=t+"px",e.style.marginLeft=n?-t/2+"px":0,e.style.marginTop=n?-l/2+"px":0)}const Ae={passive:!0},We=Z({capture:!0},Ae);function Ye(e,t,l,n){t.addEventListener(e,l,n?We:Ae)}function Ce(e,t,l,n){t.removeEventListener(e,l,n?We:Ae)}!function e(){let t=devicePixelRatio;we!=t&&(we=t,_e&&Ce(he,_e,e),_e=matchMedia(`(min-resolution: ${we-.001}dppx) and (max-resolution: ${we+.001}dppx)`),Ye(he,_e,e),xe.dispatchEvent(new CustomEvent(de)))}();const Fe=["January","February","March","April","May","June","July","August","September","October","November","December"],He=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function Re(e){return e.slice(0,3)}const Le=He.map(Re),Ie=Fe.map(Re),Ge={MMMM:Fe,MMM:Ie,WWWW:He,WWW:Le};function Oe(e){return(10>e?"0":"")+e}const Ne={YYYY:e=>e.getFullYear(),YY:e=>(e.getFullYear()+"").slice(2),MMMM:(e,t)=>t.MMMM[e.getMonth()],MMM:(e,t)=>t.MMM[e.getMonth()],MM:e=>Oe(e.getMonth()+1),M:e=>e.getMonth()+1,DD:e=>Oe(e.getDate()),D:e=>e.getDate(),WWWW:(e,t)=>t.WWWW[e.getDay()],WWW:(e,t)=>t.WWW[e.getDay()],HH:e=>Oe(e.getHours()),H:e=>e.getHours(),h:e=>{let t=e.getHours();return 0==t?12:t>12?t-12:t},AA:e=>12>e.getHours()?"AM":"PM",aa:e=>12>e.getHours()?"am":"pm",a:e=>12>e.getHours()?"a":"p",mm:e=>Oe(e.getMinutes()),m:e=>e.getMinutes(),ss:e=>Oe(e.getSeconds()),s:e=>e.getSeconds(),fff:e=>function(e){return(10>e?"00":100>e?"0":"")+e}(e.getMilliseconds())};function je(e,t){t=t||Ge;let l,n=[],i=/\{([a-z]+)\}|[^{]+/gi;for(;l=i.exec(e);)n.push("{"==l[0][0]?Ne[l[1]]:l[0]);return e=>{let l="";for(let i=0;n.length>i;i++)l+="string"==typeof n[i]?n[i]:n[i](e,t);return l}}const Be=(new Intl.DateTimeFormat).resolvedOptions().timeZone,Ve=e=>e%1==0,Ue=[1,2,2.5,5],Je=O(10,-16,0,Ue),qe=O(10,0,16,Ue),Ke=qe.filter(Ve),Ze=Je.concat(qe),$e="{YYYY}",Xe="\n"+$e,Qe="{M}/{D}",et="\n"+Qe,tt=et+"/{YY}",lt="{aa}",nt="{h}:{mm}"+lt,it="\n"+nt,ot=":{ss}",st=null;function rt(e){let t=1e3*e,l=60*t,n=60*l,i=24*n,o=30*i,s=365*i;return[(1==e?O(10,0,3,Ue).filter(Ve):O(10,-3,0,Ue)).concat([t,5*t,10*t,15*t,30*t,l,5*l,10*l,15*l,30*l,n,2*n,3*n,4*n,6*n,8*n,12*n,i,2*i,3*i,4*i,5*i,6*i,7*i,8*i,9*i,10*i,15*i,o,2*o,3*o,4*o,6*o,s,2*s,5*s,10*s,25*s,50*s,100*s]),[[s,$e,st,st,st,st,st,st,1],[28*i,"{MMM}",Xe,st,st,st,st,st,1],[i,Qe,Xe,st,st,st,st,st,1],[n,"{h}"+lt,tt,st,et,st,st,st,1],[l,nt,tt,st,et,st,st,st,1],[t,ot,tt+" "+nt,st,et+" "+nt,st,it,st,1],[e,ot+".{fff}",tt+" "+nt,st,et+" "+nt,st,it,st,1]],function(t){return(r,u,a,c,f,h)=>{let d=[],p=f>=s,m=f>=o&&s>f,w=t(a),_=L(w*e,3),b=gt(w.getFullYear(),p?0:w.getMonth(),m||p?1:w.getDate()),k=L(b*e,3);if(m||p){let l=m?f/o:0,n=p?f/s:0,i=_==k?_:L(gt(b.getFullYear()+n,b.getMonth()+l,1)*e,3),r=new Date(x(i/e)),u=r.getFullYear(),a=r.getMonth();for(let o=0;c>=i;o++){let s=gt(u+n*o,a+l*o,1),r=s-t(L(s*e,3));i=L((+s+r)*e,3),i>c||d.push(i)}}else{let o=i>f?f:i,s=k+(g(a)-g(_))+H(_-k,o);d.push(s);let p=t(s),m=p.getHours()+p.getMinutes()/l+p.getSeconds()/n,x=f/n,w=h/r.axes[u]._space;for(;s=L(s+f,1==e?0:3),c>=s;)if(x>1){let e=g(L(m+x,6))%24,l=t(s).getHours()-e;l>1&&(l=-1),s-=l*n,m=(m+x)%24,.7>L((s-d[d.length-1])/f,3)*w||d.push(s)}else d.push(s)}return d}}]}const[ut,at,ct]=rt(1),[ft,ht,dt]=rt(.001);function pt(e,t){return e.map((e=>e.map(((l,n)=>0==n||8==n||null==l?l:t(1==n||0==e[8]?l:e[1]+l)))))}function mt(e,t){return(l,n,i,o,s)=>{let r,u,a,c,f,h,d=t.find((e=>s>=e[0]))||t[t.length-1];return n.map((t=>{let l=e(t),n=l.getFullYear(),i=l.getMonth(),o=l.getDate(),s=l.getHours(),p=l.getMinutes(),m=l.getSeconds(),g=n!=r&&d[2]||i!=u&&d[3]||o!=a&&d[4]||s!=c&&d[5]||p!=f&&d[6]||m!=h&&d[7]||d[1];return r=n,u=i,a=o,c=s,f=p,h=m,g(l)}))}}function gt(e,t,l){return new Date(e,t,l)}function xt(e,t){return t(e)}function wt(e,t){return(l,n)=>t(e(n))}O(2,-53,53,[1]);const _t={show:!0,live:!0,isolate:!1,markers:{show:!0,width:2,stroke:function(e,t){let l=e.series[t];return l.width?l.stroke(e,t):l.points.width?l.points.stroke(e,t):null},fill:function(e,t){return e.series[t].fill(e,t)},dash:"solid"},idx:null,idxs:null,values:[]},bt=[0,0];function kt(e,t,l){return e=>{0==e.button&&l(e)}}function vt(e,t,l){return l}const yt={show:!0,x:!0,y:!0,lock:!1,move:function(e,t,l){return bt[0]=t,bt[1]=l,bt},points:{show:function(e,t){let l=e.cursor.points,n=Me(),i=l.size(e,t);ve(n,Q,i),ve(n,ee,i);let o=i/-2;ve(n,"marginLeft",o),ve(n,"marginTop",o);let s=l.width(e,t,i);return s&&ve(n,"borderWidth",s),n},size:function(e,t){return Ot(e.series[t].points.width,1)},width:0,stroke:function(e,t){let l=e.series[t].points;return l._stroke||l._fill},fill:function(e,t){let l=e.series[t].points;return l._fill||l._stroke}},bind:{mousedown:kt,mouseup:kt,click:kt,dblclick:kt,mousemove:vt,mouseleave:vt,mouseenter:vt},drag:{setScale:!0,x:!0,y:!1,dist:0,uni:null,_x:!1,_y:!1},focus:{prox:-1},left:-10,top:-10,idx:null,dataIdx:function(e,t,l){return l},idxs:null},Mt={show:!0,stroke:"rgba(0,0,0,0.07)",width:2,filter:W},St=Z({},Mt,{size:10}),Et='12px system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',Dt="bold "+Et,Tt={show:!0,scale:"x",stroke:oe,space:50,gap:5,size:50,labelGap:0,labelSize:30,labelFont:Dt,side:2,grid:Mt,ticks:St,font:Et,rotate:0},zt={show:!0,scale:"x",auto:!1,sorted:1,min:E,max:-E,idxs:[]};function Pt(e,t){return t.map((e=>null==e?"":h(e)))}function At(e,t,l,n,i,o,s){let r=[],u=I.get(i)||0;for(let e=l=s?l:L(H(l,i),u);n>=e;e=L(e+i,u))r.push(Object.is(e,-0)?0:e);return r}function Wt(e,t,l,n,i){const o=[],s=e.scales[e.axes[t].scale].log,r=g((10==s?y:M)(l));i=k(s,r),0>r&&(i=L(i,-r));let u=l;do{o.push(u),u=L(u+i,I.get(i)),i*s>u||(i=u)}while(n>=u);return o}function Yt(e,t,l,n,i){let o=e.scales[e.axes[t].scale].asinh,s=n>o?Wt(e,t,b(o,l),n,i):[o],r=0>n||l>0?[]:[0];return(-o>l?Wt(e,t,b(o,-n),-l,i):[o]).reverse().map((e=>-e)).concat(r,s)}const Ct=/./,Ft=/[12357]/,Ht=/[125]/,Rt=/1/;function Lt(e,t,l){let n=e.axes[l],i=n.scale,o=e.scales[i];if(3==o.distr&&2==o.log)return t;let s=e.valToPos,r=n._space,u=s(10,i),a=s(9,i)-u<r?s(7,i)-u<r?s(5,i)-u<r?Rt:Ht:Ft:Ct;return t.map((e=>4==o.distr&&0==e||a.test(e)?e:null))}function It(e,t){return null==t?"":h(t)}const Gt={show:!0,scale:"y",stroke:oe,space:30,gap:5,size:50,labelGap:0,labelSize:30,labelFont:Dt,side:3,grid:Mt,ticks:St,font:Et,rotate:0};function Ot(e,t){return L((3+2*(e||1))*t,3)}function Nt(e,t){let l=e.scales[e.series[t].scale],n=e.bands&&e.bands.some((e=>e.series[0]==t));return 3==l.distr||n?l.min:0}const jt={scale:null,auto:!0,min:E,max:-E},Bt={show:!0,auto:!0,sorted:0,alpha:1,facets:[Z({},jt,{scale:"x"}),Z({},jt,{scale:"y"})]},Vt={scale:"y",auto:!0,sorted:0,show:!0,spanGaps:!1,gaps:(e,t,l,n,i)=>i,alpha:1,points:{show:function(e,t){let{scale:l,idxs:n}=e.series[0],i=e._data[0],o=e.valToPos(i[n[0]],l,!0),s=e.valToPos(i[n[1]],l,!0);return m(s-o)/(e.series[t].points.space*we)>=n[1]-n[0]},filter:null},values:null,min:E,max:-E,idxs:[],path:null,clip:null};function Ut(e,t,l){return l/10}const Jt={time:!0,auto:!0,distr:1,log:10,asinh:1,min:null,max:null,dir:1,ori:0},qt=Z({},Jt,{time:!1,ori:1}),Kt={};function Zt(e){let t=Kt[e];return t||(t={key:e,plots:[],sub(e){t.plots.push(e)},unsub(e){t.plots=t.plots.filter((t=>t!=e))},pub(e,l,n,i,o,s,r){for(let u=0;t.plots.length>u;u++)t.plots[u]!=l&&t.plots[u].pub(e,l,n,i,o,s,r)}},null!=e&&(Kt[e]=t)),t}function $t(e,t,l){const n=e.series[t],i=e.scales,o=e.bbox;let s=e._data[0],r=e._data[t],u=2==e.mode?i[n.facets[0].scale]:i[e.series[0].scale],a=2==e.mode?i[n.facets[1].scale]:i[n.scale],c=o.left,f=o.top,h=o.width,d=o.height,p=e.valToPosH,m=e.valToPosV;return 0==u.ori?l(n,s,r,u,a,p,m,c,f,h,d,nl,ol,rl,al,fl):l(n,s,r,u,a,m,p,f,c,d,h,il,sl,ul,cl,hl)}function Xt(e,t,l,n,i){return $t(e,t,((e,t,o,s,r,u,a,c,f,h,d)=>{let p=e.pxRound;const m=0==s.ori?ol:sl;let g,x;1==s.dir*(0==s.ori?1:-1)?(g=l,x=n):(g=n,x=l);let w=p(u(t[g],s,h,c)),_=p(a(o[g],r,d,f)),b=p(u(t[x],s,h,c)),k=p(a(r.max,r,d,f)),v=new Path2D(i);return m(v,b,k),m(v,w,k),m(v,w,_),v}))}function Qt(e,t,l,n,i,o){let s=null;if(e.length>0){s=new Path2D;const r=0==t?rl:ul;let u=l;for(let t=0;e.length>t;t++){let l=e[t];if(l[1]>l[0]){let e=l[0]-u;e>0&&r(s,u,n,e,n+o),u=l[1]}}let a=l+i-u;a>0&&r(s,u,n,a,n+o)}return s}function el(e,t,l){let n=e[e.length-1];n&&n[0]==t?n[1]=l:e.push([t,l])}function tl(e){return 0==e?A:1==e?x:t=>T(t,e)}function ll(e){let t=0==e?nl:il,l=0==e?(e,t,l,n,i,o)=>{e.arcTo(t,l,n,i,o)}:(e,t,l,n,i,o)=>{e.arcTo(l,t,i,n,o)},n=0==e?(e,t,l,n,i)=>{e.rect(t,l,n,i)}:(e,t,l,n,i)=>{e.rect(l,t,i,n)};return(e,i,o,s,r,u=0)=>{0==u?n(e,i,o,s,r):(u=_(u,s/2,r/2),t(e,i+u,o),l(e,i+s,o,i+s,o+r,u),l(e,i+s,o+r,i,o+r,u),l(e,i,o+r,i,o,u),l(e,i,o,i+s,o,u),e.closePath())}}const nl=(e,t,l)=>{e.moveTo(t,l)},il=(e,t,l)=>{e.moveTo(l,t)},ol=(e,t,l)=>{e.lineTo(t,l)},sl=(e,t,l)=>{e.lineTo(l,t)},rl=ll(0),ul=ll(1),al=(e,t,l,n,i,o)=>{e.arc(t,l,n,i,o)},cl=(e,t,l,n,i,o)=>{e.arc(l,t,n,i,o)},fl=(e,t,l,n,i,o,s)=>{e.bezierCurveTo(t,l,n,i,o,s)},hl=(e,t,l,n,i,o,s)=>{e.bezierCurveTo(l,t,i,n,s,o)};function dl(){return(e,t,l,n,i)=>$t(e,t,((t,o,s,r,u,a,c,f,h,d,m)=>{let g,x,{pxRound:w,points:_}=t;0==r.ori?(g=nl,x=al):(g=il,x=cl);const b=L(_.width*we,3);let k=(_.size-_.width)/2*we,v=L(2*k,3),y=new Path2D,M=new Path2D,{left:S,top:E,width:D,height:T}=e.bbox;rl(M,S-v,E-v,D+2*v,T+2*v);const z=e=>{if(null!=s[e]){let t=w(a(o[e],r,d,f)),l=w(c(s[e],u,m,h));g(y,t+k,l),x(y,t,l,k,0,2*p)}};if(i)i.forEach(z);else for(let e=l;n>=e;e++)z(e);return{stroke:b>0?y:null,fill:y,clip:M,flags:3}}))}function pl(e){return(t,l,n,i,o,s)=>{n!=i&&(o!=n&&s!=n&&e(t,l,n),o!=i&&s!=i&&e(t,l,i),e(t,l,s))}}const ml=pl(ol),gl=pl(sl);function xl(){return(e,l,n,i)=>$t(e,l,((o,s,r,u,a,c,f,h,d,p,m)=>{let g,x,w=o.pxRound;0==u.ori?(g=ol,x=ml):(g=sl,x=gl);const k=u.dir*(0==u.ori?1:-1),v={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},y=v.stroke;let M,S,D,T,z=E,P=-E,A=[],W=w(c(s[1==k?n:i],u,p,h)),Y=!1,C=!1,F=t(r,n,i,1*k),H=t(r,n,i,-1*k),R=w(c(s[F],u,p,h)),L=w(c(s[H],u,p,h));R>h&&el(A,h,R);for(let e=1==k?n:i;e>=n&&i>=e;e+=k){let t=w(c(s[e],u,p,h));if(t==W)null!=r[e]?(S=w(f(r[e],a,m,d)),z==E&&(g(y,t,S),M=S),z=_(S,z),P=b(S,P)):null===r[e]&&(Y=C=!0);else{let l=!1;z!=E?(x(y,W,z,P,M,S),D=T=W):Y&&(l=!0,Y=!1),null!=r[e]?(S=w(f(r[e],a,m,d)),g(y,t,S),z=P=M=S,C&&t-W>1&&(l=!0),C=!1):(z=E,P=-E,null===r[e]&&(Y=!0,t-W>1&&(l=!0))),l&&el(A,D,t),W=t}}if(z!=E&&z!=P&&T!=W&&x(y,W,z,P,M,S),h+p>L&&el(A,L,h+p),null!=o.fill){let t=v.fill=new Path2D(y),n=w(f(o.fillTo(e,l,o.min,o.max),a,m,d));g(t,L,n),g(t,R,n)}return v.gaps=A=o.gaps(e,l,n,i,A),o.spanGaps||(v.clip=Qt(A,u.ori,h,d,p,m)),e.bands.length>0&&(v.band=Xt(e,l,n,i,y)),v}))}function wl(e,t,l,n,i){const o=e.length;if(2>o)return null;const s=new Path2D;if(l(s,e[0],t[0]),2==o)n(s,e[1],t[1]);else{let l=Array(o),n=Array(o-1),r=Array(o-1),u=Array(o-1);for(let l=0;o-1>l;l++)r[l]=t[l+1]-t[l],u[l]=e[l+1]-e[l],n[l]=r[l]/u[l];l[0]=n[0];for(let e=1;o-1>e;e++)0===n[e]||0===n[e-1]||n[e-1]>0!=n[e]>0?l[e]=0:(l[e]=3*(u[e-1]+u[e])/((2*u[e]+u[e-1])/n[e-1]+(u[e]+2*u[e-1])/n[e]),isFinite(l[e])||(l[e]=0));l[o-1]=n[o-2];for(let n=0;o-1>n;n++)i(s,e[n]+u[n]/3,t[n]+l[n]*u[n]/3,e[n+1]-u[n]/3,t[n+1]-l[n+1]*u[n]/3,e[n+1],t[n+1])}return s}const _l=new Set;function bl(){_l.forEach((e=>{e.syncRect(!0)}))}Ye("resize",xe,bl),Ye("scroll",xe,bl,!0);const kl=xl(),vl=dl();function yl(e,t,l,n){return(n?[e[0],e[1]].concat(e.slice(2)):[e[0]].concat(e.slice(1))).map(((e,n)=>Ml(e,n,t,l)))}function Ml(e,t,l,n){return Z({},0==t?l:n,e)}function Sl(e,t,l){return null==t?B:[t,l]}const El=Sl;function Dl(e,t,l){return null==t?B:a(t,l,.1,!0)}function Tl(e,t,l,n){return null==t?B:i(t,l,e.scales[n].log,!1)}const zl=Tl;function Pl(e,t,l,n){return null==t?B:o(t,l,e.scales[n].log,!1)}const Al=Pl;function Wl(t,l,n,i,o){let s=b(D(t),D(l)),r=l-t,u=e(o/i*r,n);do{let e=n[u],t=i*e/r;if(t>=o&&17>=s+(5>e?I.get(e):0))return[e,t]}while(++u<n.length);return[0,0]}function Yl(e){let t,l;return[e=e.replace(/(\d+)px/,((e,n)=>(t=x((l=+n)*we))+"px")),t,l]}function Cl(e){e.show&&[e.font,e.labelFont].forEach((e=>{let t=L(e[2]*we,1);e[0]=e[0].replace(/[0-9.]+px/,t+"px"),e[1]=t}))}function Fl(t,l,n){const r={mode:c(t.mode,1)},u=r.mode;function f(e,t){return((3==t.distr?y(e>0?e:t.clamp(r,e,t.min,t.max,t.key)):4==t.distr?S(e,t.asinh):e)-t._min)/(t._max-t._min)}function h(e,t,l,n){let i=f(e,t);return n+l*(-1==t.dir?1-i:i)}function g(e,t,l,n){let i=f(e,t);return n+l*(-1==t.dir?i:1-i)}function v(e,t,l,n){return 0==t.ori?h(e,t,l,n):g(e,t,l,n)}r.valToPosH=h,r.valToPosV=g;let M=!1;r.status=0;const D=r.root=Me("uplot");null!=t.id&&(D.id=t.id),be(D,t.class),t.title&&(Me("u-title",D).textContent=t.title);const A=ye("canvas"),R=r.ctx=A.getContext("2d"),I=Me("u-wrap",D),G=r.under=Me("u-under",I);I.appendChild(A);const O=r.over=Me("u-over",I),$=+c((t=K(t)).pxAlign,1),oe=tl($);(t.plugins||[]).forEach((e=>{e.opts&&(t=e.opts(r,t)||t)}));const he=t.ms||.001,_e=r.series=1==u?yl(t.series||[],zt,Vt,!1):function(e,t){return e.map(((e,l)=>0==l?null:Z({},t,e)))}(t.series||[null],Bt),Se=r.axes=yl(t.axes||[],Tt,Gt,!0),De=r.scales={},ze=r.bands=t.bands||[];ze.forEach((e=>{e.fill=P(e.fill||null)}));const Ae=2==u?_e[1].facets[0].scale:_e[0].scale,We={axes:function(){for(let e=0;Se.length>e;e++){let t=Se[e];if(!t.show||!t._show)continue;let l,n,i=t.side,o=i%2,s=t.stroke(r,e),u=0==i||3==i?-1:1;if(t.label){let e=x((t._lpos+t.labelGap*u)*we);ql(t.labelFont[0],s,"center",2==i?te:le),R.save(),1==o?(l=n=0,R.translate(e,x(Rt+Kt/2)),R.rotate((3==i?-p:p)/2)):(l=x(Ht+jt/2),n=e),R.fillText(t.label,l,n),R.restore()}let[a,c]=t._found;if(0==c)continue;let f=De[t.scale],h=0==o?jt:Kt,d=0==o?Ht:Rt,m=x(t.gap*we),g=t._splits,w=2==f.distr?g.map((e=>jl[e])):g,_=2==f.distr?jl[g[1]]-jl[g[0]]:a,b=t.ticks,k=b.show?x(b.size*we):0,y=t._rotate*-p/180,M=oe(t._pos*we),S=M+(k+m)*u;n=0==o?S:0,l=1==o?S:0,ql(t.font[0],s,1==t.align?ne:2==t.align?ie:y>0?ne:0>y?ie:0==o?"center":3==i?ie:ne,y||1==o?"middle":2==i?te:le);let E=1.5*t.font[1],D=g.map((e=>oe(v(e,f,h,d)))),T=t._values;for(let e=0;T.length>e;e++){let t=T[e];if(null!=t){0==o?l=D[e]:n=D[e],t=""+t;let i=-1==t.indexOf("\n")?[t]:t.split(/\n/gm);for(let e=0;i.length>e;e++){let t=i[e];y?(R.save(),R.translate(l,n+e*E),R.rotate(y),R.fillText(t,0,0),R.restore()):R.fillText(t,l,n+e*E)}}}b.show&&tn(D,b.filter(r,w,e,c,_),o,i,M,k,L(b.width*we,3),b.stroke(r,e),b.dash,b.cap);let z=t.grid;z.show&&tn(D,z.filter(r,w,e,c,_),o,0==o?2:1,0==o?Rt:Ht,0==o?Kt:jt,L(z.width*we,3),z.stroke(r,e),z.dash,z.cap)}ti("drawAxes")},series:function(){pl>0&&(_e.forEach(((e,t)=>{if(t>0&&e.show&&null==e._paths){let n=function(e){let t=z(ml-1,0,pl-1),l=z(gl+1,0,pl-1);for(;null==e[t]&&t>0;)t--;for(;null==e[l]&&pl-1>l;)l++;return[t,l]}(l[t]);e._paths=e.paths(r,t,n[0],n[1])}})),_e.forEach(((e,t)=>{if(t>0&&e.show){Nl!=e.alpha&&(R.globalAlpha=Nl=e.alpha),Zl(t,!1),e._paths&&$l(t,!1);{Zl(t,!0);let l=e.points.show(r,t,ml,gl),n=e.points.filter(r,t,l,e._paths?e._paths.gaps:null);(l||n)&&(e.points._paths=e.points.paths(r,t,ml,gl,n),$l(t,!0))}1!=Nl&&(R.globalAlpha=Nl=1),ti("drawSeries",t)}})))}},Fe=(t.drawOrder||["axes","series"]).map((e=>We[e]));function He(e){let l=De[e];if(null==l){let n=(t.scales||N)[e]||N;if(null!=n.from)He(n.from),De[e]=Z({},De[n.from],n,{key:e});else{l=De[e]=Z({},e==Ae?Jt:qt,n),2==u&&(l.time=!1),l.key=e;let t=l.time,i=l.range,o=V(i);if((e!=Ae||2==u)&&(!o||null!=i[0]&&null!=i[1]||(i={min:null==i[0]?s:{mode:1,hard:i[0],soft:i[0]},max:null==i[1]?s:{mode:1,hard:i[1],soft:i[1]}},o=!1),!o&&J(i))){let e=i;i=(t,l,n)=>null==l?B:a(l,n,e)}l.range=P(i||(t?El:e==Ae?3==l.distr?zl:4==l.distr?Al:Sl:3==l.distr?Tl:4==l.distr?Pl:Dl)),l.auto=P(!o&&l.auto),l.clamp=P(l.clamp||Ut),l._min=l._max=null}}}He("x"),He("y"),1==u&&_e.forEach((e=>{He(e.scale)})),Se.forEach((e=>{He(e.scale)}));for(let e in t.scales)He(e);const Re=De[Ae],Le=Re.distr;let Ie,Ge;0==Re.ori?(be(D,"u-hz"),Ie=h,Ge=g):(be(D,"u-vt"),Ie=g,Ge=h);const Oe={};for(let e in De){let t=De[e];null==t.min&&null==t.max||(Oe[e]={min:t.min,max:t.max},t.min=t.max=null)}const Ne=t.tzDate||(e=>new Date(x(e/he))),Be=t.fmtDate||je,Ve=1==he?ct(Ne):dt(Ne),Ue=mt(Ne,pt(1==he?at:ht,Be)),Je=wt(Ne,xt("{YYYY}-{MM}-{DD} {h}:{mm}{aa}",Be)),qe=[],$e=r.legend=Z({},_t,t.legend),Xe=$e.show,Qe=$e.markers;let et;$e.idxs=qe,Qe.width=P(Qe.width),Qe.dash=P(Qe.dash),Qe.stroke=P(Qe.stroke),Qe.fill=P(Qe.fill);let tt,lt=[],nt=[],it=!1,ot={};if($e.live){const e=_e[1]?_e[1].values:null;it=null!=e,tt=it?e(r,1,0):{_:0};for(let e in tt)ot[e]="--"}if(Xe)if(et=ye("table","u-legend",D),it){let e=ye("tr","u-thead",et);for(var st in ye("th",null,e),tt)ye("th",me,e).textContent=st}else be(et,"u-inline"),$e.live&&be(et,"u-live");const rt={show:!0},gt={show:!1},bt=new Map;function kt(e,t,l){const n=bt.get(t)||{},i=ol.bind[e](r,t,l);i&&(Ye(e,t,n[e]=i),bt.set(t,n))}function vt(e,t){const l=bt.get(t)||{};for(let n in l)null!=e&&n!=e||(Ce(n,t,l[n]),delete l[n]);null==e&&bt.delete(t)}let Mt=0,St=0,Et=0,Dt=0,Ct=0,Ft=0,Ht=0,Rt=0,jt=0,Kt=0;r.bbox={};let $t=!1,Xt=!1,Qt=!1,el=!1,ll=!1;function nl(e,t,l){(l||e!=r.width||t!=r.height)&&il(e,t),on(!1),Qt=!0,Xt=!0,el=ll=ol.left>=0,_n()}function il(e,t){r.width=Mt=Et=e,r.height=St=Dt=t,Ct=Ft=0,function(){let e=!1,t=!1,l=!1,n=!1;Se.forEach((i=>{if(i.show&&i._show){let{side:o,_size:s}=i,r=o%2,u=s+(null!=i.label?i.labelSize:0);u>0&&(r?(Et-=u,3==o?(Ct+=u,n=!0):l=!0):(Dt-=u,0==o?(Ft+=u,e=!0):t=!0))}})),cl[0]=e,cl[1]=l,cl[2]=t,cl[3]=n,Et-=dl[1]+dl[3],Ct+=dl[3],Dt-=dl[2]+dl[0],Ft+=dl[0]}(),function(){let e=Ct+Et,t=Ft+Dt,l=Ct,n=Ft;function i(i,o){switch(i){case 1:return e+=o,e-o;case 2:return t+=o,t-o;case 3:return l-=o,l+o;case 0:return n-=o,n+o}}Se.forEach((e=>{if(e.show&&e._show){let t=e.side;e._pos=i(t,e._size),null!=e.label&&(e._lpos=i(t,e.labelSize))}}))}();let l=r.bbox;Ht=l.left=T(Ct*we,.5),Rt=l.top=T(Ft*we,.5),jt=l.width=T(Et*we,.5),Kt=l.height=T(Dt*we,.5)}r.setSize=function({width:e,height:t}){nl(e,t)};const ol=r.cursor=Z({},yt,{drag:{y:2==u}},t.cursor);{ol.idxs=qe,ol._lock=!1;let e=ol.points;e.show=P(e.show),e.size=P(e.size),e.stroke=P(e.stroke),e.width=P(e.width),e.fill=P(e.fill)}const sl=r.focus=Z({},t.focus||{alpha:.3},ol.focus),rl=sl.prox>=0;let ul=[null];function al(e,t){if(1==u||t>0){let t=1==u&&De[e.scale].time,l=e.value;e.value=t?U(l)?wt(Ne,xt(l,Be)):l||Je:l||It,e.label=e.label||(t?"Time":"Value")}if(t>0){e.width=null==e.width?1:e.width,e.paths=e.paths||kl||Y,e.fillTo=P(e.fillTo||Nt),e.pxAlign=+c(e.pxAlign,$),e.pxRound=tl(e.pxAlign),e.stroke=P(e.stroke||null),e.fill=P(e.fill||null),e._stroke=e._fill=e._paths=e._focus=null;let t=Ot(e.width,1),l=e.points=Z({},{size:t,width:b(1,.2*t),stroke:e.stroke,space:2*t,paths:vl,_stroke:null,_fill:null},e.points);l.show=P(l.show),l.filter=P(l.filter),l.fill=P(l.fill),l.stroke=P(l.stroke),l.paths=P(l.paths),l.pxAlign=e.pxAlign}if(Xe){let l=function(e,t){if(0==t&&(it||!$e.live||2==u))return B;let l=[],n=ye("tr","u-series",et,et.childNodes[t]);be(n,e.class),e.show||be(n,pe);let i=ye("th",null,n);if(Qe.show){let e=Me("u-marker",i);if(t>0){let l=Qe.width(r,t);l&&(e.style.border=l+"px "+Qe.dash(r,t)+" "+Qe.stroke(r,t)),e.style.background=Qe.fill(r,t)}}let o=Me(me,i);for(var s in o.textContent=e.label,t>0&&(Qe.show||(o.style.color=e.width>0?Qe.stroke(r,t):Qe.fill(r,t)),kt("click",i,(t=>{if(ol._lock)return;let l=_e.indexOf(e);if((t.ctrlKey||t.metaKey)!=$e.isolate){let e=_e.some(((e,t)=>t>0&&t!=l&&e.show));_e.forEach(((t,n)=>{n>0&&Pn(n,e?n==l?rt:gt:rt,!0,li.setSeries)}))}else Pn(l,{show:!e.show},!0,li.setSeries)})),rl&&kt(ae,i,(()=>{ol._lock||Pn(_e.indexOf(e),Cn,!0,li.setSeries)}))),tt){let e=ye("td","u-value",n);e.textContent="--",l.push(e)}return[n,l]}(e,t);lt.splice(t,0,l[0]),nt.splice(t,0,l[1]),$e.values.push(null)}if(ol.show){qe.splice(t,0,null);let l=function(e,t){if(t>0){let l=ol.points.show(r,t);if(l)return be(l,"u-cursor-pt"),be(l,e.class),Ee(l,-10,-10,Et,Dt),O.insertBefore(l,ul[t]),l}}(e,t);l&&ul.splice(t,0,l)}}r.addSeries=function(e,t){e=Ml(e,t=null==t?_e.length:t,zt,Vt),_e.splice(t,0,e),al(_e[t],t)},r.delSeries=function(e){if(_e.splice(e,1),Xe){$e.values.splice(e,1),nt.splice(e,1);let t=lt.splice(e,1)[0];vt(null,t.firstChild),t.remove()}ol.show&&(qe.splice(e,1),ul.length>1&&ul.splice(e,1)[0].remove())};const cl=[!1,!1,!1,!1];function fl(e,t,l){let[n,i,o,s]=l,r=t%2,u=0;return 0==r&&(s||i)&&(u=0==t&&!n||2==t&&!o?x(Tt.size/3):0),1==r&&(n||o)&&(u=1==t&&!i||3==t&&!s?x(Gt.size/2):0),u}const hl=r.padding=(t.padding||[fl,fl,fl,fl]).map((e=>P(c(e,fl)))),dl=r._padding=hl.map(((e,t)=>e(r,t,cl,0)));let pl,ml=null,gl=null;const xl=1==u?_e[0].idxs:null;let wl,bl,Fl,Hl,Rl,Ll,Il,Gl,Ol,Nl,jl=null,Bl=!1;function Vl(e,t){if(2==u){pl=0;for(let e=1;_e.length>e;e++)pl+=l[e][0].length;r.data=l=e}else(l=(e||[]).slice())[0]=l[0]||[],r.data=l.slice(),jl=l[0],pl=jl.length,2==Le&&(l[0]=jl.map(((e,t)=>t)));if(r._data=l,on(!0),ti("setData"),!1!==t){let e=Re;e.auto(r,Bl)?Ul():zn(Ae,e.min,e.max),el=ol.left>=0,ll=!0,_n()}}function Ul(){let e,t;Bl=!0,1==u&&(pl>0?(ml=xl[0]=0,gl=xl[1]=pl-1,e=l[0][ml],t=l[0][gl],2==Le?(e=ml,t=gl):1==pl&&(3==Le?[e,t]=i(e,e,Re.log,!1):4==Le?[e,t]=o(e,e,Re.log,!1):Re.time?t=e+x(86400/he):[e,t]=a(e,t,.1,!0))):(ml=xl[0]=e=null,gl=xl[1]=t=null)),zn(Ae,e,t)}function Jl(e="#0000",t,l=j,n="butt",i="#0000",o="round"){e!=wl&&(R.strokeStyle=wl=e),i!=bl&&(R.fillStyle=bl=i),t!=Fl&&(R.lineWidth=Fl=t),o!=Rl&&(R.lineJoin=Rl=o),n!=Ll&&(R.lineCap=Ll=n),l!=Hl&&R.setLineDash(Hl=l)}function ql(e,t,l,n){t!=bl&&(R.fillStyle=bl=t),e!=Il&&(R.font=Il=e),l!=Gl&&(R.textAlign=Gl=l),n!=Ol&&(R.textBaseline=Ol=n)}function Kl(e,t,l,n){if(e.auto(r,Bl)&&(null==t||null==t.min)){let t=c(ml,0),i=c(gl,n.length-1),o=null==l.min?3==e.distr?function(e,t,l){let n=E,i=-E;for(let o=t;l>=o;o++)e[o]>0&&(n=_(n,e[o]),i=b(i,e[o]));return[n==E?1:n,i==-E?10:i]}(n,t,i):function(e,t,l){let n=E,i=-E;for(let o=t;l>=o;o++)null!=e[o]&&(n=_(n,e[o]),i=b(i,e[o]));return[n,i]}(n,t,i):[l.min,l.max];e.min=_(e.min,l.min=o[0]),e.max=b(e.max,l.max=o[1])}}function Zl(e,t){let l=t?_e[e].points:_e[e];l._stroke=l.stroke(r,e),l._fill=l.fill(r,e)}function $l(e,t){let n=t?_e[e].points:_e[e],i=n._stroke,o=n._fill,{stroke:s,fill:u,clip:a,flags:f}=n._paths,h=null,d=L(n.width*we,3),p=d%2/2;t&&null==o&&(o=d>0?"#fff":i);let m=1==n.pxAlign;if(m&&R.translate(p,p),!t){let e=Ht,t=Rt,l=jt,i=Kt,o=d*we/2;0==n.min&&(i+=o),0==n.max&&(t-=o,i+=o),h=new Path2D,h.rect(e,t,l,i)}t?Xl(i,d,n.dash,n.cap,o,s,u,f,a):function(e,t,n,i,o,s,u,a,f,h,d){let p=!1;ze.forEach(((m,g)=>{if(m.series[0]==e){let e,x=_e[m.series[1]],w=l[m.series[1]],_=(x._paths||N).band,b=null;x.show&&_&&function(e,t,l){for(t=c(t,0),l=c(l,e.length-1);l>=t;){if(null!=e[t])return!0;t++}return!1}(w,ml,gl)?(b=m.fill(r,g)||s,e=x._paths.clip):_=null,Xl(t,n,i,o,b,u,a,f,h,d,e,_),p=!0}})),p||Xl(t,n,i,o,s,u,a,f,h,d)}(e,i,d,n.dash,n.cap,o,s,u,f,h,a),m&&R.translate(-p,-p)}function Xl(e,t,l,n,i,o,s,r,u,a,c,f){Jl(e,t,l,n,i),(u||a||f)&&(R.save(),u&&R.clip(u),a&&R.clip(a)),f?3==(3&r)?(R.clip(f),c&&R.clip(c),en(i,s),Ql(e,o,t)):2&r?(en(i,s),R.clip(f),Ql(e,o,t)):1&r&&(R.save(),R.clip(f),c&&R.clip(c),en(i,s),R.restore(),Ql(e,o,t)):(en(i,s),Ql(e,o,t)),(u||a||f)&&R.restore()}function Ql(e,t,l){l>0&&(t instanceof Map?t.forEach(((e,t)=>{R.strokeStyle=wl=t,R.stroke(e)})):null!=t&&e&&R.stroke(t))}function en(e,t){t instanceof Map?t.forEach(((e,t)=>{R.fillStyle=bl=t,R.fill(e)})):null!=t&&e&&R.fill(t)}function tn(e,t,l,n,i,o,s,r,u,a){let c=s%2/2;1==$&&R.translate(c,c),Jl(r,s,u,a,r),R.beginPath();let f,h,d,p,m=i+(0==n||3==n?-o:o);0==l?(h=i,p=m):(f=i,d=m);for(let n=0;e.length>n;n++)null!=t[n]&&(0==l?f=d=e[n]:h=p=e[n],R.moveTo(f,h),R.lineTo(d,p));R.stroke(),1==$&&R.translate(-c,-c)}function ln(e){let t=!0;return Se.forEach(((l,n)=>{if(!l.show)return;let i=De[l.scale];if(null==i.min)return void(l._show&&(t=!1,l._show=!1,on(!1)));l._show||(t=!1,l._show=!0,on(!1));let o=l.side,s=o%2,{min:u,max:a}=i,[c,f]=function(e,t,l,n){let i,o=Se[e];if(n>0){let s=o._space=o.space(r,e,t,l,n);i=Wl(t,l,o._incrs=o.incrs(r,e,t,l,n,s),n,s)}else i=[0,0];return o._found=i}(n,u,a,0==s?Et:Dt);if(0==f)return;let h=l._splits=l.splits(r,n,u,a,c,f,2==i.distr),d=2==i.distr?h.map((e=>jl[e])):h,p=2==i.distr?jl[h[1]]-jl[h[0]]:c,m=l._values=l.values(r,l.filter(r,d,n,f,p),n,f,p);l._rotate=2==o?l.rotate(r,m,n,f):0;let g=l._size;l._size=w(l.size(r,m,n,e)),null!=g&&l._size!=g&&(t=!1)})),t}function nn(e){let t=!0;return hl.forEach(((l,n)=>{let i=l(r,n,cl,e);i!=dl[n]&&(t=!1),dl[n]=i})),t}function on(e){_e.forEach(((t,l)=>{l>0&&(t._paths=null,e&&(1==u?(t.min=null,t.max=null):t.facets.forEach((e=>{e.min=null,e.max=null}))))}))}r.setData=Vl;let sn,rn,un,an,cn,fn,hn,dn,pn,mn,gn,xn,wn=!1;function _n(){wn||(X(bn),wn=!0)}function bn(){$t&&(function(){let t=K(De,q);for(let e in t){let l=t[e],n=Oe[e];if(null!=n&&null!=n.min)Z(l,n),e==Ae&&on(!0);else if(e!=Ae||2==u)if(0==pl&&null==l.from){let t=l.range(r,null,null,e);l.min=t[0],l.max=t[1]}else l.min=E,l.max=-E}if(pl>0){_e.forEach(((n,i)=>{if(1==u){let o=n.scale,s=t[o],u=Oe[o];if(0==i){let t=s.range(r,s.min,s.max,o);s.min=t[0],s.max=t[1],ml=e(s.min,l[0]),gl=e(s.max,l[0]),s.min>l[0][ml]&&ml++,l[0][gl]>s.max&&gl--,n.min=jl[ml],n.max=jl[gl]}else n.show&&n.auto&&Kl(s,u,n,l[i]);n.idxs[0]=ml,n.idxs[1]=gl}else if(i>0&&n.show&&n.auto){let[e,o]=n.facets,s=e.scale,r=o.scale,[u,a]=l[i];Kl(t[s],Oe[s],e,u),Kl(t[r],Oe[r],o,a),n.min=o.min,n.max=o.max}}));for(let e in t){let l=t[e],n=Oe[e];if(null==l.from&&(null==n||null==n.min)){let t=l.range(r,l.min==E?null:l.min,l.max==-E?null:l.max,e);l.min=t[0],l.max=t[1]}}}for(let e in t){let l=t[e];if(null!=l.from){let n=t[l.from];if(null==n.min)l.min=l.max=null;else{let t=l.range(r,n.min,n.max,e);l.min=t[0],l.max=t[1]}}}let n={},i=!1;for(let e in t){let l=t[e],o=De[e];if(o.min!=l.min||o.max!=l.max){o.min=l.min,o.max=l.max;let t=o.distr;o._min=3==t?y(o.min):4==t?S(o.min,o.asinh):o.min,o._max=3==t?y(o.max):4==t?S(o.max,o.asinh):o.max,n[e]=i=!0}}if(i){_e.forEach(((e,t)=>{2==u?t>0&&n.y&&(e._paths=null):n[e.scale]&&(e._paths=null)}));for(let e in n)Qt=!0,ti("setScale",e);ol.show&&(el=ll=ol.left>=0)}for(let e in Oe)Oe[e]=null}(),$t=!1),Qt&&(function(){let e=!1,t=0;for(;!e;){t++;let l=ln(t),n=nn(t);e=3==t||l&&n,e||(il(r.width,r.height),Xt=!0)}}(),Qt=!1),Xt&&(ve(G,ne,Ct),ve(G,te,Ft),ve(G,Q,Et),ve(G,ee,Dt),ve(O,ne,Ct),ve(O,te,Ft),ve(O,Q,Et),ve(O,ee,Dt),ve(I,Q,Mt),ve(I,ee,St),A.width=x(Mt*we),A.height=x(St*we),Se.forEach((e=>{let{_show:t,_el:l,_size:n,_pos:i,side:o}=e;if(t){let e=o%2==1;ve(l,e?"left":"top",i-(3===o||0===o?n:0)),ve(l,e?"width":"height",n),ve(l,e?"top":"left",e?Ft:Ct),ve(l,e?"height":"width",e?Dt:Et),l&&ke(l,pe)}else l&&be(l,pe)})),wl=bl=Fl=Rl=Ll=Il=Gl=Ol=Hl=null,Nl=1,Vn(!1),ti("setSize"),Xt=!1),Mt>0&&St>0&&(R.clearRect(0,0,A.width,A.height),ti("drawClear"),Fe.forEach((e=>e())),ti("draw")),ol.show&&el&&(jn(null,!0,!1),el=!1),M||(M=!0,r.status=1,ti("ready")),Bl=!1,wn=!1}function kn(t,n){let i=De[t];if(null==i.from){if(0==pl){let e=i.range(r,n.min,n.max,t);n.min=e[0],n.max=e[1]}if(n.min>n.max){let e=n.min;n.min=n.max,n.max=e}if(pl>1&&null!=n.min&&null!=n.max&&1e-16>n.max-n.min)return;t==Ae&&2==i.distr&&pl>0&&(n.min=e(n.min,l[0]),n.max=e(n.max,l[0]),n.min==n.max&&n.max++),Oe[t]=n,$t=!0,_n()}}r.redraw=(e,t)=>{Qt=t||!1,!1!==e?zn(Ae,Re.min,Re.max):_n()},r.setScale=kn;let vn=!1;const yn=ol.drag;let Mn=yn.x,Sn=yn.y;ol.show&&(ol.x&&(sn=Me("u-cursor-x",O)),ol.y&&(rn=Me("u-cursor-y",O)),0==Re.ori?(un=sn,an=rn):(un=rn,an=sn),gn=ol.left,xn=ol.top);const En=r.select=Z({show:!0,over:!0,left:0,width:0,top:0,height:0},t.select),Dn=En.show?Me("u-select",En.over?O:G):null;function Tn(e,t){if(En.show){for(let t in e)ve(Dn,t,En[t]=e[t]);!1!==t&&ti("setSelect")}}function zn(e,t,l){kn(e,{min:t,max:l})}function Pn(e,t,l,n){let i=_e[e];null!=t.focus&&function(e){if(e!=Yn){let t=null==e,l=1!=sl.alpha;_e.forEach(((n,i)=>{let o=t||0==i||i==e;n._focus=t?null:o,l&&function(e,t){_e[e].alpha=t,ol.show&&ul[e]&&(ul[e].style.opacity=t),Xe&&lt[e]&&(lt[e].style.opacity=t)}(i,o?1:sl.alpha)})),Yn=e,l&&_n()}}(e),null!=t.show&&(i.show=t.show,function(e){let t=Xe?lt[e]:null;_e[e].show?t&&ke(t,pe):(t&&be(t,pe),ul.length>1&&Ee(ul[e],-10,-10,Et,Dt))}(e),zn(2==u?i.facets[1].scale:i.scale,null,null),_n()),!1!==l&&ti("setSeries",e,t),n&&oi("setSeries",r,e,t)}let An,Wn,Yn;r.setSelect=Tn,r.setSeries=Pn,r.addBand=function(e,t){e.fill=P(e.fill||null),ze.splice(t=null==t?ze.length:t,0,e)},r.setBand=function(e,t){Z(ze[e],t)},r.delBand=function(e){null==e?ze.length=0:ze.splice(e,1)};const Cn={focus:!0},Fn={focus:!1};function Hn(e,t,l){let n=De[t];l&&(e=e/we-(1==n.ori?Ft:Ct));let i=Et;1==n.ori&&(i=Dt,e=i-e),-1==n.dir&&(e=i-e);let o=n._min,s=o+e/i*(n._max-o),r=n.distr;return 3==r?k(10,s):4==r?((e,t=1)=>d.sinh(e)*t)(s,n.asinh):s}function Rn(e,t){ve(Dn,ne,En.left=e),ve(Dn,Q,En.width=t)}function Ln(e,t){ve(Dn,te,En.top=e),ve(Dn,ee,En.height=t)}Xe&&rl&&Ye(ce,et,(()=>{ol._lock||(Pn(null,Fn,!0,li.setSeries),jn(null,!0,!1))})),r.valToIdx=t=>e(t,l[0]),r.posToIdx=function(t,n){return e(Hn(t,Ae,n),l[0],ml,gl)},r.posToVal=Hn,r.valToPos=(e,t,l)=>0==De[t].ori?h(e,De[t],l?jt:Et,l?Ht:0):g(e,De[t],l?Kt:Dt,l?Rt:0),r.batch=function(e){e(r),_n()},r.setCursor=(e,t,l)=>{gn=e.left,xn=e.top,jn(null,t,l)};let In=0==Re.ori?Rn:Ln,Gn=1==Re.ori?Rn:Ln;function On(e,t){if(null!=e){let t=e.idx;$e.idx=t,_e.forEach(((e,l)=>{(l>0||!it)&&Nn(l,t)}))}Xe&&$e.live&&function(){if(Xe&&$e.live)for(let e=2==u?1:0;_e.length>e;e++){if(0==e&&it)continue;let t=$e.values[e],l=0;for(let n in t)nt[e][l++].firstChild.nodeValue=t[n]}}(),ll=!1,!1!==t&&ti("setLegend")}function Nn(e,t){let n;if(null==t)n=ot;else{let i=_e[e],o=0==e&&2==Le?jl:l[e];n=it?i.values(r,e,t):{_:i.value(r,o[t],e,t)}}$e.values[e]=n}function jn(t,n,i){let o;pn=gn,mn=xn,[gn,xn]=ol.move(r,gn,xn),ol.show&&(un&&Ee(un,x(gn),0,Et,Dt),an&&Ee(an,0,x(xn),Et,Dt)),An=E;let s=0==Re.ori?Et:Dt,a=1==Re.ori?Et:Dt;if(0>gn||0==pl||ml>gl){o=null;for(let e=0;_e.length>e;e++)e>0&&ul.length>1&&Ee(ul[e],-10,-10,Et,Dt);if(rl&&Pn(null,Cn,!0,null==t&&li.setSeries),$e.live){qe.fill(null),ll=!0;for(let e=0;_e.length>e;e++)$e.values[e]=ot}}else{let t,n,i;1==u&&(t=0==Re.ori?gn:xn,n=Hn(t,Ae),o=e(n,l[0],ml,gl),i=H(Ie(l[0][o],Re,s,0),.5));for(let e=2==u?1:0;_e.length>e;e++){let t=_e[e],c=qe[e],f=1==u?l[e][c]:l[e][1][c],h=ol.dataIdx(r,e,o,n),d=1==u?l[e][h]:l[e][1][h];ll=ll||d!=f||h!=c,qe[e]=h;let p=h==o?i:H(Ie(1==u?l[0][h]:l[e][0][h],Re,s,0),.5);if(e>0&&t.show){let l,n,i=null==d?-10:H(Ge(d,1==u?De[t.scale]:De[t.facets[1].scale],a,0),.5);if(i>0&&1==u){let t=m(i-xn);t>An||(An=t,Wn=e)}if(0==Re.ori?(l=p,n=i):(l=i,n=p),ll&&ul.length>1){Te(ul[e],ol.points.fill(r,e),ol.points.stroke(r,e));let t,i,o,s,u=!0,a=ol.points.bbox;if(null!=a){u=!1;let l=a(r,e);o=l.left,s=l.top,t=l.width,i=l.height}else o=l,s=n,t=i=ol.points.size(r,e);Pe(ul[e],t,i,u),Ee(ul[e],o,s,Et,Dt)}}if($e.live){if(!ll||0==e&&it)continue;Nn(e,h)}}}if(ol.idx=o,ol.left=gn,ol.top=xn,ll&&($e.idx=o,On()),En.show&&vn)if(null!=t){let[e,l]=li.scales,[n,i]=li.match,[o,r]=t.cursor.sync.scales,u=t.cursor.drag;Mn=u._x,Sn=u._y;let c,f,h,d,p,{left:g,top:x,width:w,height:b}=t.select,k=t.scales[e].ori,v=t.posToVal,y=null!=e&&n(e,o),M=null!=l&&i(l,r);y&&(0==k?(c=g,f=w):(c=x,f=b),Mn?(h=De[e],d=Ie(v(c,o),h,s,0),p=Ie(v(c+f,o),h,s,0),In(_(d,p),m(p-d))):In(0,s),M||Gn(0,a)),M&&(1==k?(c=g,f=w):(c=x,f=b),Sn?(h=De[l],d=Ge(v(c,r),h,a,0),p=Ge(v(c+f,r),h,a,0),Gn(_(d,p),m(p-d))):Gn(0,a),y||In(0,s))}else{let e=m(pn-cn),t=m(mn-fn);if(1==Re.ori){let l=e;e=t,t=l}Mn=yn.x&&e>=yn.dist,Sn=yn.y&&t>=yn.dist;let l,n,i=yn.uni;null!=i?Mn&&Sn&&(Mn=e>=i,Sn=t>=i,Mn||Sn||(t>e?Sn=!0:Mn=!0)):yn.x&&yn.y&&(Mn||Sn)&&(Mn=Sn=!0),Mn&&(0==Re.ori?(l=hn,n=gn):(l=dn,n=xn),In(_(l,n),m(n-l)),Sn||Gn(0,a)),Sn&&(1==Re.ori?(l=hn,n=gn):(l=dn,n=xn),Gn(_(l,n),m(n-l)),Mn||In(0,s)),Mn||Sn||(In(0,0),Gn(0,0))}if(yn._x=Mn,yn._y=Sn,null==t){if(i){if(null!=ni){let[e,t]=li.scales;li.values[0]=null!=e?Hn(0==Re.ori?gn:xn,e):null,li.values[1]=null!=t?Hn(1==Re.ori?gn:xn,t):null}oi(se,r,gn,xn,Et,Dt,o)}if(rl){let e=i&&li.setSeries,t=sl.prox;null==Yn?An>t||Pn(Wn,Cn,!0,e):An>t?Pn(null,Cn,!0,e):Wn!=Yn&&Pn(Wn,Cn,!0,e)}}M&&!1!==n&&ti("setCursor")}r.setLegend=On;let Bn=null;function Vn(e){!0===e?Bn=null:(Bn=O.getBoundingClientRect(),ti("syncRect",Bn))}function Un(e,t,l,n,i,o){ol._lock||(Jn(e,t,l,n,i,o,0,!1,null!=e),null!=e?jn(null,!0,!0):jn(t,!0,!1))}function Jn(e,t,l,n,i,o,s,u,a){if(null==Bn&&Vn(!1),null!=e)l=e.clientX-Bn.left,n=e.clientY-Bn.top;else{if(0>l||0>n)return gn=-10,void(xn=-10);let[e,s]=li.scales,r=t.cursor.sync,[u,a]=r.values,[c,f]=r.scales,[h,d]=li.match,p=1==t.scales[c].ori,m=0==Re.ori?Et:Dt,g=1==Re.ori?Et:Dt,x=p?o:i,w=p?i:o,_=p?n:l,b=p?l:n;if(l=null!=c?h(e,c)?v(u,De[e],m,0):-10:m*(_/x),n=null!=f?d(s,f)?v(a,De[s],g,0):-10:g*(b/w),1==Re.ori){let e=l;l=n,n=e}}a&&(l>1&&Et-1>l||(l=T(l,Et)),n>1&&Dt-1>n||(n=T(n,Dt))),u?(cn=l,fn=n,[hn,dn]=ol.move(r,l,n)):(gn=l,xn=n)}function qn(){Tn({width:0,height:0},!1)}function Kn(e,t,l,n,i,o){vn=!0,Mn=Sn=yn._x=yn._y=!1,Jn(e,t,l,n,i,o,0,!0,!1),null!=e&&(kt(ue,ge,Zn),oi(re,r,hn,dn,Et,Dt,null))}function Zn(e,t,l,n,i,o){vn=yn._x=yn._y=!1,Jn(e,t,l,n,i,o,0,!1,!0);let{left:s,top:u,width:a,height:c}=En,f=a>0||c>0;if(f&&Tn(En),yn.setScale&&f){let e=s,t=a,l=u,n=c;if(1==Re.ori&&(e=u,t=c,l=s,n=a),Mn&&zn(Ae,Hn(e,Ae),Hn(e+t,Ae)),Sn)for(let e in De){let t=De[e];e!=Ae&&null==t.from&&t.min!=E&&zn(e,Hn(l+n,e),Hn(l,e))}qn()}else ol.lock&&(ol._lock=!ol._lock,ol._lock||jn(null,!0,!1));null!=e&&(vt(ue,ge),oi(ue,r,gn,xn,Et,Dt,null))}function $n(e){Ul(),qn(),null!=e&&oi(fe,r,gn,xn,Et,Dt,null)}function Xn(){Se.forEach(Cl),nl(r.width,r.height,!0)}Ye(de,xe,Xn);const Qn={};Qn.mousedown=Kn,Qn.mousemove=Un,Qn.mouseup=Zn,Qn.dblclick=$n,Qn.setSeries=(e,t,l,n)=>{Pn(l,n,!0,!1)},ol.show&&(kt(re,O,Kn),kt(se,O,Un),kt(ae,O,Vn),kt(ce,O,(function(){if(!ol._lock){let e=vn;if(vn){let e,t,l=!0,n=!0,i=10;0==Re.ori?(e=Mn,t=Sn):(e=Sn,t=Mn),e&&t&&(l=i>=gn||gn>=Et-i,n=i>=xn||xn>=Dt-i),e&&l&&(gn=hn>gn?0:Et),t&&n&&(xn=dn>xn?0:Dt),jn(null,!0,!0),vn=!1}gn=-10,xn=-10,jn(null,!0,!0),e&&(vn=e)}})),kt(fe,O,$n),_l.add(r),r.syncRect=Vn);const ei=r.hooks=t.hooks||{};function ti(e,t,l){e in ei&&ei[e].forEach((e=>{e.call(null,r,t,l)}))}(t.plugins||[]).forEach((e=>{for(let t in e.hooks)ei[t]=(ei[t]||[]).concat(e.hooks[t])}));const li=Z({key:null,setSeries:!1,filters:{pub:C,sub:C},scales:[Ae,_e[1]?_e[1].scale:null],match:[F,F],values:[null,null]},ol.sync);ol.sync=li;const ni=li.key,ii=Zt(ni);function oi(e,t,l,n,i,o,s){li.filters.pub(e,t,l,n,i,o,s)&&ii.pub(e,t,l,n,i,o,s)}function si(){ti("init",t,l),Vl(l||t.data,!1),Oe[Ae]?kn(Ae,Oe[Ae]):Ul(),nl(t.width,t.height),jn(null,!0,!1),Tn(En,!1)}return ii.sub(r),r.pub=function(e,t,l,n,i,o,s){li.filters.sub(e,t,l,n,i,o,s)&&Qn[e](null,t,l,n,i,o,s)},r.destroy=function(){ii.unsub(r),_l.delete(r),bt.clear(),Ce(de,xe,Xn),D.remove(),ti("destroy")},_e.forEach(al),Se.forEach((function(e,t){if(e._show=e.show,e.show){let l=e.side%2,n=De[e.scale];null==n&&(e.scale=l?_e[1].scale:Ae,n=De[e.scale]);let i=n.time;e.size=P(e.size),e.space=P(e.space),e.rotate=P(e.rotate),e.incrs=P(e.incrs||(2==n.distr?Ke:i?1==he?ut:ft:Ze)),e.splits=P(e.splits||(i&&1==n.distr?Ve:3==n.distr?Wt:4==n.distr?Yt:At)),e.stroke=P(e.stroke),e.grid.stroke=P(e.grid.stroke),e.ticks.stroke=P(e.ticks.stroke);let o=e.values;e.values=V(o)&&!V(o[0])?P(o):i?V(o)?mt(Ne,pt(o,Be)):U(o)?function(e,t){let l=je(t);return(t,n)=>n.map((t=>l(e(t))))}(Ne,o):o||Ue:o||Pt,e.filter=P(e.filter||(3>n.distr?W:Lt)),e.font=Yl(e.font),e.labelFont=Yl(e.labelFont),e._size=e.size(r,null,t,0),e._space=e._rotate=e._incrs=e._found=e._splits=e._values=null,e._size>0&&(cl[t]=!0),e._el=Me("u-axis",I)}})),n?n instanceof HTMLElement?(n.appendChild(D),si()):n(r,si):si(),r}Fl.assign=Z,Fl.fmtNum=h,Fl.rangeNum=a,Fl.rangeLog=i,Fl.rangeAsinh=o,Fl.orient=$t,Fl.join=function(e,t){let l=new Set;for(let t=0;e.length>t;t++){let n=e[t][0],i=n.length;for(let e=0;i>e;e++)l.add(n[e])}let n=[Array.from(l).sort(((e,t)=>e-t))],i=n[0].length,o=new Map;for(let e=0;i>e;e++)o.set(n[0][e],e);for(let l=0;e.length>l;l++){let s=e[l],r=s[0];for(let e=1;s.length>e;e++){let u=s[e],a=Array(i).fill(void 0),c=t?t[l][e]:1,f=[];for(let e=0;u.length>e;e++){let t=u[e],l=o.get(r[e]);null===t?0!=c&&(a[l]=t,2==c&&f.push(l)):a[l]=t}$(a,f,i),n.push(a)}}return n},Fl.fmtDate=je,Fl.tzDate=function(e,t){let l;return"UTC"==t||"Etc/UTC"==t?l=new Date(+e+6e4*e.getTimezoneOffset()):t==Be?l=e:(l=new Date(e.toLocaleString("en-US",{timeZone:t})),l.setMilliseconds(e.getMilliseconds())),l},Fl.sync=Zt;{Fl.addGap=el,Fl.clipGaps=Qt;let e=Fl.paths={points:dl};e.linear=xl,e.stepped=function(e){const l=c(e.align,1),n=c(e.ascDesc,!1);return(e,i,o,s)=>$t(e,i,((r,u,a,c,f,h,d,p,m,g,x)=>{let w=r.pxRound,_=0==c.ori?ol:sl;const b={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},k=b.stroke,v=1*c.dir*(0==c.ori?1:-1);o=t(a,o,s,1),s=t(a,o,s,-1);let y=[],M=!1,S=w(d(a[1==v?o:s],f,x,m)),E=w(h(u[1==v?o:s],c,g,p)),D=E;_(k,E,S);for(let e=1==v?o:s;e>=o&&s>=e;e+=v){let t=a[e],n=w(h(u[e],c,g,p));if(null==t){null===t&&(el(y,D,n),M=!0);continue}let i=w(d(t,f,x,m));M&&(el(y,D,n),M=!1),1==l?_(k,n,S):_(k,D,i),_(k,n,i),S=i,D=n}if(null!=r.fill){let t=b.fill=new Path2D(k),l=w(d(r.fillTo(e,i,r.min,r.max),f,x,m));_(t,D,l),_(t,E,l)}b.gaps=y=r.gaps(e,i,o,s,y);let T=r.width*we/2,z=n||1==l?T:-T,P=n||-1==l?-T:T;return y.forEach((e=>{e[0]+=z,e[1]+=P})),r.spanGaps||(b.clip=Qt(y,c.ori,p,m,g,x)),e.bands.length>0&&(b.band=Xt(e,i,o,s,k)),b}))},e.bars=function(e){const t=c((e=e||N).size,[.6,E,1]),l=e.align||0,n=(e.gap||0)*we,i=c(e.radius,0),o=1-t[0],s=c(t[1],E)*we,r=c(t[2],1)*we,u=c(e.disp,N),a=c(e.each,(()=>{})),{fill:f,stroke:h}=u;return(e,t,d,p)=>$t(e,t,((x,w,k,v,y,M,S,E,D,T,z)=>{let P=x.pxRound;const A=v.dir*(0==v.ori?1:-1),W=y.dir*(1==y.ori?1:-1);let Y,C,F=0==v.ori?rl:ul,H=0==v.ori?a:(e,t,l,n,i,o,s)=>{a(e,t,l,i,n,s,o)},R=x.fillTo(e,t,x.min,x.max),L=S(R,y,z,D),I=P(x.width*we),G=!1,O=null,N=null,j=null,B=null;null!=f&&null!=h&&(G=!0,O=f.values(e,t,d,p),N=new Map,new Set(O).forEach((e=>{null!=e&&N.set(e,new Path2D)})),j=h.values(e,t,d,p),B=new Map,new Set(j).forEach((e=>{null!=e&&B.set(e,new Path2D)})));let{x0:V,size:U}=u;if(null!=V&&null!=U){w=V.values(e,t,d,p),2==V.unit&&(w=w.map((t=>e.posToVal(E+t*T,v.key,!0))));let l=U.values(e,t,d,p);C=2==U.unit?l[0]*T:M(l[0],v,T,E)-M(0,v,T,E),C=P(C-I),Y=1==A?-I/2:C+I/2}else{let e=T;if(w.length>1){let t=null;for(let l=0,n=1/0;w.length>l;l++)if(void 0!==k[l]){if(null!=t){let i=m(w[l]-w[t]);n>i&&(n=i,e=m(M(w[l],v,T,E)-M(w[t],v,T,E)))}t=l}}C=P(_(s,b(r,e-e*o))-I-n),Y=(0==l?C/2:l==A?0:C)-l*A*n/2}const J={stroke:null,fill:null,clip:null,band:null,gaps:null,flags:3},q=e.bands.length>0;let K;q&&(J.band=new Path2D,K=P(S(y.max,y,z,D)));const Z=G?null:new Path2D,$=J.band;for(let l=1==A?d:p;l>=d&&p>=l;l+=A){let n=k[l],o=M(2!=v.distr||null!=u?w[l]:l,v,T,E),s=S(c(n,R),y,z,D),r=P(o-Y),a=P(b(s,L)),f=P(_(s,L)),h=a-f,d=i*C;null!=n&&(G?(I>0&&null!=j[l]&&F(B.get(j[l]),r,f+g(I/2),C,b(0,h-I),d),null!=O[l]&&F(N.get(O[l]),r,f+g(I/2),C,b(0,h-I),d)):F(Z,r,f+g(I/2),C,b(0,h-I),d),H(e,t,l,r-I/2,f,C+I,h)),q&&(1==W?(a=f,f=K):(f=a,a=K),h=a-f,F($,r-I/2,f,C+I,b(0,h),0))}return I>0&&(J.stroke=G?B:Z),J.fill=G?N:Z,J}))},e.spline=function(){return function(e){return(l,n,i,o)=>$t(l,n,((s,r,u,a,c,f,h,d,p,m,g)=>{let x,w,_,b=s.pxRound;0==a.ori?(x=nl,_=ol,w=fl):(x=il,_=sl,w=hl);const k=1*a.dir*(0==a.ori?1:-1);i=t(u,i,o,1),o=t(u,i,o,-1);let v=[],y=!1,M=b(f(r[1==k?i:o],a,m,d)),S=M,E=[],D=[];for(let e=1==k?i:o;e>=i&&o>=e;e+=k){let t=u[e],l=f(r[e],a,m,d);null!=t?(y&&(el(v,S,l),y=!1),E.push(S=l),D.push(h(u[e],c,g,p))):null===t&&(el(v,S,l),y=!0)}const T={stroke:e(E,D,x,_,w,b),fill:null,clip:null,band:null,gaps:null,flags:1},z=T.stroke;if(null!=s.fill&&null!=z){let e=T.fill=new Path2D(z),t=b(h(s.fillTo(l,n,s.min,s.max),c,g,p));_(e,S,t),_(e,M,t)}return T.gaps=v=s.gaps(l,n,i,o,v),s.spanGaps||(T.clip=Qt(v,a.ori,d,p,m,g)),l.bands.length>0&&(T.band=Xt(l,n,i,o,z)),T}))}(wl)}}return Fl}();
