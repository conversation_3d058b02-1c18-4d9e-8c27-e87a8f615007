{"version": 3, "file": "lang/summernote-bn-BD.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,kBAJH;AAKJC,QAAAA,MAAM,EAAE,eALJ;AAMJC,QAAAA,IAAI,EAAE,aANF;AAOJC,QAAAA,aAAa,EAAE,UAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,YATT;AAUJC,QAAAA,IAAI,EAAE,aAVF;AAWJC,QAAAA,QAAQ,EAAE;AAXN,OADC;AAcPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,KADF;AAELC,QAAAA,MAAM,EAAE,cAFH;AAGLC,QAAAA,UAAU,EAAE,iBAHP;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,aAAa,EAAE,qBALV;AAMLC,QAAAA,UAAU,EAAE,UANP;AAOLC,QAAAA,SAAS,EAAE,UAPN;AAQLC,QAAAA,UAAU,EAAE,UARP;AASLC,QAAAA,SAAS,EAAE,UATN;AAULC,QAAAA,YAAY,EAAE,gBAVT;AAWLC,QAAAA,WAAW,EAAE,cAXR;AAYLC,QAAAA,cAAc,EAAE,kBAZX;AAaLC,QAAAA,SAAS,EAAE,gBAbN;AAcLC,QAAAA,aAAa,EAAE,6BAdV;AAeLC,QAAAA,SAAS,EAAE,mBAfN;AAgBLC,QAAAA,eAAe,EAAE,yBAhBZ;AAiBLC,QAAAA,eAAe,EAAE,sBAjBZ;AAkBLC,QAAAA,oBAAoB,EAAE,qCAlBjB;AAmBLC,QAAAA,GAAG,EAAE,UAnBA;AAoBLC,QAAAA,MAAM,EAAE,UApBH;AAqBLC,QAAAA,QAAQ,EAAE;AArBL,OAdA;AAqCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,cAFN;AAGLrB,QAAAA,MAAM,EAAE,qBAHH;AAILiB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OArCA;AA4CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJvB,QAAAA,MAAM,EAAE,qBAFJ;AAGJwB,QAAAA,MAAM,EAAE,iBAHJ;AAIJC,QAAAA,IAAI,EAAE,eAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,4BAND;AAOJU,QAAAA,eAAe,EAAE,qBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA5CC;AAsDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,WAAW,EAAE,oBAFR;AAGLC,QAAAA,WAAW,EAAE,oBAHR;AAILC,QAAAA,UAAU,EAAE,oBAJP;AAKLC,QAAAA,WAAW,EAAE,oBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,YAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAtDA;AAgEPC,MAAAA,EAAE,EAAE;AACFrC,QAAAA,MAAM,EAAE;AADN,OAhEG;AAmEPsC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAnEA;AA+EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,kBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA/EA;AAmFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,UAAU,EAAE,aAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAnFF;AAwFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,sBAFA;AAGTC,QAAAA,MAAM,EAAE,cAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,wBALC;AAMTC,QAAAA,KAAK,EAAE,oBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAxFJ;AAiGPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,eADH;AAELC,QAAAA,IAAI,EAAE,QAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,QALR;AAMLC,QAAAA,cAAc,EAAE,sBANX;AAOLC,QAAAA,KAAK,EAAE,iBAPF;AAQLC,QAAAA,cAAc,EAAE,4BARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OAjGA;AA4GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,iBADH;AAERC,QAAAA,KAAK,EAAE,WAFC;AAGRC,QAAAA,cAAc,EAAE,gBAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,qBALb;AAMRC,QAAAA,aAAa,EAAE,WANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA5GH;AAqHP3B,MAAAA,IAAI,EAAE;AACJ,kBAAU,QADN;AAEJ,2BAAmB,mBAFf;AAGJ,gBAAQ,8BAHJ;AAIJ,gBAAQ,uBAJJ;AAKJ,eAAO,OALH;AAMJ,iBAAS,SANL;AAOJ,gBAAQ,mBAPJ;AAQJ,kBAAU,sBARN;AASJ,qBAAa,0BATT;AAUJ,yBAAiB,0BAVb;AAWJ,wBAAgB,eAXZ;AAYJ,uBAAe,0BAZX;AAaJ,yBAAiB,8BAbb;AAcJ,wBAAgB,0BAdZ;AAeJ,uBAAe,0BAfX;AAgBJ,+BAAuB,sBAhBnB;AAiBJ,6BAAqB,qBAjBjB;AAkBJ,mBAAW,wCAlBP;AAmBJ,kBAAU,gCAnBN;AAoBJ,sBAAc,6DApBV;AAqBJ,oBAAY,6CArBR;AAsBJ,oBAAY,6CAtBR;AAuBJ,oBAAY,6CAvBR;AAwBJ,oBAAY,6CAxBR;AAyBJ,oBAAY,6CAzBR;AA0BJ,oBAAY,6CA1BR;AA2BJ,gCAAwB,sBA3BpB;AA4BJ,2BAAmB;AA5Bf,OArHC;AAmJP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,mBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAnJF;AAuJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,aADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAvJN;AADiB,GAA5B;AA8JD,CA/JD,EA+JGC,MA/JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-bn-BD.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'bn-BD': {\n      font: {\n        bold: 'গাঢ়',\n        italic: 'তির্যক',\n        underline: 'নিন্মরেখা',\n        clear: 'ফন্টের শৈলী সরান',\n        height: 'লাইনের উচ্চতা',\n        name: 'ফন্ট পরিবার',\n        strikethrough: 'অবচ্ছেদন',\n        subscript: 'নিম্নলিপি',\n        superscript: 'উর্ধ্বলিপি',\n        size: 'ফন্টের আকার',\n        sizeunit: 'ফন্টের আকারের একক',\n      },\n      image: {\n        image: 'ছবি',\n        insert: 'ছবি যোগ করুন',\n        resizeFull: 'পূর্ণ আকারে নিন',\n        resizeHalf: 'অর্ধ আকারে নিন',\n        resizeQuarter: 'চতুর্থাংশ আকারে নিন',\n        resizeNone: 'আসল আকার',\n        floatLeft: 'বামে নিন',\n        floatRight: 'ডানে নিন',\n        floatNone: 'দিক সরান',\n        shapeRounded: 'আকৃতি: গোলাকার',\n        shapeCircle: 'আকৃতি: বৃত্ত',\n        shapeThumbnail: 'আকৃতি: থাম্বনেইল',\n        shapeNone: 'আকৃতি: কিছু নয়',\n        dragImageHere: 'এখানে ছবি বা লেখা টেনে আনুন',\n        dropImage: 'ছবি বা লেখা ছাড়ুন',\n        selectFromFiles: 'ফাইল থেকে নির্বাচন করুন',\n        maximumFileSize: 'সর্বোচ্চ ফাইলের আকার',\n        maximumFileSizeError: 'সর্বোচ্চ ফাইলের আকার অতিক্রম করেছে।',\n        url: 'ছবির URL',\n        remove: 'ছবি সরান',\n        original: 'আসল',\n      },\n      video: {\n        video: 'ভিডিও',\n        videoLink: 'ভিডিওর লিঙ্ক',\n        insert: 'ভিডিও সন্নিবেশ করুন',\n        url: 'ভিডিওর URL',\n        providers: '(ইউটিউব, গুগল ড্রাইভ, ভিমিও, ভিন, ইনস্টাগ্রাম, ডেইলিমোশন বা ইউকু)',\n      },\n      link: {\n        link: 'লিঙ্ক',\n        insert: 'লিঙ্ক সন্নিবেশ করুন',\n        unlink: 'লিঙ্কমুক্ত করুন',\n        edit: 'সম্পাদনা করুন',\n        textToDisplay: 'দেখানোর জন্য লেখা',\n        url: 'এই লিঙ্কটি কোন URL-এ যাবে?',\n        openInNewWindow: 'নতুন উইন্ডোতে খুলুন',\n        useProtocol: 'পূর্বনির্ধারিত প্রোটোকল ব্যবহার করুন',\n      },\n      table: {\n        table: 'ছক',\n        addRowAbove: 'উপরে সারি যোগ করুন',\n        addRowBelow: 'নিচে সারি যোগ করুন',\n        addColLeft: 'বামে কলাম যোগ করুন',\n        addColRight: 'ডানে কলাম যোগ করুন',\n        delRow: 'সারি মুছুন',\n        delCol: 'কলাম মুছুন',\n        delTable: 'ছক মুছুন',\n      },\n      hr: {\n        insert: 'বিভাজক রেখা সন্নিবেশ করুন',\n      },\n      style: {\n        style: 'শৈলী',\n        p: 'সাধারণ',\n        blockquote: 'উক্তি',\n        pre: 'কোড',\n        h1: 'শীর্ষক ১',\n        h2: 'শীর্ষক ২',\n        h3: 'শীর্ষক ৩',\n        h4: 'শীর্ষক ৪',\n        h5: 'শীর্ষক ৫',\n        h6: 'শীর্ষক ৬',\n      },\n      lists: {\n        unordered: 'অবিন্যস্ত তালিকা',\n        ordered: 'বিন্যস্ত তালিকা',\n      },\n      options: {\n        help: 'সাহায্য',\n        fullscreen: 'পূর্ণ পর্দা',\n        codeview: 'কোড দৃশ্য',\n      },\n      paragraph: {\n        paragraph: 'অনুচ্ছেদ',\n        outdent: 'ঋণাত্মক প্রান্তিককরণ',\n        indent: 'প্রান্তিককরণ',\n        left: 'বামে সারিবদ্ধ করুন',\n        center: 'কেন্দ্রে সারিবদ্ধ করুন',\n        right: 'ডানে সারিবদ্ধ করুন',\n        justify: 'যথাযথ ফাঁক দিয়ে সাজান',\n      },\n      color: {\n        recent: 'সাম্প্রতিক রং',\n        more: 'আরও রং',\n        background: 'পটভূমির রং',\n        foreground: 'লেখার রং',\n        transparent: 'স্বচ্ছ',\n        setTransparent: 'স্বচ্ছ নির্ধারণ করুন',\n        reset: 'পুনঃস্থাপন করুন',\n        resetToDefault: 'পূর্বনির্ধারিত ফিরিয়ে আনুন',\n        cpSelect: 'নির্বাচন করুন',\n      },\n      shortcut: {\n        shortcuts: 'কীবোর্ড শর্টকাট',\n        close: 'বন্ধ করুন',\n        textFormatting: 'লেখার বিন্যাসন',\n        action: 'কার্য',\n        paragraphFormatting: 'অনুচ্ছেদের বিন্যাসন',\n        documentStyle: 'নথির শৈলী',\n        extraKeys: 'অতিরিক্ত কীগুলি',\n      },\n      help: {\n        'escape': 'এস্কেপ',\n        'insertParagraph': 'অনুচ্ছেদ সন্নিবেশ',\n        'undo': 'শেষ কমান্ড পূর্বাবস্থায় ফেরত',\n        'redo': 'শেষ কমান্ড পুনরায় করা',\n        'tab': 'ট্যাব',\n        'untab': 'অ-ট্যাব',\n        'bold': 'গাঢ় শৈলী নির্ধারণ',\n        'italic': 'তির্যক শৈলী নির্ধারণ',\n        'underline': 'নিম্নরেখার শৈলী নির্ধারণ',\n        'strikethrough': 'অবচ্ছেদনের শৈলী নির্ধারণ',\n        'removeFormat': 'শৈলী পরিষ্কার',\n        'justifyLeft': 'বামের সারিবন্ধন নির্ধারণ',\n        'justifyCenter': 'কেন্দ্রের সারিবন্ধন নির্ধারণ',\n        'justifyRight': 'ডানের সারিবন্ধন নির্ধারণ',\n        'justifyFull': 'পূর্ণ সারিবন্ধন নির্ধারণ',\n        'insertUnorderedList': 'অবিন্যস্ত তালিকা টগল',\n        'insertOrderedList': 'বিন্যস্ত তালিকা টগল',\n        'outdent': 'বর্তমান অনুচ্ছেদে ঋণাত্মক প্রান্তিককরণ',\n        'indent': 'বর্তমান অনুচ্ছেদে প্রান্তিককরণ',\n        'formatPara': 'বর্তমান ব্লকের বিন্যাসটি অনুচ্ছেদ হিসেবে পরিবর্তন (P ট্যাগ)',\n        'formatH1': 'বর্তমান ব্লকের বিন্যাসটি H1 হিসেবে পরিবর্তন',\n        'formatH2': 'বর্তমান ব্লকের বিন্যাসটি H2 হিসেবে পরিবর্তন',\n        'formatH3': 'বর্তমান ব্লকের বিন্যাসটি H3 হিসেবে পরিবর্তন',\n        'formatH4': 'বর্তমান ব্লকের বিন্যাসটি H4 হিসেবে পরিবর্তন',\n        'formatH5': 'বর্তমান ব্লকের বিন্যাসটি H5 হিসেবে পরিবর্তন',\n        'formatH6': 'বর্তমান ব্লকের বিন্যাসটি H6 হিসেবে পরিবর্তন',\n        'insertHorizontalRule': 'বিভাজক রেখা সন্নিবেশ',\n        'linkDialog.show': 'লিংক ডায়ালগ প্রদর্শন',\n      },\n      history: {\n        undo: 'পূর্বাবস্থায় আনুন',\n        redo: 'পুনঃকরুন',\n      },\n      specialChar: {\n        specialChar: 'বিশেষ অক্ষর',\n        select: 'বিশেষ অক্ষর নির্বাচন করুন',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}