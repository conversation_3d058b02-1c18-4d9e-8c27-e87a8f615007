{% extends 'z115_app/base.html' %}
{% load static %}  <!-- Thêm dòng này ở đầu file -->

{% block title %}VẬT TƯ ĐÃ KIỂM{% endblock %}

{% block content %}
    <div class="container mt-4">
        <h2>VẬT TƯ ĐÃ KIỂM</h2>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
        {% endif %}

        <form method="post" action="">
            {% csrf_token %}
            <table class="table table-striped table-hover">
                <thead style="background-color: #0066cc; color: white;">
                    <tr>
                        <th style="padding: 10px;">Ngày</th>
                        <th style="padding: 10px;">Tên quy cách</th>
                        <th style="padding: 10px;">Dùng vào việc</th>
                        <th style="padding: 10px;">Đvt</th>
                        <th style="padding: 10px;"><PERSON><PERSON> lượng</th>
                        <th style="padding: 10px;"><PERSON><PERSON><PERSON><PERSON> b<PERSON>o kiểm</th>
                        <th style="padding: 10px;">Nơi để vật tư</th>
                        <th style="padding: 10px;">Số hợp cách</th>
                        <th style="padding: 10px;">Kết quả kiểm tra</th>
                        <th style="padding: 10px;">Ngày trả kết quả</th>
                        <th style="padding: 10px;">ACTION</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in page_obj %}
                        <tr>
                            <input type="hidden" name="item_id" value="{{ item.id }}">
                            <td style="padding: 10px;">{{ item.ngay|date:'d/m/Y' }}</td>
                            <td style="padding: 10px;">{{ item.ten_quy_cach }}</td>
                            <td style="padding: 10px;">{{ item.dung_vao_viec }}</td>
                            <td style="padding: 10px;">{{ item.don_vi_tinh }}</td>
                            <td style="padding: 10px;">{{ item.so_luong|floatformat:0 }}</td>
                            <td style="padding: 10px;">{{ item.nguoi_bao_kiem }}</td>
                            <td style="padding: 10px;">{{ item.noi_de_vat_tu }}</td>
                            <td style="padding: 10px;">
                                <input type="number" name="so_hop_cach_{{ item.id }}" value="{{ item.so_hop_cach|default:0 }}" min="0" class="form-control" readonly>
                            </td>
                            <td style="padding: 10px;">
                                <textarea name="ket_qua_kiem_tra_{{ item.id }}" class="form-control auto-expand" rows="1" readonly>{{ item.ket_qua_kiem_tra|default:'' }}</textarea>
                            </td>
                            <td style="padding: 10px;">
                                <input type="date" name="ngay_tra_ket_qua_{{ item.id }}" value="{{ item.ngay_tra_ket_qua|date:'Y-m-d'|default:'' }}" class="form-control" readonly>
                            </td>
                            <td style="padding: 10px;">
                                {% if user.username == 'PHONGB12' %}
                                    <button type="submit" name="edit_item" value="{{ item.id }}" class="btn btn-warning btn-sm">SỬA</button>
                                    <button type="submit" name="save_item" value="{{ item.id }}" class="btn btn-success btn-sm" style="display: none;">LƯU</button>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% if user.username == 'PHONGB12' %}
                <div class="text-center mt-4">
                    <a href="{% url 'home' %}" class="btn btn-secondary">QUAY LẠI</a>
                </div>
            {% endif %}
        </form>

        {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Trước</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Trước</span></li>
                    {% endif %}
                    <li class="page-item disabled"><span class="page-link">Trang {{ page_obj.number }} của {{ page_obj.paginator.num_pages }}</span></li>
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Tiếp</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    </div>

    <style>
        .auto-expand {
            min-height: 38px;
            overflow: hidden;
            resize: none;
        }
        .auto-expand:focus {
            outline: none;
        }
    </style>

    <script src="{% static 'z115_app/js/jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'z115_app/js/bootstrap.bundle.min.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('.auto-expand');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });

            const editButtons = document.querySelectorAll('button[name="edit_item"]');
            const saveButtons = document.querySelectorAll('button[name="save_item"]');
            editButtons.forEach((button, index) => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const row = this.closest('tr');
                    const inputs = row.querySelectorAll('input, textarea');
                    inputs.forEach(input => input.removeAttribute('readonly'));
                    this.style.display = 'none';
                    saveButtons[index].style.display = 'inline-block';
                });
            });
        });
    </script>
{% endblock %}