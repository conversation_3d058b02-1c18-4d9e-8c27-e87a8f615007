# views/auth_views.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.forms import AuthenticationForm
from django.contrib import messages
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def login_view(request):
    if request.method == "POST":
        username = request.POST['username']
        password = request.POST['password']
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            user._perm_cache = None
            logger.debug(f"User {username} logged in successfully, redirecting to home")
            return redirect('home')
        else:
            logger.debug(f"Login failed for {username}")
            messages.error(request, "Tên đăng nhập hoặc mật khẩu không đúng!")
    return render(request, 'z115_app/login.html', {'form': AuthenticationForm()})

def logout_view(request):
    logout(request)
    return redirect('login')

@login_required
def home(request):
    logger.debug(f"User accessing home: {request.user.username}")
    if request.user.username == 'PHONGB12':
        return render(request, 'z115_app/phongb12_home.html', {'username': request.user.username})
    return render(request, 'z115_app/home.html', {'username': request.user.username})