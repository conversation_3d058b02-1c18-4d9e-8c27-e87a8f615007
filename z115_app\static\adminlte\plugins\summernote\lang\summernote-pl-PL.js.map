{"version": 3, "file": "lang/summernote-pl-PL.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,aADF;AAEJC,QAAAA,MAAM,EAAE,YAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,UANF;AAOJC,QAAAA,aAAa,EAAE,eAPX;AAQJC,QAAAA,SAAS,EAAE,cARP;AASJC,QAAAA,WAAW,EAAE,cATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,MAAM,EAAE,eAFH;AAGLC,QAAAA,UAAU,EAAE,uBAHP;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,aAAa,EAAE,sBALV;AAMLC,QAAAA,SAAS,EAAE,UANN;AAOLC,QAAAA,UAAU,EAAE,WAPP;AAQLC,QAAAA,SAAS,EAAE,iBARN;AASLC,QAAAA,YAAY,EAAE,sBATT;AAULC,QAAAA,WAAW,EAAE,gBAVR;AAWLC,QAAAA,cAAc,EAAE,oBAXX;AAYLC,QAAAA,SAAS,EAAE,eAZN;AAaLC,QAAAA,aAAa,EAAE,qCAbV;AAcLC,QAAAA,SAAS,EAAE,+BAdN;AAeLC,QAAAA,eAAe,EAAE,iBAfZ;AAgBLC,QAAAA,eAAe,EAAE,uBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,qCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,mBAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,aAFN;AAGLpB,QAAAA,MAAM,EAAE,aAHH;AAILgB,QAAAA,GAAG,EAAE,aAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,UADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,eAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,uBALX;AAMJT,QAAAA,GAAG,EAAE,oDAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,sBAFR;AAGLC,QAAAA,WAAW,EAAE,sBAHR;AAILC,QAAAA,UAAU,EAAE,wBAJP;AAKLC,QAAAA,WAAW,EAAE,yBALR;AAMLC,QAAAA,MAAM,EAAE,aANH;AAOLC,QAAAA,MAAM,EAAE,cAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,KAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,YALC;AAMLC,QAAAA,EAAE,EAAE,YANC;AAOLC,QAAAA,EAAE,EAAE,YAPC;AAQLC,QAAAA,EAAE,EAAE,YARC;AASLC,QAAAA,EAAE,EAAE,YATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,oBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,aAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,QADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,mBAJG;AAKTC,QAAAA,MAAM,EAAE,oBALC;AAMTC,QAAAA,KAAK,EAAE,oBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,cADH;AAELC,QAAAA,IAAI,EAAE,gBAFD;AAGLC,QAAAA,UAAU,EAAE,KAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,eALR;AAMLC,QAAAA,cAAc,EAAE,eANX;AAOLC,QAAAA,KAAK,EAAE,UAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,qBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,sBALb;AAMRC,QAAAA,aAAa,EAAE,gBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,gBADf;AAEJ,gBAAQ,4BAFJ;AAGJ,gBAAQ,8BAHJ;AAIJ,eAAO,WAJH;AAKJ,iBAAS,gBALL;AAMJ,gBAAQ,aANJ;AAOJ,kBAAU,SAPN;AAQJ,qBAAa,cART;AASJ,yBAAiB,eATb;AAUJ,wBAAgB,mBAVZ;AAWJ,uBAAe,mBAXX;AAYJ,yBAAiB,oBAZb;AAaJ,wBAAgB,oBAbZ;AAcJ,uBAAe,cAdX;AAeJ,+BAAuB,qBAfnB;AAgBJ,6BAAqB,oBAhBjB;AAiBJ,mBAAW,4BAjBP;AAkBJ,kBAAU,2BAlBN;AAmBJ,sBAAc,yCAnBV;AAoBJ,oBAAY,2BApBR;AAqBJ,oBAAY,2BArBR;AAsBJ,oBAAY,2BAtBR;AAuBJ,oBAAY,2BAvBR;AAwBJ,oBAAY,2BAxBR;AAyBJ,oBAAY,2BAzBR;AA0BJ,gCAAwB,qBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,iBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pl-PL.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'pl-PL': {\n      font: {\n        bold: 'Pogrubienie',\n        italic: 'Pochy<PERSON>ie',\n        underline: 'Podkreślenie',\n        clear: '<PERSON><PERSON><PERSON> formatowanie',\n        height: '<PERSON>linia',\n        name: '<PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Prz<PERSON>reślenie',\n        subscript: 'Indeks dolny',\n        superscript: 'Indeks górny',\n        size: 'Rozmiar',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Wstaw grafikę',\n        resizeFull: '<PERSON>mie<PERSON> rozmiar na 100%',\n        resizeHalf: 'Zmień rozmiar na 50%',\n        resizeQuarter: 'Zmień rozmiar na 25%',\n        floatLeft: 'Po lewej',\n        floatRight: 'Po prawej',\n        floatNone: 'Równo z tekstem',\n        shapeRounded: 'Kształt: zaokrąglone',\n        shapeCircle: 'Kształt: okrąg',\n        shapeThumbnail: 'Kształt: miniatura',\n        shapeNone: 'Kształt: brak',\n        dragImageHere: 'Przec<PERSON><PERSON><PERSON>j grafikę lub tekst tutaj',\n        dropImage: 'Przeciągnij grafikę lub tekst',\n        selectFromFiles: 'Wybierz z dysku',\n        maximumFileSize: 'Limit wielkości pliku',\n        maximumFileSizeError: 'Przekroczono limit wielkości pliku.',\n        url: 'Adres URL grafiki',\n        remove: 'Usuń grafikę',\n        original: 'Oryginał',\n      },\n      video: {\n        video: 'Wideo',\n        videoLink: 'Adres wideo',\n        insert: 'Wstaw wideo',\n        url: 'Adres wideo',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion lub Youku)',\n      },\n      link: {\n        link: 'Odnośnik',\n        insert: 'Wstaw odnośnik',\n        unlink: 'Usuń odnośnik',\n        edit: 'Edytuj',\n        textToDisplay: 'Tekst do wyświetlenia',\n        url: 'Na jaki adres URL powinien przenosić ten odnośnik?',\n        openInNewWindow: 'Otwórz w nowym oknie',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Dodaj wiersz powyżej',\n        addRowBelow: 'Dodaj wiersz poniżej',\n        addColLeft: 'Dodaj kolumnę po lewej',\n        addColRight: 'Dodaj kolumnę po prawej',\n        delRow: 'Usuń wiersz',\n        delCol: 'Usuń kolumnę',\n        delTable: 'Usuń tabelę',\n      },\n      hr: {\n        insert: 'Wstaw poziomą linię',\n      },\n      style: {\n        style: 'Styl',\n        p: 'pny',\n        blockquote: 'Cytat',\n        pre: 'Kod',\n        h1: 'Nagłówek 1',\n        h2: 'Nagłówek 2',\n        h3: 'Nagłówek 3',\n        h4: 'Nagłówek 4',\n        h5: 'Nagłówek 5',\n        h6: 'Nagłówek 6',\n      },\n      lists: {\n        unordered: 'Lista wypunktowana',\n        ordered: 'Lista numerowana',\n      },\n      options: {\n        help: 'Pomoc',\n        fullscreen: 'Pełny ekran',\n        codeview: 'Źródło',\n      },\n      paragraph: {\n        paragraph: 'Akapit',\n        outdent: 'Zmniejsz wcięcie',\n        indent: 'Zwiększ wcięcie',\n        left: 'Wyrównaj do lewej',\n        center: 'Wyrównaj do środka',\n        right: 'Wyrównaj do prawej',\n        justify: 'Wyrównaj do lewej i prawej',\n      },\n      color: {\n        recent: 'Ostani kolor',\n        more: 'Więcej kolorów',\n        background: 'Tło',\n        foreground: 'Czcionka',\n        transparent: 'Przeźroczysty',\n        setTransparent: 'Przeźroczyste',\n        reset: 'Zresetuj',\n        resetToDefault: 'Domyślne',\n      },\n      shortcut: {\n        shortcuts: 'Skróty klawiaturowe',\n        close: 'Zamknij',\n        textFormatting: 'Formatowanie tekstu',\n        action: 'Akcja',\n        paragraphFormatting: 'Formatowanie akapitu',\n        documentStyle: 'Styl dokumentu',\n        extraKeys: 'Dodatkowe klawisze',\n      },\n      help: {\n        'insertParagraph': 'Wstaw paragraf',\n        'undo': 'Cofnij poprzednią operację',\n        'redo': 'Przywróć poprzednią operację',\n        'tab': 'Tabulacja',\n        'untab': 'Usuń tabulację',\n        'bold': 'Pogrubienie',\n        'italic': 'Kursywa',\n        'underline': 'Podkreślenie',\n        'strikethrough': 'Przekreślenie',\n        'removeFormat': 'Usuń formatowanie',\n        'justifyLeft': 'Wyrównaj do lewej',\n        'justifyCenter': 'Wyrównaj do środka',\n        'justifyRight': 'Wyrównaj do prawej',\n        'justifyFull': 'Justyfikacja',\n        'insertUnorderedList': 'Nienumerowana lista',\n        'insertOrderedList': 'Wypunktowana lista',\n        'outdent': 'Zmniejsz wcięcie paragrafu',\n        'indent': 'Zwiększ wcięcie paragrafu',\n        'formatPara': 'Zamień format bloku na paragraf (tag P)',\n        'formatH1': 'Zamień format bloku na H1',\n        'formatH2': 'Zamień format bloku na H2',\n        'formatH3': 'Zamień format bloku na H3',\n        'formatH4': 'Zamień format bloku na H4',\n        'formatH5': 'Zamień format bloku na H5',\n        'formatH6': 'Zamień format bloku na H6',\n        'insertHorizontalRule': 'Wstaw poziomą linię',\n        'linkDialog.show': 'Pokaż dialog linkowania',\n      },\n      history: {\n        undo: 'Cofnij',\n        redo: 'Ponów',\n      },\n      specialChar: {\n        specialChar: 'ZNAKI SPECJALNE',\n        select: 'Wybierz Znak specjalny',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}