{"version": 3, "file": "lang/summernote-hu-HU.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,UADF;AAEJC,QAAAA,MAAM,EAAE,MAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,kBAJH;AAKJC,QAAAA,MAAM,EAAE,QALJ;AAMJC,QAAAA,IAAI,EAAE,WANF;AAOJC,QAAAA,aAAa,EAAE,UAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,KADF;AAELC,QAAAA,MAAM,EAAE,eAFH;AAGLC,QAAAA,UAAU,EAAE,4BAHP;AAILC,QAAAA,UAAU,EAAE,oBAJP;AAKLC,QAAAA,aAAa,EAAE,uBALV;AAMLC,QAAAA,SAAS,EAAE,gBANN;AAOLC,QAAAA,UAAU,EAAE,iBAPP;AAQLC,QAAAA,SAAS,EAAE,kBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,gCAbV;AAcLC,QAAAA,SAAS,EAAE,kCAdN;AAeLC,QAAAA,eAAe,EAAE,qBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,aAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLpB,QAAAA,MAAM,EAAE,iBAHH;AAILgB,QAAAA,GAAG,EAAE,gBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,YADF;AAEJtB,QAAAA,MAAM,EAAE,sBAFJ;AAGJuB,QAAAA,MAAM,EAAE,0BAHJ;AAIJC,QAAAA,IAAI,EAAE,aAJF;AAKJC,QAAAA,aAAa,EAAE,uBALX;AAMJT,QAAAA,GAAG,EAAE,+BAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,kBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,iBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,qBAFA;AAGTC,QAAAA,MAAM,EAAE,kBAHC;AAITC,QAAAA,IAAI,EAAE,gBAJG;AAKTC,QAAAA,MAAM,EAAE,kBALC;AAMTC,QAAAA,KAAK,EAAE,iBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,gBAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,uBANX;AAOLC,QAAAA,KAAK,EAAE,eAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,gBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,SAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,kBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,aADf;AAEJ,gBAAQ,aAFJ;AAGJ,gBAAQ,MAHJ;AAIJ,eAAO,kBAJH;AAKJ,iBAAS,qBALL;AAMJ,gBAAQ,oBANJ;AAOJ,kBAAU,gBAPN;AAQJ,qBAAa,UART;AASJ,yBAAiB,SATb;AAUJ,wBAAgB,kBAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,WAdX;AAeJ,+BAAuB,yBAfnB;AAgBJ,6BAAqB,uBAhBjB;AAiBJ,mBAAW,8CAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sCAnBV;AAoBJ,oBAAY,gCApBR;AAqBJ,oBAAY,gCArBR;AAsBJ,oBAAY,gCAtBR;AAuBJ,oBAAY,gCAvBR;AAwBJ,oBAAY,gCAxBR;AAyBJ,oBAAY,gCAzBR;AA0BJ,gCAAwB,4BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,aADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-hu-HU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'hu-HU': {\n      font: {\n        bold: 'Félkövér',\n        italic: 'Dőlt',\n        underline: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        clear: '<PERSON><PERSON>z<PERSON> törlése',\n        height: '<PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Betűméret',\n      },\n      image: {\n        image: 'Kép',\n        insert: 'Kép beszúrása',\n        resizeFull: 'Átméretezés teljes méretre',\n        resizeHalf: 'Átméretezés felére',\n        resizeQuarter: 'Átméretezés negyedére',\n        floatLeft: 'Igazítás balra',\n        floatRight: 'Igazítás jobbra',\n        floatNone: 'Igazítás törlése',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Ide húzhat képet vagy szöveget',\n        dropImage: 'Engedje el a képet vagy szöveget',\n        selectFromFiles: 'Fájlok kiválasztása',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Kép URL címe',\n        remove: 'Kép törlése',\n        original: 'Original',\n      },\n      video: {\n        video: 'Videó',\n        videoLink: 'Videó hivatkozás',\n        insert: 'Videó beszúrása',\n        url: 'Videó URL címe',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion vagy Youku)',\n      },\n      link: {\n        link: 'Hivatkozás',\n        insert: 'Hivatkozás beszúrása',\n        unlink: 'Hivatkozás megszüntetése',\n        edit: 'Szerkesztés',\n        textToDisplay: 'Megjelenítendő szöveg',\n        url: 'Milyen URL címre hivatkozzon?',\n        openInNewWindow: 'Megnyitás új ablakban',\n      },\n      table: {\n        table: 'Táblázat',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Elválasztó vonal beszúrása',\n      },\n      style: {\n        style: 'Stílus',\n        p: 'Normál',\n        blockquote: 'Idézet',\n        pre: 'Kód',\n        h1: 'Fejléc 1',\n        h2: 'Fejléc 2',\n        h3: 'Fejléc 3',\n        h4: 'Fejléc 4',\n        h5: 'Fejléc 5',\n        h6: 'Fejléc 6',\n      },\n      lists: {\n        unordered: 'Listajeles lista',\n        ordered: 'Számozott lista',\n      },\n      options: {\n        help: 'Súgó',\n        fullscreen: 'Teljes képernyő',\n        codeview: 'Kód nézet',\n      },\n      paragraph: {\n        paragraph: 'Bekezdés',\n        outdent: 'Behúzás csökkentése',\n        indent: 'Behúzás növelése',\n        left: 'Igazítás balra',\n        center: 'Igazítás középre',\n        right: 'Igazítás jobbra',\n        justify: 'Sorkizárt',\n      },\n      color: {\n        recent: 'Jelenlegi szín',\n        more: 'További színek',\n        background: 'Háttérszín',\n        foreground: 'Betűszín',\n        transparent: 'Átlátszó',\n        setTransparent: 'Átlászóság beállítása',\n        reset: 'Visszaállítás',\n        resetToDefault: 'Alaphelyzetbe állítás',\n      },\n      shortcut: {\n        shortcuts: 'Gyorsbillentyű',\n        close: 'Bezárás',\n        textFormatting: 'Szöveg formázása',\n        action: 'Művelet',\n        paragraphFormatting: 'Bekezdés formázása',\n        documentStyle: 'Dokumentumstílus',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Új bekezdés',\n        'undo': 'Visszavonás',\n        'redo': 'Újra',\n        'tab': 'Behúzás növelése',\n        'untab': 'Behúzás csökkentése',\n        'bold': 'Félkövérre állítás',\n        'italic': 'Dőltre állítás',\n        'underline': 'Aláhúzás',\n        'strikethrough': 'Áthúzás',\n        'removeFormat': 'Formázás törlése',\n        'justifyLeft': 'Balra igazítás',\n        'justifyCenter': 'Középre igazítás',\n        'justifyRight': 'Jobbra igazítás',\n        'justifyFull': 'Sorkizárt',\n        'insertUnorderedList': 'Számozatlan lista be/ki',\n        'insertOrderedList': 'Számozott lista be/ki',\n        'outdent': 'Jelenlegi bekezdés behúzásának megszüntetése',\n        'indent': 'Jelenlegi bekezdés behúzása',\n        'formatPara': 'Blokk formázása bekezdésként (P tag)',\n        'formatH1': 'Blokk formázása, mint Fejléc 1',\n        'formatH2': 'Blokk formázása, mint Fejléc 2',\n        'formatH3': 'Blokk formázása, mint Fejléc 3',\n        'formatH4': 'Blokk formázása, mint Fejléc 4',\n        'formatH5': 'Blokk formázása, mint Fejléc 5',\n        'formatH6': 'Blokk formázása, mint Fejléc 6',\n        'insertHorizontalRule': 'Vízszintes vonal beszúrása',\n        'linkDialog.show': 'Link párbeszédablak megjelenítése',\n      },\n      history: {\n        undo: 'Visszavonás',\n        redo: 'Újra',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}