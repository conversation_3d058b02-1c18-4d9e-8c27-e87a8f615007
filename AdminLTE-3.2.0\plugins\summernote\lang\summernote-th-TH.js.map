{"version": 3, "file": "lang/summernote-th-TH.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,QADF;AAEJC,QAAAA,MAAM,EAAE,UAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,oBAJH;AAKJC,QAAAA,MAAM,EAAE,eALJ;AAMJC,QAAAA,IAAI,EAAE,aANF;AAOJC,QAAAA,aAAa,EAAE,QAPX;AAQJC,QAAAA,SAAS,EAAE,SARP;AASJC,QAAAA,WAAW,EAAE,OATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,YAFH;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,aAAa,EAAE,gBALV;AAMLC,QAAAA,SAAS,EAAE,SANN;AAOLC,QAAAA,UAAU,EAAE,QAPP;AAQLC,QAAAA,SAAS,EAAE,eARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,8BAbV;AAcLC,QAAAA,SAAS,EAAE,sBAdN;AAeLC,QAAAA,eAAe,EAAE,iBAfZ;AAgBLC,QAAAA,eAAe,EAAE,iBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,sBAjBjB;AAkBLC,QAAAA,GAAG,EAAE,uBAlBA;AAmBLC,QAAAA,MAAM,EAAE,UAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,SAAS,EAAE,gBAFN;AAGLpB,QAAAA,MAAM,EAAE,YAHH;AAILgB,QAAAA,GAAG,EAAE,uBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,cADF;AAEJtB,QAAAA,MAAM,EAAE,kBAFJ;AAGJuB,QAAAA,MAAM,EAAE,oBAHJ;AAIJC,QAAAA,IAAI,EAAE,OAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,6CAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,gBAFR;AAGLC,QAAAA,WAAW,EAAE,kBAHR;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,WAAW,EAAE,qBALR;AAMLC,QAAAA,MAAM,EAAE,OANH;AAOLC,QAAAA,MAAM,EAAE,WAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,MAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,qBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,UAAU,EAAE,gBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,SADF;AAETC,QAAAA,OAAO,EAAE,YAFA;AAGTC,QAAAA,MAAM,EAAE,WAHC;AAITC,QAAAA,IAAI,EAAE,gBAJG;AAKTC,QAAAA,MAAM,EAAE,iBALC;AAMTC,QAAAA,KAAK,EAAE,eANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,SAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,qBANX;AAOLC,QAAAA,KAAK,EAAE,QAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,SADH;AAERC,QAAAA,KAAK,EAAE,KAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,qBALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,UANJ;AAOJ,kBAAU,YAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,mCApBR;AAqBJ,oBAAY,mCArBR;AAsBJ,oBAAY,mCAtBR;AAuBJ,oBAAY,mCAvBR;AAwBJ,oBAAY,mCAxBR;AAyBJ,oBAAY,mCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,gBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-th-TH.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'th-TH': {\n      font: {\n        bold: 'ตัวหนา',\n        italic: 'ตัวเอียง',\n        underline: 'ขีดเส้นใต้',\n        clear: 'ล้างรูปแบบตัวอักษร',\n        height: 'ความสูงบรรทัด',\n        name: 'แบบตัวอักษร',\n        strikethrough: 'ขีดฆ่า',\n        subscript: 'ตัวห้อย',\n        superscript: 'ตัวยก',\n        size: 'ขนาดตัวอักษร',\n      },\n      image: {\n        image: 'รูปภาพ',\n        insert: 'แทรกรูปภาพ',\n        resizeFull: 'ปรับขนาดเท่าจริง',\n        resizeHalf: 'ปรับขนาดลง 50%',\n        resizeQuarter: 'ปรับขนาดลง 25%',\n        floatLeft: 'ชิดซ้าย',\n        floatRight: 'ชิดขวา',\n        floatNone: 'ไม่จัดตำแหน่ง',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'ลากรูปภาพที่ต้องการไว้ที่นี่',\n        dropImage: 'วางรูปภาพหรือข้อความ',\n        selectFromFiles: 'เลือกไฟล์รูปภาพ',\n        maximumFileSize: 'ขนาดไฟล์ใหญ่สุด',\n        maximumFileSizeError: 'ไฟล์เกินขนาดที่กำหนด',\n        url: 'ที่อยู่ URL ของรูปภาพ',\n        remove: 'ลบรูปภาพ',\n        original: 'Original',\n      },\n      video: {\n        video: 'วีดีโอ',\n        videoLink: 'ลิงก์ของวีดีโอ',\n        insert: 'แทรกวีดีโอ',\n        url: 'ที่อยู่ URL ของวีดีโอ',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion หรือ Youku)',\n      },\n      link: {\n        link: 'ตัวเชื่อมโยง',\n        insert: 'แทรกตัวเชื่อมโยง',\n        unlink: 'ยกเลิกตัวเชื่อมโยง',\n        edit: 'แก้ไข',\n        textToDisplay: 'ข้อความที่ให้แสดง',\n        url: 'ที่อยู่เว็บไซต์ที่ต้องการให้เชื่อมโยงไปถึง?',\n        openInNewWindow: 'เปิดในหน้าต่างใหม่',\n      },\n      table: {\n        table: 'ตาราง',\n        addRowAbove: 'เพิ่มแถวด้านบน',\n        addRowBelow: 'เพิ่มแถวด้านล่าง',\n        addColLeft: 'เพิ่มคอลัมน์ด้านซ้าย',\n        addColRight: 'เพิ่มคอลัมน์ด้านขวา',\n        delRow: 'ลบแถว',\n        delCol: 'ลบคอลัมน์',\n        delTable: 'ลบตาราง',\n      },\n      hr: {\n        insert: 'แทรกเส้นคั่น',\n      },\n      style: {\n        style: 'รูปแบบ',\n        p: 'ปกติ',\n        blockquote: 'ข้อความ',\n        pre: 'โค้ด',\n        h1: 'หัวข้อ 1',\n        h2: 'หัวข้อ 2',\n        h3: 'หัวข้อ 3',\n        h4: 'หัวข้อ 4',\n        h5: 'หัวข้อ 5',\n        h6: 'หัวข้อ 6',\n      },\n      lists: {\n        unordered: 'รายการแบบไม่มีลำดับ',\n        ordered: 'รายการแบบมีลำดับ',\n      },\n      options: {\n        help: 'ช่วยเหลือ',\n        fullscreen: 'ขยายเต็มหน้าจอ',\n        codeview: 'ซอร์สโค้ด',\n      },\n      paragraph: {\n        paragraph: 'ย่อหน้า',\n        outdent: 'เยื้องซ้าย',\n        indent: 'เยื้องขวา',\n        left: 'จัดหน้าชิดซ้าย',\n        center: 'จัดหน้ากึ่งกลาง',\n        right: 'จัดหน้าชิดขวา',\n        justify: 'จัดบรรทัดเสมอกัน',\n      },\n      color: {\n        recent: 'สีที่ใช้ล่าสุด',\n        more: 'สีอื่นๆ',\n        background: 'สีพื้นหลัง',\n        foreground: 'สีพื้นหน้า',\n        transparent: 'โปร่งแสง',\n        setTransparent: 'ตั้งค่าความโปร่งแสง',\n        reset: 'คืนค่า',\n        resetToDefault: 'คืนค่ามาตรฐาน',\n      },\n      shortcut: {\n        shortcuts: 'แป้นลัด',\n        close: 'ปิด',\n        textFormatting: 'การจัดรูปแบบข้อความ',\n        action: 'การกระทำ',\n        paragraphFormatting: 'การจัดรูปแบบย่อหน้า',\n        documentStyle: 'รูปแบบของเอกสาร',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'ทำตัวหนา',\n        'italic': 'ทำตัวเอียง',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H1',\n        'formatH2': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H2',\n        'formatH3': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H3',\n        'formatH4': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H4',\n        'formatH5': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H5',\n        'formatH6': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'เปิดหน้าแก้ไข Link',\n      },\n      history: {\n        undo: 'ยกเลิกการกระทำ',\n        redo: 'ทำซ้ำการกระทำ',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}