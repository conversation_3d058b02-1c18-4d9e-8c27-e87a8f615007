# views/nhap_kho_views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def nhap_kho_thanh_pham_khu_a(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_nhap_kho_thanh_pham_khu_a'):
        logger.debug(f"Access denied for {request.user.username} to nhap_kho_thanh_pham_khu_a.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")
    return render(request, 'z115_app/nhap_kho_thanh_pham_khu_a.html')